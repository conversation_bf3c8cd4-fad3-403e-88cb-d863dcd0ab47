# 多模型智能选择器

import json
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import re

@dataclass
class TaskRequirement:
    """任务需求"""
    task_type: str  # code_generation, debugging, refactoring, documentation
    complexity: str  # simple, medium, complex
    language: str
    domain: str  # web, desktop, data_science, etc.
    context_length: int
    quality_priority: str  # speed, quality, cost
    budget_limit: float = 0.0
    response_time_limit: int = 30  # seconds

@dataclass
class ModelCapability:
    """模型能力评估"""
    provider: str
    model: str
    code_quality: float  # 0-1
    speed: float  # requests per minute
    cost_per_1k_tokens: float
    context_length: int
    language_support: List[str]
    specialties: List[str]  # web, data_science, algorithms, etc.
    reliability: float  # 0-1
    availability: float  # 0-1

class TaskType(Enum):
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    OPTIMIZATION = "optimization"
    ARCHITECTURE = "architecture"

class ModelSelector:
    """多模型智能选择器"""
    
    def __init__(self):
        self.model_capabilities = self._init_model_capabilities()
        self.usage_history = {}
        self.performance_metrics = {}
        self.cost_tracking = {}
        
    def _init_model_capabilities(self) -> Dict[str, ModelCapability]:
        """初始化模型能力数据"""
        return {
            # 国外模型
            "OpenAI GPT-4": ModelCapability(
                provider="OpenAI GPT-4",
                model="gpt-4-turbo-preview",
                code_quality=0.95,
                speed=20,
                cost_per_1k_tokens=0.03,
                context_length=128000,
                language_support=["python", "javascript", "java", "cpp", "go", "rust"],
                specialties=["algorithms", "web", "desktop", "architecture"],
                reliability=0.98,
                availability=0.99
            ),
            
            "Anthropic Claude": ModelCapability(
                provider="Anthropic Claude",
                model="claude-3-opus",
                code_quality=0.93,
                speed=15,
                cost_per_1k_tokens=0.015,
                context_length=200000,
                language_support=["python", "javascript", "java", "cpp", "go"],
                specialties=["reasoning", "analysis", "documentation"],
                reliability=0.97,
                availability=0.98
            ),
            
            "Google Gemini": ModelCapability(
                provider="Google Gemini",
                model="gemini-pro",
                code_quality=0.88,
                speed=30,
                cost_per_1k_tokens=0.001,
                context_length=32000,
                language_support=["python", "javascript", "java", "cpp"],
                specialties=["web", "mobile", "multimodal"],
                reliability=0.95,
                availability=0.97
            ),
            
            "DeepSeek": ModelCapability(
                provider="DeepSeek",
                model="deepseek-coder",
                code_quality=0.92,
                speed=25,
                cost_per_1k_tokens=0.001,
                context_length=16000,
                language_support=["python", "javascript", "java", "cpp", "go", "rust"],
                specialties=["code_generation", "algorithms", "optimization"],
                reliability=0.94,
                availability=0.96
            ),
            
            # 国内模型
            "百度文心一言": ModelCapability(
                provider="百度文心一言",
                model="ernie-bot-4",
                code_quality=0.85,
                speed=35,
                cost_per_1k_tokens=0.008,
                context_length=8000,
                language_support=["python", "javascript", "java"],
                specialties=["chinese", "web", "data_science"],
                reliability=0.92,
                availability=0.95
            ),
            
            "阿里通义千问": ModelCapability(
                provider="阿里通义千问",
                model="qwen-max",
                code_quality=0.87,
                speed=30,
                cost_per_1k_tokens=0.008,
                context_length=32000,
                language_support=["python", "javascript", "java", "go"],
                specialties=["chinese", "web", "cloud"],
                reliability=0.93,
                availability=0.96
            ),
            
            "智谱清言": ModelCapability(
                provider="智谱清言",
                model="glm-4",
                code_quality=0.86,
                speed=28,
                cost_per_1k_tokens=0.01,
                context_length=128000,
                language_support=["python", "javascript", "java"],
                specialties=["chinese", "multimodal", "tools"],
                reliability=0.91,
                availability=0.94
            ),
            
            "月之暗面": ModelCapability(
                provider="月之暗面",
                model="moonshot-v1-32k",
                code_quality=0.89,
                speed=22,
                cost_per_1k_tokens=0.012,
                context_length=200000,
                language_support=["python", "javascript", "java"],
                specialties=["long_context", "reasoning", "chinese"],
                reliability=0.93,
                availability=0.95
            )
        }
    
    def select_best_model(self, requirement: TaskRequirement, 
                         available_models: List[str] = None) -> Tuple[str, float, str]:
        """选择最佳模型"""
        if available_models is None:
            available_models = list(self.model_capabilities.keys())
        
        # 过滤可用模型
        candidates = {k: v for k, v in self.model_capabilities.items() 
                     if k in available_models}
        
        if not candidates:
            return None, 0.0, "没有可用的模型"
        
        # 计算每个模型的得分
        scores = {}
        for provider, capability in candidates.items():
            score = self._calculate_model_score(requirement, capability)
            scores[provider] = score
        
        # 选择得分最高的模型
        best_model = max(scores, key=scores.get)
        best_score = scores[best_model]
        
        # 生成选择理由
        reason = self._generate_selection_reason(requirement, 
                                               self.model_capabilities[best_model], 
                                               best_score)
        
        return best_model, best_score, reason
    
    def _calculate_model_score(self, requirement: TaskRequirement, 
                              capability: ModelCapability) -> float:
        """计算模型得分"""
        score = 0.0
        
        # 基础能力得分 (40%)
        base_score = capability.code_quality * 0.4
        
        # 任务适配得分 (25%)
        task_score = self._calculate_task_fitness(requirement, capability) * 0.25
        
        # 性能得分 (20%)
        performance_score = self._calculate_performance_score(requirement, capability) * 0.2
        
        # 成本得分 (15%)
        cost_score = self._calculate_cost_score(requirement, capability) * 0.15
        
        total_score = base_score + task_score + performance_score + cost_score
        
        # 应用可靠性和可用性调整
        total_score *= capability.reliability * capability.availability
        
        return total_score
    
    def _calculate_task_fitness(self, requirement: TaskRequirement, 
                               capability: ModelCapability) -> float:
        """计算任务适配度"""
        fitness = 0.0
        
        # 语言支持
        if requirement.language in capability.language_support:
            fitness += 0.3
        
        # 专业领域匹配
        domain_match = any(specialty in requirement.domain.lower() 
                          for specialty in capability.specialties)
        if domain_match:
            fitness += 0.3
        
        # 任务类型匹配
        task_specialties = {
            TaskType.CODE_GENERATION: ["code_generation", "algorithms"],
            TaskType.DEBUGGING: ["debugging", "analysis"],
            TaskType.DOCUMENTATION: ["documentation", "reasoning"],
            TaskType.REFACTORING: ["optimization", "refactoring"],
            TaskType.ARCHITECTURE: ["architecture", "design"]
        }
        
        task_type = TaskType(requirement.task_type)
        if task_type in task_specialties:
            relevant_specialties = task_specialties[task_type]
            if any(spec in capability.specialties for spec in relevant_specialties):
                fitness += 0.2
        
        # 上下文长度适配
        if capability.context_length >= requirement.context_length:
            fitness += 0.2
        else:
            # 上下文长度不足的惩罚
            ratio = capability.context_length / requirement.context_length
            fitness += 0.2 * ratio
        
        return min(fitness, 1.0)
    
    def _calculate_performance_score(self, requirement: TaskRequirement, 
                                   capability: ModelCapability) -> float:
        """计算性能得分"""
        if requirement.quality_priority == "speed":
            # 速度优先
            max_speed = max(cap.speed for cap in self.model_capabilities.values())
            speed_score = capability.speed / max_speed
            
            # 响应时间检查
            estimated_time = 60 / capability.speed  # 估算响应时间
            if estimated_time <= requirement.response_time_limit:
                time_bonus = 0.2
            else:
                time_bonus = 0.0
            
            return speed_score * 0.8 + time_bonus
        
        elif requirement.quality_priority == "quality":
            # 质量优先
            return capability.code_quality
        
        else:  # balanced
            # 平衡模式
            max_speed = max(cap.speed for cap in self.model_capabilities.values())
            speed_score = capability.speed / max_speed
            quality_score = capability.code_quality
            
            return (speed_score + quality_score) / 2
    
    def _calculate_cost_score(self, requirement: TaskRequirement, 
                            capability: ModelCapability) -> float:
        """计算成本得分"""
        if requirement.quality_priority == "cost":
            # 成本优先
            min_cost = min(cap.cost_per_1k_tokens for cap in self.model_capabilities.values())
            cost_score = min_cost / capability.cost_per_1k_tokens
            
            # 预算检查
            estimated_cost = capability.cost_per_1k_tokens * (requirement.context_length / 1000)
            if requirement.budget_limit > 0 and estimated_cost <= requirement.budget_limit:
                budget_bonus = 0.2
            else:
                budget_bonus = 0.0
            
            return cost_score * 0.8 + budget_bonus
        
        else:
            # 非成本优先时，给低成本模型适当加分
            max_cost = max(cap.cost_per_1k_tokens for cap in self.model_capabilities.values())
            cost_efficiency = (max_cost - capability.cost_per_1k_tokens) / max_cost
            return cost_efficiency * 0.5
    
    def _generate_selection_reason(self, requirement: TaskRequirement, 
                                 capability: ModelCapability, score: float) -> str:
        """生成选择理由"""
        reasons = []
        
        # 基础能力
        if capability.code_quality >= 0.9:
            reasons.append("代码质量优秀")
        elif capability.code_quality >= 0.85:
            reasons.append("代码质量良好")
        
        # 专业匹配
        domain_match = any(specialty in requirement.domain.lower() 
                          for specialty in capability.specialties)
        if domain_match:
            matching_specialties = [s for s in capability.specialties 
                                  if s in requirement.domain.lower()]
            reasons.append(f"专业领域匹配: {', '.join(matching_specialties)}")
        
        # 性能特点
        if requirement.quality_priority == "speed" and capability.speed >= 25:
            reasons.append("响应速度快")
        elif requirement.quality_priority == "cost" and capability.cost_per_1k_tokens <= 0.01:
            reasons.append("成本效益高")
        elif requirement.quality_priority == "quality" and capability.code_quality >= 0.9:
            reasons.append("质量表现优异")
        
        # 上下文支持
        if capability.context_length >= requirement.context_length:
            reasons.append("支持所需的上下文长度")
        
        # 语言支持
        if requirement.language in capability.language_support:
            reasons.append(f"原生支持{requirement.language}")
        
        # 可靠性
        if capability.reliability >= 0.95:
            reasons.append("高可靠性")
        
        reason_text = "; ".join(reasons) if reasons else "综合评估最佳"
        return f"选择理由: {reason_text} (得分: {score:.3f})"
    
    def get_model_recommendations(self, requirement: TaskRequirement, 
                                top_k: int = 3) -> List[Tuple[str, float, str]]:
        """获取模型推荐列表"""
        recommendations = []
        
        for provider, capability in self.model_capabilities.items():
            score = self._calculate_model_score(requirement, capability)
            reason = self._generate_selection_reason(requirement, capability, score)
            recommendations.append((provider, score, reason))
        
        # 按得分排序
        recommendations.sort(key=lambda x: x[1], reverse=True)
        
        return recommendations[:top_k]
    
    def update_performance_metrics(self, provider: str, metrics: Dict[str, Any]):
        """更新模型性能指标"""
        if provider not in self.performance_metrics:
            self.performance_metrics[provider] = {
                'total_requests': 0,
                'successful_requests': 0,
                'average_response_time': 0.0,
                'average_quality_rating': 0.0,
                'total_cost': 0.0
            }
        
        current = self.performance_metrics[provider]
        
        # 更新统计信息
        current['total_requests'] += 1
        if metrics.get('success', False):
            current['successful_requests'] += 1
        
        # 更新平均响应时间
        if 'response_time' in metrics:
            current['average_response_time'] = (
                (current['average_response_time'] * (current['total_requests'] - 1) + 
                 metrics['response_time']) / current['total_requests']
            )
        
        # 更新质量评分
        if 'quality_rating' in metrics:
            current['average_quality_rating'] = (
                (current['average_quality_rating'] * (current['total_requests'] - 1) + 
                 metrics['quality_rating']) / current['total_requests']
            )
        
        # 更新成本
        if 'cost' in metrics:
            current['total_cost'] += metrics['cost']
        
        # 动态调整模型能力评估
        self._adjust_model_capability(provider, current)
    
    def _adjust_model_capability(self, provider: str, metrics: Dict[str, Any]):
        """根据实际表现调整模型能力评估"""
        if provider not in self.model_capabilities:
            return
        
        capability = self.model_capabilities[provider]
        
        # 调整可靠性
        if metrics['total_requests'] >= 10:
            success_rate = metrics['successful_requests'] / metrics['total_requests']
            capability.reliability = (capability.reliability * 0.7 + success_rate * 0.3)
        
        # 调整速度评估
        if metrics['average_response_time'] > 0:
            # 根据实际响应时间调整速度评估
            estimated_speed = 60 / metrics['average_response_time']
            capability.speed = (capability.speed * 0.8 + estimated_speed * 0.2)
        
        # 调整质量评估
        if metrics['average_quality_rating'] > 0:
            capability.code_quality = (
                capability.code_quality * 0.8 + 
                metrics['average_quality_rating'] * 0.2
            )
    
    def get_cost_analysis(self, requirement: TaskRequirement, 
                         models: List[str] = None) -> Dict[str, Dict]:
        """获取成本分析"""
        if models is None:
            models = list(self.model_capabilities.keys())
        
        analysis = {}
        
        for model in models:
            if model not in self.model_capabilities:
                continue
            
            capability = self.model_capabilities[model]
            
            # 估算成本
            estimated_tokens = requirement.context_length
            estimated_cost = capability.cost_per_1k_tokens * (estimated_tokens / 1000)
            
            # 估算时间
            estimated_time = 60 / capability.speed
            
            analysis[model] = {
                'estimated_cost': estimated_cost,
                'estimated_time': estimated_time,
                'cost_per_minute': estimated_cost / (estimated_time / 60),
                'quality_score': capability.code_quality,
                'cost_efficiency': capability.code_quality / estimated_cost if estimated_cost > 0 else 0
            }
        
        return analysis
    
    def suggest_model_combination(self, requirement: TaskRequirement) -> Dict[str, Any]:
        """建议模型组合策略"""
        suggestions = {
            'primary_model': None,
            'fallback_models': [],
            'strategy': '',
            'reasoning': ''
        }
        
        # 选择主要模型
        primary, score, reason = self.select_best_model(requirement)
        suggestions['primary_model'] = primary
        
        # 选择备用模型
        recommendations = self.get_model_recommendations(requirement, top_k=5)
        fallback_candidates = [r[0] for r in recommendations[1:3]]  # 取第2、3名
        suggestions['fallback_models'] = fallback_candidates
        
        # 确定策略
        if requirement.quality_priority == "cost":
            suggestions['strategy'] = "成本优化策略"
            suggestions['reasoning'] = "优先使用低成本模型，质量要求不高的任务可降级使用"
        elif requirement.quality_priority == "speed":
            suggestions['strategy'] = "速度优化策略"
            suggestions['reasoning'] = "优先使用高速模型，超时时自动切换到更快的备用模型"
        elif requirement.quality_priority == "quality":
            suggestions['strategy'] = "质量优先策略"
            suggestions['reasoning'] = "优先使用高质量模型，失败时使用次优质量模型重试"
        else:
            suggestions['strategy'] = "平衡策略"
            suggestions['reasoning'] = "在质量、速度、成本之间寻求平衡"
        
        return suggestions
    
    def export_model_comparison(self, requirement: TaskRequirement) -> str:
        """导出模型对比报告"""
        recommendations = self.get_model_recommendations(requirement, top_k=10)
        cost_analysis = self.get_cost_analysis(requirement)
        
        report = []
        report.append("# 模型选择分析报告\\n")
        
        # 任务需求
        report.append("## 任务需求")
        report.append(f"- 任务类型: {requirement.task_type}")
        report.append(f"- 复杂度: {requirement.complexity}")
        report.append(f"- 编程语言: {requirement.language}")
        report.append(f"- 应用领域: {requirement.domain}")
        report.append(f"- 优先级: {requirement.quality_priority}")
        report.append("")
        
        # 推荐排名
        report.append("## 模型推荐排名")
        for i, (model, score, reason) in enumerate(recommendations, 1):
            capability = self.model_capabilities[model]
            cost_info = cost_analysis.get(model, {})
            
            report.append(f"### {i}. {model}")
            report.append(f"- 综合得分: {score:.3f}")
            report.append(f"- 代码质量: {capability.code_quality:.2f}")
            report.append(f"- 响应速度: {capability.speed} req/min")
            report.append(f"- 成本: ${capability.cost_per_1k_tokens:.4f}/1K tokens")
            report.append(f"- 预估成本: ${cost_info.get('estimated_cost', 0):.4f}")
            report.append(f"- 预估时间: {cost_info.get('estimated_time', 0):.1f}秒")
            report.append(f"- {reason}")
            report.append("")
        
        # 成本效益分析
        report.append("## 成本效益分析")
        sorted_by_efficiency = sorted(cost_analysis.items(), 
                                    key=lambda x: x[1].get('cost_efficiency', 0), 
                                    reverse=True)
        
        for model, analysis in sorted_by_efficiency[:5]:
            report.append(f"- {model}: 效益比 {analysis.get('cost_efficiency', 0):.2f}")
        
        return "\\n".join(report)

class ModelPerformanceTracker:
    """模型性能跟踪器"""
    
    def __init__(self):
        self.performance_data = {}
        self.quality_feedback = {}
    
    def record_usage(self, provider: str, request_data: Dict, response_data: Dict):
        """记录使用情况"""
        if provider not in self.performance_data:
            self.performance_data[provider] = []
        
        usage_record = {
            'timestamp': time.time(),
            'request_tokens': request_data.get('tokens', 0),
            'response_tokens': response_data.get('tokens', 0),
            'response_time': response_data.get('response_time', 0),
            'success': response_data.get('success', False),
            'error': response_data.get('error', ''),
            'cost': response_data.get('cost', 0)
        }
        
        self.performance_data[provider].append(usage_record)
    
    def record_quality_feedback(self, provider: str, request_id: str, 
                               quality_rating: float, feedback: str = ""):
        """记录质量反馈"""
        if provider not in self.quality_feedback:
            self.quality_feedback[provider] = []
        
        feedback_record = {
            'request_id': request_id,
            'timestamp': time.time(),
            'quality_rating': quality_rating,  # 1-5 scale
            'feedback': feedback
        }
        
        self.quality_feedback[provider].append(feedback_record)
    
    def get_performance_summary(self, provider: str, days: int = 30) -> Dict:
        """获取性能摘要"""
        if provider not in self.performance_data:
            return {}
        
        cutoff_time = time.time() - (days * 24 * 3600)
        recent_data = [d for d in self.performance_data[provider] 
                      if d['timestamp'] > cutoff_time]
        
        if not recent_data:
            return {}
        
        total_requests = len(recent_data)
        successful_requests = sum(1 for d in recent_data if d['success'])
        total_cost = sum(d['cost'] for d in recent_data)
        avg_response_time = sum(d['response_time'] for d in recent_data) / total_requests
        
        # 质量评分
        quality_data = [f for f in self.quality_feedback.get(provider, []) 
                       if f['timestamp'] > cutoff_time]
        avg_quality = (sum(f['quality_rating'] for f in quality_data) / len(quality_data) 
                      if quality_data else 0)
        
        return {
            'total_requests': total_requests,
            'success_rate': successful_requests / total_requests,
            'total_cost': total_cost,
            'average_cost_per_request': total_cost / total_requests,
            'average_response_time': avg_response_time,
            'average_quality_rating': avg_quality,
            'requests_per_day': total_requests / days
        }