#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
统一管理应用程序的所有配置
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import logging
from datetime import datetime

@dataclass
class AppConfig:
    """应用程序配置"""
    # 基本信息
    app_name: str = "AI代码生成器"
    app_version: str = "2.0.0"
    app_author: str = "AI Code Team"
    
    # 界面配置
    theme: str = "dark"
    language: str = "zh_CN"
    window_width: int = 1400
    window_height: int = 900
    window_maximized: bool = False
    
    # 功能配置
    auto_save: bool = True
    auto_save_interval: int = 300  # 秒
    max_history: int = 100
    enable_performance_monitor: bool = True
    enable_error_tracking: bool = True
    
    # API配置
    default_api_provider: str = "openai"
    api_timeout: int = 30
    max_retries: int = 3
    enable_api_cache: bool = True
    cache_duration: int = 3600  # 秒
    
    # 代码生成配置
    default_language: str = "python"
    default_complexity: str = "medium"
    include_tests: bool = True
    include_docs: bool = True
    include_comments: bool = True
    
    # 打包配置
    default_output_dir: str = "dist"
    clean_build_dir: bool = True
    compress_output: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_file_size: int = 10  # MB
    log_backup_count: int = 5

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.app_config_file = self.config_dir / "app_config.json"
        self.api_config_file = self.config_dir / "api_configs.json"
        self.user_config_file = self.config_dir / "user_config.json"
        
        self.app_config = AppConfig()
        self.api_configs: Dict[str, Any] = {}
        self.user_configs: Dict[str, Any] = {}
        
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.load_all_configs()
    
    def load_all_configs(self):
        """加载所有配置"""
        self.load_app_config()
        self.load_api_configs()
        self.load_user_configs()
    
    def load_app_config(self) -> bool:
        """加载应用配置"""
        try:
            if self.app_config_file.exists():
                with open(self.app_config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置对象
                for key, value in config_data.items():
                    if hasattr(self.app_config, key):
                        setattr(self.app_config, key, value)
                
                self.logger.info("应用配置加载成功")
                return True
            else:
                # 创建默认配置
                self.save_app_config()
                self.logger.info("创建默认应用配置")
                return True
        except Exception as e:
            self.logger.error(f"加载应用配置失败: {e}")
            return False
    
    def save_app_config(self) -> bool:
        """保存应用配置"""
        try:
            config_data = asdict(self.app_config)
            with open(self.app_config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info("应用配置保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存应用配置失败: {e}")
            return False
    
    def load_api_configs(self) -> bool:
        """加载API配置"""
        try:
            # 优先从config目录加载
            if self.api_config_file.exists():
                config_file = self.api_config_file
            elif Path("api_configs.json").exists():
                config_file = Path("api_configs.json")
            else:
                # 创建默认配置
                self.create_default_api_configs()
                return True
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self.api_configs = json.load(f)
            
            # 如果从根目录加载，复制到config目录
            if config_file != self.api_config_file:
                self.save_api_configs()
            
            self.logger.info("API配置加载成功")
            return True
        except Exception as e:
            self.logger.error(f"加载API配置失败: {e}")
            return False
    
    def save_api_configs(self) -> bool:
        """保存API配置"""
        try:
            with open(self.api_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.api_configs, f, indent=2, ensure_ascii=False)
            
            self.logger.info("API配置保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存API配置失败: {e}")
            return False
    
    def create_default_api_configs(self):
        """创建默认API配置"""
        self.api_configs = {
            "openai": {
                "api_key": "",
                "base_url": "https://api.openai.com/v1",
                "model": "gpt-3.5-turbo",
                "max_tokens": 4096,
                "temperature": 0.7,
                "enabled": False
            },
            "anthropic": {
                "api_key": "",
                "base_url": "https://api.anthropic.com",
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 4096,
                "temperature": 0.7,
                "enabled": False
            },
            "google": {
                "api_key": "",
                "base_url": "https://generativelanguage.googleapis.com/v1beta",
                "model": "gemini-pro",
                "max_tokens": 4096,
                "temperature": 0.7,
                "enabled": False
            }
        }
        self.save_api_configs()
    
    def load_user_configs(self) -> bool:
        """加载用户配置"""
        try:
            if self.user_config_file.exists():
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    self.user_configs = json.load(f)
            else:
                self.user_configs = {}
                self.save_user_configs()
            
            self.logger.info("用户配置加载成功")
            return True
        except Exception as e:
            self.logger.error(f"加载用户配置失败: {e}")
            return False
    
    def save_user_configs(self) -> bool:
        """保存用户配置"""
        try:
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_configs, f, indent=2, ensure_ascii=False)
            
            self.logger.info("用户配置保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存用户配置失败: {e}")
            return False
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        # 支持点号分隔的键路径
        keys = key.split('.')
        
        # 首先尝试从应用配置获取
        if hasattr(self.app_config, keys[0]):
            value = getattr(self.app_config, keys[0])
            for k in keys[1:]:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        
        # 然后尝试从用户配置获取
        value = self.user_configs
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_config(self, key: str, value: Any) -> bool:
        """设置配置值"""
        keys = key.split('.')
        
        # 如果是应用配置
        if hasattr(self.app_config, keys[0]):
            if len(keys) == 1:
                setattr(self.app_config, keys[0], value)
                return self.save_app_config()
            else:
                # 复杂路径暂不支持
                return False
        
        # 用户配置
        config = self.user_configs
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        return self.save_user_configs()
    
    def get_api_config(self, provider: str) -> Optional[Dict[str, Any]]:
        """获取API配置"""
        return self.api_configs.get(provider)
    
    def set_api_config(self, provider: str, config: Dict[str, Any]) -> bool:
        """设置API配置"""
        self.api_configs[provider] = config
        return self.save_api_configs()
    
    def get_enabled_api_providers(self) -> List[str]:
        """获取启用的API提供商"""
        enabled = []
        for provider, config in self.api_configs.items():
            if config.get("enabled", False) and config.get("api_key"):
                enabled.append(provider)
        return enabled
    
    def backup_configs(self, backup_dir: str = "config_backup") -> bool:
        """备份配置"""
        try:
            backup_path = Path(backup_dir)
            backup_path.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_subdir = backup_path / f"backup_{timestamp}"
            backup_subdir.mkdir(exist_ok=True)
            
            # 备份所有配置文件
            for config_file in self.config_dir.glob("*.json"):
                shutil.copy2(config_file, backup_subdir)
            
            self.logger.info(f"配置备份成功: {backup_subdir}")
            return True
        except Exception as e:
            self.logger.error(f"配置备份失败: {e}")
            return False
    
    def restore_configs(self, backup_path: str) -> bool:
        """恢复配置"""
        try:
            backup_dir = Path(backup_path)
            if not backup_dir.exists():
                self.logger.error(f"备份目录不存在: {backup_path}")
                return False
            
            # 恢复配置文件
            for backup_file in backup_dir.glob("*.json"):
                target_file = self.config_dir / backup_file.name
                shutil.copy2(backup_file, target_file)
            
            # 重新加载配置
            self.load_all_configs()
            
            self.logger.info(f"配置恢复成功: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"配置恢复失败: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            # 重置应用配置
            self.app_config = AppConfig()
            self.save_app_config()
            
            # 重置API配置
            self.create_default_api_configs()
            
            # 清空用户配置
            self.user_configs = {}
            self.save_user_configs()
            
            self.logger.info("配置已重置为默认值")
            return True
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
            return False
    
    def validate_configs(self) -> Dict[str, List[str]]:
        """验证配置"""
        issues = {
            "errors": [],
            "warnings": []
        }
        
        # 验证API配置
        for provider, config in self.api_configs.items():
            if config.get("enabled", False):
                if not config.get("api_key"):
                    issues["errors"].append(f"{provider}: API密钥未设置")
                
                if not config.get("base_url"):
                    issues["errors"].append(f"{provider}: 基础URL未设置")
                
                if config.get("max_tokens", 0) <= 0:
                    issues["warnings"].append(f"{provider}: max_tokens设置可能不合理")
        
        # 验证应用配置
        if self.app_config.window_width < 800:
            issues["warnings"].append("窗口宽度可能过小")
        
        if self.app_config.window_height < 600:
            issues["warnings"].append("窗口高度可能过小")
        
        if self.app_config.api_timeout < 5:
            issues["warnings"].append("API超时时间可能过短")
        
        return issues
    
    def export_configs(self, export_path: str) -> bool:
        """导出配置"""
        try:
            export_data = {
                "app_config": asdict(self.app_config),
                "api_configs": self.api_configs,
                "user_configs": self.user_configs,
                "export_time": datetime.now().isoformat(),
                "version": self.app_config.app_version
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置导出成功: {export_path}")
            return True
        except Exception as e:
            self.logger.error(f"配置导出失败: {e}")
            return False
    
    def import_configs(self, import_path: str) -> bool:
        """导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 验证导入数据
            if "app_config" not in import_data:
                self.logger.error("导入文件格式无效")
                return False
            
            # 导入应用配置
            app_config_data = import_data["app_config"]
            for key, value in app_config_data.items():
                if hasattr(self.app_config, key):
                    setattr(self.app_config, key, value)
            
            # 导入API配置
            if "api_configs" in import_data:
                self.api_configs = import_data["api_configs"]
            
            # 导入用户配置
            if "user_configs" in import_data:
                self.user_configs = import_data["user_configs"]
            
            # 保存所有配置
            self.save_app_config()
            self.save_api_configs()
            self.save_user_configs()
            
            self.logger.info(f"配置导入成功: {import_path}")
            return True
        except Exception as e:
            self.logger.error(f"配置导入失败: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()
