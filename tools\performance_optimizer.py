#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化和监控模块
提供性能监控、分析和优化建议
"""

import time
import psutil
import threading
import asyncio
import functools
import traceback
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json
import os
from datetime import datetime, timedelta

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    response_time: Optional[float] = None
    error_count: int = 0

@dataclass
class FunctionMetrics:
    """函数性能指标"""
    name: str
    call_count: int = 0
    total_time: float = 0.0
    avg_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    error_count: int = 0
    last_called: Optional[float] = None

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, sample_interval: float = 1.0, max_samples: int = 1000):
        self.sample_interval = sample_interval
        self.max_samples = max_samples
        self.metrics_history: deque = deque(maxlen=max_samples)
        self.function_metrics: Dict[str, FunctionMetrics] = {}
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.logger = logging.getLogger(__name__)
        
        # 初始化基准值
        self.baseline_metrics = self._get_current_metrics()
        
    def start_monitoring(self):
        """开始监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=2.0)
            self.logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._get_current_metrics()
                self.metrics_history.append(metrics)
                time.sleep(self.sample_interval)
            except Exception as e:
                self.logger.error(f"监控过程中出错: {e}")
                time.sleep(self.sample_interval)
    
    def _get_current_metrics(self) -> PerformanceMetrics:
        """获取当前性能指标"""
        # CPU和内存
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        # 磁盘IO
        disk_io = psutil.disk_io_counters()
        disk_read_mb = disk_io.read_bytes / (1024 * 1024) if disk_io else 0
        disk_write_mb = disk_io.write_bytes / (1024 * 1024) if disk_io else 0
        
        # 网络IO
        network_io = psutil.net_io_counters()
        network_sent_mb = network_io.bytes_sent / (1024 * 1024) if network_io else 0
        network_recv_mb = network_io.bytes_recv / (1024 * 1024) if network_io else 0
        
        # 线程数
        active_threads = threading.active_count()
        
        return PerformanceMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            active_threads=active_threads
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"error": "没有性能数据"}
        
        recent_metrics = list(self.metrics_history)[-60:]  # 最近60个样本
        
        # 计算平均值
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory_mb = sum(m.memory_used_mb for m in recent_metrics) / len(recent_metrics)
        
        # 计算峰值
        max_cpu = max(m.cpu_percent for m in recent_metrics)
        max_memory = max(m.memory_percent for m in recent_metrics)
        max_memory_mb = max(m.memory_used_mb for m in recent_metrics)
        
        # 函数性能统计
        top_functions = sorted(
            self.function_metrics.values(),
            key=lambda x: x.total_time,
            reverse=True
        )[:10]
        
        return {
            "system_metrics": {
                "avg_cpu_percent": round(avg_cpu, 2),
                "max_cpu_percent": round(max_cpu, 2),
                "avg_memory_percent": round(avg_memory, 2),
                "max_memory_percent": round(max_memory, 2),
                "avg_memory_mb": round(avg_memory_mb, 2),
                "max_memory_mb": round(max_memory_mb, 2),
                "active_threads": recent_metrics[-1].active_threads if recent_metrics else 0
            },
            "function_metrics": [
                {
                    "name": f.name,
                    "call_count": f.call_count,
                    "total_time": round(f.total_time, 4),
                    "avg_time": round(f.avg_time, 4),
                    "max_time": round(f.max_time, 4),
                    "error_count": f.error_count
                }
                for f in top_functions
            ],
            "sample_count": len(recent_metrics),
            "monitoring_duration": round(time.time() - recent_metrics[0].timestamp, 2) if recent_metrics else 0
        }
    
    def performance_decorator(self, func_name: Optional[str] = None):
        """性能监控装饰器"""
        def decorator(func: Callable) -> Callable:
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    self._record_function_call(name, time.time() - start_time, False)
                    return result
                except Exception as e:
                    self._record_function_call(name, time.time() - start_time, True)
                    raise
            
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    self._record_function_call(name, time.time() - start_time, False)
                    return result
                except Exception as e:
                    self._record_function_call(name, time.time() - start_time, True)
                    raise
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def _record_function_call(self, func_name: str, execution_time: float, had_error: bool):
        """记录函数调用"""
        if func_name not in self.function_metrics:
            self.function_metrics[func_name] = FunctionMetrics(name=func_name)
        
        metrics = self.function_metrics[func_name]
        metrics.call_count += 1
        metrics.total_time += execution_time
        metrics.avg_time = metrics.total_time / metrics.call_count
        metrics.min_time = min(metrics.min_time, execution_time)
        metrics.max_time = max(metrics.max_time, execution_time)
        metrics.last_called = time.time()
        
        if had_error:
            metrics.error_count += 1

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
        self.optimization_rules = self._load_optimization_rules()
        self.logger = logging.getLogger(__name__)
    
    def _load_optimization_rules(self) -> Dict[str, Any]:
        """加载优化规则"""
        return {
            "cpu_threshold": 80.0,  # CPU使用率阈值
            "memory_threshold": 85.0,  # 内存使用率阈值
            "response_time_threshold": 2.0,  # 响应时间阈值（秒）
            "error_rate_threshold": 0.05,  # 错误率阈值
            "thread_count_threshold": 100,  # 线程数阈值
        }
    
    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能"""
        summary = self.monitor.get_performance_summary()
        if "error" in summary:
            return summary
        
        issues = []
        recommendations = []
        
        # 分析系统指标
        system_metrics = summary["system_metrics"]
        
        # CPU分析
        if system_metrics["max_cpu_percent"] > self.optimization_rules["cpu_threshold"]:
            issues.append({
                "type": "high_cpu",
                "severity": "high",
                "description": f"CPU使用率过高: {system_metrics['max_cpu_percent']:.1f}%",
                "threshold": self.optimization_rules["cpu_threshold"]
            })
            recommendations.extend([
                "考虑优化CPU密集型操作",
                "使用异步编程减少阻塞",
                "考虑使用多进程处理CPU密集型任务"
            ])
        
        # 内存分析
        if system_metrics["max_memory_percent"] > self.optimization_rules["memory_threshold"]:
            issues.append({
                "type": "high_memory",
                "severity": "high",
                "description": f"内存使用率过高: {system_metrics['max_memory_percent']:.1f}%",
                "threshold": self.optimization_rules["memory_threshold"]
            })
            recommendations.extend([
                "检查内存泄漏",
                "优化数据结构使用",
                "考虑使用生成器减少内存占用"
            ])
        
        # 线程分析
        if system_metrics["active_threads"] > self.optimization_rules["thread_count_threshold"]:
            issues.append({
                "type": "too_many_threads",
                "severity": "medium",
                "description": f"活跃线程数过多: {system_metrics['active_threads']}",
                "threshold": self.optimization_rules["thread_count_threshold"]
            })
            recommendations.extend([
                "考虑使用线程池",
                "减少不必要的线程创建",
                "使用异步编程替代多线程"
            ])
        
        # 分析函数性能
        function_issues = self._analyze_function_performance(summary["function_metrics"])
        issues.extend(function_issues)
        
        return {
            "analysis_time": datetime.now().isoformat(),
            "issues": issues,
            "recommendations": recommendations,
            "performance_score": self._calculate_performance_score(issues),
            "summary": summary
        }
    
    def _analyze_function_performance(self, function_metrics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析函数性能"""
        issues = []
        
        for func in function_metrics:
            # 检查响应时间
            if func["avg_time"] > self.optimization_rules["response_time_threshold"]:
                issues.append({
                    "type": "slow_function",
                    "severity": "medium",
                    "description": f"函数 {func['name']} 平均响应时间过长: {func['avg_time']:.3f}s",
                    "function": func["name"],
                    "avg_time": func["avg_time"]
                })
            
            # 检查错误率
            if func["call_count"] > 0:
                error_rate = func["error_count"] / func["call_count"]
                if error_rate > self.optimization_rules["error_rate_threshold"]:
                    issues.append({
                        "type": "high_error_rate",
                        "severity": "high",
                        "description": f"函数 {func['name']} 错误率过高: {error_rate:.2%}",
                        "function": func["name"],
                        "error_rate": error_rate
                    })
        
        return issues
    
    def _calculate_performance_score(self, issues: List[Dict[str, Any]]) -> float:
        """计算性能分数"""
        base_score = 100.0
        
        for issue in issues:
            if issue["severity"] == "high":
                base_score -= 20
            elif issue["severity"] == "medium":
                base_score -= 10
            elif issue["severity"] == "low":
                base_score -= 5
        
        return max(base_score, 0.0)
    
    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        analysis = self.analyze_performance()
        
        report = f"""
# 性能优化报告

生成时间: {analysis['analysis_time']}
性能评分: {analysis['performance_score']:.1f}/100

## 系统概览
- 平均CPU使用率: {analysis['summary']['system_metrics']['avg_cpu_percent']:.1f}%
- 最大CPU使用率: {analysis['summary']['system_metrics']['max_cpu_percent']:.1f}%
- 平均内存使用率: {analysis['summary']['system_metrics']['avg_memory_percent']:.1f}%
- 最大内存使用率: {analysis['summary']['system_metrics']['max_memory_percent']:.1f}%
- 活跃线程数: {analysis['summary']['system_metrics']['active_threads']}

## 发现的问题
"""
        
        if analysis['issues']:
            for i, issue in enumerate(analysis['issues'], 1):
                report += f"{i}. **{issue['severity'].upper()}**: {issue['description']}\n"
        else:
            report += "未发现性能问题。\n"
        
        report += "\n## 优化建议\n"
        if analysis['recommendations']:
            for i, rec in enumerate(analysis['recommendations'], 1):
                report += f"{i}. {rec}\n"
        else:
            report += "当前性能良好，无需特别优化。\n"
        
        report += "\n## 函数性能统计\n"
        for func in analysis['summary']['function_metrics'][:5]:  # 显示前5个
            report += f"- {func['name']}: 调用{func['call_count']}次, 平均{func['avg_time']:.3f}s\n"
        
        return report
    
    def save_report(self, file_path: str) -> bool:
        """保存报告到文件"""
        try:
            report = self.generate_optimization_report()
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report)
            return True
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
            return False

class ErrorHandler:
    """增强的错误处理器"""
    
    def __init__(self):
        self.error_history: List[Dict[str, Any]] = []
        self.error_patterns: Dict[str, int] = defaultdict(int)
        self.logger = logging.getLogger(__name__)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler('error.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.ERROR)
        
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许用户中断
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_info = {
            "timestamp": time.time(),
            "type": exc_type.__name__,
            "message": str(exc_value),
            "traceback": traceback.format_exception(exc_type, exc_value, exc_traceback),
            "module": getattr(exc_traceback.tb_frame, 'f_globals', {}).get('__name__', 'unknown')
        }
        
        self.error_history.append(error_info)
        self.error_patterns[exc_type.__name__] += 1
        
        # 记录到日志
        self.logger.error(
            f"未处理的异常: {exc_type.__name__}: {exc_value}",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
        
        # 分析错误模式
        self._analyze_error_patterns()
    
    def _analyze_error_patterns(self):
        """分析错误模式"""
        if len(self.error_history) >= 10:  # 当有足够的错误样本时
            recent_errors = self.error_history[-10:]
            error_types = [e["type"] for e in recent_errors]
            
            # 检查是否有重复的错误类型
            from collections import Counter
            error_counts = Counter(error_types)
            
            for error_type, count in error_counts.items():
                if count >= 3:  # 如果同一类型错误出现3次以上
                    self.logger.warning(f"检测到重复错误模式: {error_type} 出现了 {count} 次")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        if not self.error_history:
            return {"total_errors": 0, "error_patterns": {}}
        
        recent_errors = self.error_history[-100:]  # 最近100个错误
        
        return {
            "total_errors": len(self.error_history),
            "recent_errors": len(recent_errors),
            "error_patterns": dict(self.error_patterns),
            "most_common_errors": [
                {"type": error_type, "count": count}
                for error_type, count in sorted(
                    self.error_patterns.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
            ],
            "last_error": self.error_history[-1] if self.error_history else None
        }

# 全局实例
performance_monitor = PerformanceMonitor()
performance_optimizer = PerformanceOptimizer(performance_monitor)
error_handler = ErrorHandler()

# 设置全局异常处理器
import sys
sys.excepthook = error_handler.handle_exception
