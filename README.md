# AI代码生成器 - 智能编程助手

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](https://github.com)

一个功能强大的AI驱动代码生成器，支持多种编程语言和AI模型，提供智能代码生成、调试、优化和打包功能。

## ✨ 主要特性

### 🤖 AI智能助手
- **多AI模型支持**: OpenAI GPT、Anthropic Claude、Google Gemini等12个主流AI提供商
- **智能代码生成**: 支持Python、JavaScript、Java、C++等18种编程语言
- **代码优化**: AI驱动的代码重构和性能优化建议
- **智能调试**: 自动错误分析和修复建议

### 🎨 现代化界面
- **响应式布局**: 自适应不同屏幕尺寸的现代化UI
- **多主题支持**: 内置6种主题，支持自定义主题
- **实时预览**: 代码生成过程的实时反馈
- **拖拽操作**: 直观的文件和组件管理

### 📦 多平台打包
- **一键打包**: 支持Windows、Linux、macOS多平台打包
- **多种格式**: 可执行文件、安装包、便携版、容器镜像
- **自动化构建**: 集成CI/CD流程，自动化构建和部署
- **依赖管理**: 智能依赖分析和打包优化

### 🔧 开发工具集成
- **版本控制**: Git集成，支持分支管理和代码提交
- **性能监控**: 实时性能分析和优化建议
- **错误追踪**: 智能错误分析和解决方案推荐
- **代码质量**: 自动代码审查和质量评估

## 🚀 快速开始

### 环境要求

- Python 3.8 或更高版本
- 操作系统: Windows 10+, Ubuntu 18.04+, macOS 10.14+
- 内存: 至少 4GB RAM
- 存储: 至少 2GB 可用空间

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/ai-code-generator.git
cd ai-code-generator
```

2. **安装依赖**
```bash
pip install -r requirements_enhanced.txt
```

3. **配置API密钥**
```bash
cp api_configs_example.json api_configs.json
# 编辑 api_configs.json 文件，添加您的API密钥
```

4. **启动应用**
```bash
# 启动现代化界面（推荐）
python main.py --modern

# 启动增强版界面
python main.py --enhanced

# 查看所有选项
python main.py --help
```

### 配置API密钥

编辑 `api_configs.json` 文件，添加您的API密钥：

```json
{
  "openai": {
    "api_key": "your-openai-api-key",
    "enabled": true
  },
  "anthropic": {
    "api_key": "your-anthropic-api-key", 
    "enabled": true
  }
}
```

## 📖 使用指南

### 基本操作

1. **代码生成**
   - 在主界面选择"AI助手"
   - 输入代码需求描述
   - 选择编程语言和AI模型
   - 点击"生成代码"

2. **代码调试**
   - 粘贴有问题的代码
   - 输入错误信息
   - AI将分析并提供修复建议

3. **项目打包**
   - 选择"打包发布"功能
   - 配置打包参数
   - 选择目标平台
   - 一键生成可执行文件

### 高级功能

#### 自定义主题
```python
# 创建自定义主题
from ui.theme_manager import ThemeManager

theme_manager = ThemeManager()
custom_colors = {
    "primary": "#1E3A8A",
    "secondary": "#3B82F6",
    "background": "#0F172A"
}
theme_manager.create_custom_theme("my_theme", custom_colors)
```

#### 性能监控
```python
# 启用性能监控
from tools.performance_optimizer import performance_monitor

performance_monitor.start_monitoring()
# 您的代码
summary = performance_monitor.get_performance_summary()
```

#### 批量代码生成
```python
from ai.enhanced_code_generator import EnhancedCodeGenerator, CodeGenerationRequest

generator = EnhancedCodeGenerator()
requests = [
    CodeGenerationRequest(
        prompt="创建一个排序函数",
        language=CodeLanguage.PYTHON
    ),
    # 更多请求...
]

# 批量处理
results = await generator.batch_generate(requests)
```

## 🛠️ 开发指南

### 项目结构

```
ai-code-generator/
├── ai/                     # AI核心模块
│   ├── enhanced_ai_engine.py
│   ├── enhanced_code_generator.py
│   └── context_aware_generator.py
├── ui/                     # 用户界面
│   ├── modern_interface.py
│   ├── layout_manager.py
│   └── theme_manager.py
├── tools/                  # 工具模块
│   ├── enhanced_packager.py
│   ├── performance_optimizer.py
│   └── multi_platform_packager.py
├── deployment/             # 部署相关
├── scripts/               # 脚本文件
├── docs/                  # 文档
└── main.py               # 主程序入口
```

### 添加新的AI提供商

1. 在 `ai/enhanced_ai_engine.py` 中添加新的提供商枚举
2. 实现对应的API调用方法
3. 更新配置文件模板
4. 添加相应的测试用例

### 自定义界面组件

```python
from ui.layout_manager import LayoutManager
from PyQt5.QtWidgets import QWidget

class CustomWidget(QWidget):
    def __init__(self, layout_manager: LayoutManager):
        super().__init__()
        self.layout_manager = layout_manager
        self.setup_ui()
    
    def setup_ui(self):
        # 使用布局管理器创建响应式界面
        layout = self.layout_manager.create_main_layout()
        self.setLayout(layout)
```

## 📊 性能优化

### 系统要求优化

- **CPU**: 多核处理器推荐，支持并行代码生成
- **内存**: 8GB+ RAM用于大型项目处理
- **网络**: 稳定的网络连接用于AI API调用

### 性能调优建议

1. **启用缓存**: 开启API响应缓存减少重复请求
2. **批量处理**: 使用批量API调用提高效率
3. **异步操作**: 利用异步编程提升响应速度
4. **资源监控**: 定期检查性能报告

## 🔧 故障排除

### 常见问题

**Q: 启动时提示缺少依赖**
```bash
# 重新安装依赖
pip install -r requirements_enhanced.txt --force-reinstall
```

**Q: API调用失败**
- 检查网络连接
- 验证API密钥是否正确
- 确认API配额是否充足

**Q: 界面显示异常**
- 更新显卡驱动
- 尝试不同的主题
- 检查屏幕分辨率设置

**Q: 打包失败**
- 确保安装了PyInstaller
- 检查项目路径中是否包含特殊字符
- 查看详细错误日志

### 日志分析

应用会生成以下日志文件：
- `error.log`: 错误日志
- `performance.log`: 性能日志
- `api.log`: API调用日志

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范

- 遵循 PEP 8 Python代码规范
- 添加适当的文档字符串
- 编写单元测试
- 保持代码简洁可读

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [OpenAI](https://openai.com) - GPT模型支持
- [Anthropic](https://anthropic.com) - Claude模型支持
- [Google](https://ai.google) - Gemini模型支持
- [PyQt5](https://riverbankcomputing.com/software/pyqt/) - GUI框架
- 所有贡献者和用户的支持

## 📞 联系我们

- 项目主页: [GitHub Repository](https://github.com/your-repo/ai-code-generator)
- 问题反馈: [Issues](https://github.com/your-repo/ai-code-generator/issues)
- 讨论区: [Discussions](https://github.com/your-repo/ai-code-generator/discussions)

---

**让AI助力您的编程之旅！** 🚀
