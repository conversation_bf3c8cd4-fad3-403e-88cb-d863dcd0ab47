#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包管理器窗口
支持多种打包格式和配置选项
"""

import os
import sys
import json
from pathlib import Path
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QLabel, QLineEdit, QComboBox, QCheckBox, QPushButton,
                           QFileDialog, QMessageBox, QProgressBar, QSpinBox,
                           QGroupBox, QFormLayout, QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon

class PackageWorker(QThread):
    """打包工作线程"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, config):
        super().__init__()
        self.config = config

    def run(self):
        try:
            from tools.multi_platform_packager import PackageManager, PackageType
            
            self.status.emit("正在初始化打包管理器...")
            manager = PackageManager()
            
            # 创建构建器
            self.status.emit("正在配置构建选项...")
            builder = manager.create_builder(PackageType(self.config['package_type']))
            
            # 设置构建选项
            builder.config.app_name = self.config['app_name']
            builder.config.version = self.config['version']
            builder.config.entry_point = self.config['entry_point']
            builder.config.icon_path = self.config.get('icon_path')
            builder.config.console = self.config.get('console', False)
            builder.config.onefile = self.config.get('onefile', True)
            builder.config.clean_build = self.config.get('clean_build', True)
            
            # 添加包含文件
            if 'include_files' in self.config:
                builder.config.include_files = self.config['include_files']
            
            # 添加排除模式
            if 'exclude_patterns' in self.config:
                builder.config.exclude_patterns = self.config['exclude_patterns']
            
            # 开始构建
            self.status.emit("正在打包应用程序...")
            success = builder.build()
            
            if success:
                self.finished.emit(True, "打包完成")
            else:
                self.finished.emit(False, "打包失败")
                
        except Exception as e:
            self.finished.emit(False, f"打包过程出错: {str(e)}")

class PackageManagerWindow(QMainWindow):
    """打包管理器窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("打包管理器")
        self.setMinimumSize(800, 600)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建布局
        layout = QVBoxLayout(main_widget)
        
        # 创建选项卡
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # 基本设置选项卡
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)
        tabs.addTab(basic_tab, "基本设置")
        
        # 应用名称
        self.app_name = QLineEdit()
        self.app_name.setPlaceholderText("输入应用名称")
        basic_layout.addRow("应用名称:", self.app_name)
        
        # 版本号
        self.version = QLineEdit()
        self.version.setPlaceholderText("例如: 1.0.0")
        basic_layout.addRow("版本号:", self.version)
        
        # 入口文件
        entry_layout = QHBoxLayout()
        self.entry_point = QLineEdit()
        self.entry_point.setPlaceholderText("选择主程序入口文件")
        entry_layout.addWidget(self.entry_point)
        
        select_entry_btn = QPushButton("浏览...")
        select_entry_btn.clicked.connect(self.select_entry_file)
        entry_layout.addWidget(select_entry_btn)
        basic_layout.addRow("入口文件:", entry_layout)
        
        # 图标文件
        icon_layout = QHBoxLayout()
        self.icon_path = QLineEdit()
        self.icon_path.setPlaceholderText("选择应用图标文件")
        icon_layout.addWidget(self.icon_path)
        
        select_icon_btn = QPushButton("浏览...")
        select_icon_btn.clicked.connect(self.select_icon_file)
        icon_layout.addWidget(select_icon_btn)
        basic_layout.addRow("图标文件:", icon_layout)
        
        # 打包类型
        self.package_type = QComboBox()
        self.package_type.addItems([
            "可执行文件 (EXE)",
            "安装程序 (INSTALLER)",
            "便携版 (PORTABLE)",
            "Linux AppImage",
            "macOS DMG",
            "Ubuntu Snap",
            "Flatpak",
            "Windows MSI"
        ])
        basic_layout.addRow("打包类型:", self.package_type)
        
        # 高级设置选项卡
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)
        tabs.addTab(advanced_tab, "高级设置")
        
        # 打包选项组
        package_group = QGroupBox("打包选项")
        package_layout = QVBoxLayout(package_group)
        
        self.console = QCheckBox("显示控制台窗口")
        package_layout.addWidget(self.console)
        
        self.onefile = QCheckBox("生成单文件")
        self.onefile.setChecked(True)
        package_layout.addWidget(self.onefile)
        
        self.clean_build = QCheckBox("清理构建目录")
        self.clean_build.setChecked(True)
        package_layout.addWidget(self.clean_build)
        
        self.upx = QCheckBox("使用UPX压缩")
        self.upx.setChecked(True)
        package_layout.addWidget(self.upx)
        
        advanced_layout.addWidget(package_group)
        
        # 资源文件组
        resource_group = QGroupBox("资源文件")
        resource_layout = QVBoxLayout(resource_group)
        
        # 包含文件
        include_layout = QHBoxLayout()
        self.include_files = QTextEdit()
        self.include_files.setPlaceholderText("每行一个文件或目录路径")
        include_layout.addWidget(self.include_files)
        
        include_btn_layout = QVBoxLayout()
        add_include_btn = QPushButton("添加文件...")
        add_include_btn.clicked.connect(self.add_include_file)
        include_btn_layout.addWidget(add_include_btn)
        
        add_include_dir_btn = QPushButton("添加目录...")
        add_include_dir_btn.clicked.connect(self.add_include_dir)
        include_btn_layout.addWidget(add_include_dir_btn)
        
        include_layout.addLayout(include_btn_layout)
        resource_layout.addLayout(include_layout)
        
        # 排除模式
        self.exclude_patterns = QTextEdit()
        self.exclude_patterns.setPlaceholderText("每行一个排除模式，例如: *.pyc, __pycache__")
        self.exclude_patterns.setText("*.pyc\n__pycache__\n*.spec\ndist/*\nbuild/*")
        resource_layout.addWidget(self.exclude_patterns)
        
        advanced_layout.addWidget(resource_group)
        
        # 状态栏
        self.status_bar = self.statusBar()
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        button_layout.addWidget(save_btn)
        
        load_btn = QPushButton("加载配置")
        load_btn.clicked.connect(self.load_config)
        button_layout.addWidget(load_btn)
        
        button_layout.addStretch()
        
        package_btn = QPushButton("开始打包")
        package_btn.setMinimumWidth(120)
        package_btn.clicked.connect(self.start_package)
        button_layout.addWidget(package_btn)
        
        layout.addLayout(button_layout)
        
        # 加载默认配置
        self.load_default_config()
    
    def select_entry_file(self):
        """选择入口文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择入口文件", "",
            "Python文件 (*.py);;所有文件 (*.*)"
        )
        if file_path:
            self.entry_point.setText(file_path)
    
    def select_icon_file(self):
        """选择图标文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图标文件", "",
            "图标文件 (*.ico);;所有文件 (*.*)"
        )
        if file_path:
            self.icon_path.setText(file_path)
    
    def add_include_file(self):
        """添加包含文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择要包含的文件", "",
            "所有文件 (*.*)"
        )
        if files:
            current_text = self.include_files.toPlainText()
            if current_text:
                current_text += "\n"
            self.include_files.setText(current_text + "\n".join(files))
    
    def add_include_dir(self):
        """添加包含目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择要包含的目录"
        )
        if dir_path:
            current_text = self.include_files.toPlainText()
            if current_text:
                current_text += "\n"
            self.include_files.setText(current_text + dir_path)
    
    def load_default_config(self):
        """加载默认配置"""
        try:
            if os.path.exists('package_config.json'):
                with open('package_config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.apply_config(config)
            else:
                # 设置一些默认值
                self.app_name.setText("AICodeGenerator")
                self.version.setText("2.0.0")
                self.entry_point.setText("main.py")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载默认配置失败: {str(e)}")
    
    def apply_config(self, config):
        """应用配置"""
        self.app_name.setText(config.get('app_name', ''))
        self.version.setText(config.get('version', ''))
        self.entry_point.setText(config.get('entry_point', ''))
        self.icon_path.setText(config.get('icon_path', ''))
        
        # 设置打包类型
        package_type = config.get('package_type', 'exe')
        index = self.package_type.findText(package_type, Qt.MatchContains)
        if index >= 0:
            self.package_type.setCurrentIndex(index)
        
        # 设置选项
        self.console.setChecked(config.get('console', False))
        self.onefile.setChecked(config.get('onefile', True))
        self.clean_build.setChecked(config.get('clean_build', True))
        self.upx.setChecked(config.get('upx', True))
        
        # 设置资源文件
        if 'include_files' in config:
            self.include_files.setText('\n'.join(config['include_files']))
        
        if 'exclude_patterns' in config:
            self.exclude_patterns.setText('\n'.join(config['exclude_patterns']))
    
    def get_current_config(self):
        """获取当前配置"""
        config = {
            'app_name': self.app_name.text(),
            'version': self.version.text(),
            'entry_point': self.entry_point.text(),
            'icon_path': self.icon_path.text(),
            'package_type': self.package_type.currentText().split(' ')[0].lower(),
            'console': self.console.isChecked(),
            'onefile': self.onefile.isChecked(),
            'clean_build': self.clean_build.isChecked(),
            'upx': self.upx.isChecked()
        }
        
        # 处理包含文件
        include_text = self.include_files.toPlainText().strip()
        if include_text:
            config['include_files'] = [
                f.strip() for f in include_text.split('\n')
            ]
        
        # 处理排除模式
        exclude_text = self.exclude_patterns.toPlainText().strip()
        if exclude_text:
            config['exclude_patterns'] = [
                p.strip() for p in exclude_text.split('\n')
            ]
        
        return config
    
    def save_config(self):
        """保存配置"""
        try:
            config = self.get_current_config()
            with open('package_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            QMessageBox.information(self, "成功", "配置已保存")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """加载配置"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择配置文件", "",
                "JSON文件 (*.json);;所有文件 (*.*)"
            )
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.apply_config(config)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载配置失败: {str(e)}")
    
    def start_package(self):
        """开始打包"""
        # 检查必填项
        if not self.app_name.text():
            QMessageBox.warning(self, "错误", "请输入应用名称")
            return
        
        if not self.version.text():
            QMessageBox.warning(self, "错误", "请输入版本号")
            return
        
        if not self.entry_point.text():
            QMessageBox.warning(self, "错误", "请选择入口文件")
            return
        
        # 获取当前配置
        config = self.get_current_config()
        
        # 创建工作线程
        self.worker = PackageWorker(config)
        self.worker.progress.connect(self.update_progress)
        self.worker.status.connect(self.update_status)
        self.worker.finished.connect(self.package_finished)
        
        # 禁用界面
        self.setEnabled(False)
        self.progress_bar.setRange(0, 0)  # 显示忙碌状态
        
        # 开始打包
        self.worker.start()
    
    def update_progress(self, value):
        """更新进度"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新状态"""
        self.status_bar.showMessage(message)
    
    def package_finished(self, success, message):
        """打包完成"""
        self.setEnabled(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100 if success else 0)
        
        if success:
            QMessageBox.information(self, "成功", "打包完成")
        else:
            QMessageBox.warning(self, "错误", f"打包失败: {message}")
