# 部署和分发模块
"""
AI代码生成器 - 部署和分发系统

这个模块提供了完整的多平台部署、容器化、自动更新和应用商店发布功能。

主要组件:
- PlatformManager: 多平台构建管理
- ContainerManager: 容器化部署管理  
- AutoUpdater: 自动更新机制
- AppStorePublisher: 应用商店发布管理
- DeploymentManager: 统一部署管理器

使用示例:
    from deployment import DeploymentManager
    
    manager = DeploymentManager()
    manager.create_all_deployment_files()
    manager.execute_deployment_plan('production')
"""

from .platform_manager import PlatformManager, PlatformType, DeploymentType, BuildResult
from .container_manager import ContainerManager, ContainerPlatform, DeploymentEnvironment
from .auto_updater import AutoUpdater, UpdateChannel, UpdateStatus, UpdateInfo
from .app_store_publisher import AppStorePublisher, AppStore, PublishStatus, PublishResult
from .deployment_manager import DeploymentManager, DeploymentStage, DeploymentPlan, DeploymentProgress

__version__ = "1.0.0"
__author__ = "AI Code Team"

__all__ = [
    # 平台管理
    'PlatformManager',
    'PlatformType', 
    'DeploymentType',
    'BuildResult',
    
    # 容器管理
    'ContainerManager',
    'ContainerPlatform',
    'DeploymentEnvironment',
    
    # 自动更新
    'AutoUpdater',
    'UpdateChannel',
    'UpdateStatus', 
    'UpdateInfo',
    
    # 应用商店发布
    'AppStorePublisher',
    'AppStore',
    'PublishStatus',
    'PublishResult',
    
    # 统一部署管理
    'DeploymentManager',
    'DeploymentStage',
    'DeploymentPlan',
    'DeploymentProgress'
]