# 应用商店发布管理器
import os
import json
import requests
import subprocess
import tempfile
import shutil
import base64
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import xml.etree.ElementTree as ET

class AppStore(Enum):
    MICROSOFT_STORE = "microsoft_store"
    MAC_APP_STORE = "mac_app_store"
    GOOGLE_PLAY = "google_play"
    SNAP_STORE = "snap_store"
    FLATHUB = "flathub"
    CHOCOLATEY = "chocolatey"
    HOMEBREW = "homebrew"
    APT_REPOSITORY = "apt_repository"
    YUM_REPOSITORY = "yum_repository"

class PublishStatus(Enum):
    PREPARING = "preparing"
    UPLOADING = "uploading"
    REVIEWING = "reviewing"
    APPROVED = "approved"
    PUBLISHED = "published"
    REJECTED = "rejected"
    FAILED = "failed"

@dataclass
class AppMetadata:
    """应用元数据"""
    name: str
    description: str
    short_description: str
    version: str
    category: str
    keywords: List[str]
    screenshots: List[str]
    icon: str
    website: str
    support_url: str
    privacy_policy: str
    license: str
    age_rating: str
    languages: List[str]
    system_requirements: Dict[str, str]

@dataclass
class PublishResult:
    """发布结果"""
    store: AppStore
    status: PublishStatus
    submission_id: str
    message: str
    review_notes: str = ""
    estimated_review_time: str = ""

class AppStorePublisher:
    """应用商店发布管理器"""
    
    def __init__(self, project_path: str = None):
        self.project_path = project_path or os.getcwd()
        self.config = self._load_config()
        self.metadata = self._load_metadata()
    
    def _load_config(self) -> Dict:
        """加载发布配置"""
        config_file = os.path.join(self.project_path, 'publish_config.json')
        default_config = {
            'app_info': {
                'bundle_id': 'com.aicodegen.app',
                'publisher_name': 'AI Code Team',
                'publisher_display_name': 'AI Code Generator Team',
                'contact_email': '<EMAIL>',
                'website': 'https://aicodegen.com',
                'support_url': 'https://aicodegen.com/support',
                'privacy_policy': 'https://aicodegen.com/privacy'
            },
            'stores': {
                'microsoft_store': {
                    'enabled': False,
                    'tenant_id': '',
                    'client_id': '',
                    'client_secret': '',
                    'app_id': '',
                    'package_family_name': ''
                },
                'mac_app_store': {
                    'enabled': False,
                    'team_id': '',
                    'bundle_id': '',
                    'app_specific_password': '',
                    'apple_id': ''
                },
                'google_play': {
                    'enabled': False,
                    'service_account_key': '',
                    'package_name': 'com.aicodegen.app',
                    'track': 'internal'  # internal, alpha, beta, production
                },
                'snap_store': {
                    'enabled': True,
                    'snap_name': 'ai-code-generator',
                    'channel': 'stable',  # stable, candidate, beta, edge
                    'grade': 'stable',    # stable, devel
                    'confinement': 'strict'  # strict, devmode, classic
                },
                'flathub': {
                    'enabled': True,
                    'app_id': 'com.aicodegen.AICodeGenerator',
                    'runtime': 'org.freedesktop.Platform',
                    'runtime_version': '22.08',
                    'sdk': 'org.freedesktop.Sdk'
                },
                'chocolatey': {
                    'enabled': True,
                    'package_id': 'ai-code-generator',
                    'api_key': '',
                    'maintainer': 'AI Code Team'
                },
                'homebrew': {
                    'enabled': True,
                    'formula_name': 'ai-code-generator',
                    'tap_name': 'aicodegen/tap',
                    'github_token': ''
                }
            },
            'assets': {
                'icon_1024': 'assets/icon-1024.png',
                'icon_512': 'assets/icon-512.png',
                'icon_256': 'assets/icon-256.png',
                'icon_128': 'assets/icon-128.png',
                'icon_64': 'assets/icon-64.png',
                'icon_32': 'assets/icon-32.png',
                'screenshots': [
                    'assets/screenshot-1.png',
                    'assets/screenshot-2.png',
                    'assets/screenshot-3.png'
                ],
                'banner': 'assets/banner.png',
                'feature_graphic': 'assets/feature-graphic.png'
            },
            'localization': {
                'default_language': 'en-US',
                'supported_languages': ['en-US', 'zh-CN', 'ja-JP', 'ko-KR']
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self._merge_config(default_config, user_config)
        
        return default_config
    
    def _load_metadata(self) -> AppMetadata:
        """加载应用元数据"""
        metadata_file = os.path.join(self.project_path, 'app_metadata.json')
        default_metadata = {
            'name': 'AI Code Generator',
            'description': 'AI-powered code generation and packaging tool that helps developers create, optimize, and deploy applications faster.',
            'short_description': 'AI-powered code generation tool',
            'version': '1.0.0',
            'category': 'Developer Tools',
            'keywords': ['ai', 'code', 'generator', 'development', 'programming', 'automation'],
            'screenshots': self.config['assets']['screenshots'],
            'icon': self.config['assets']['icon_512'],
            'website': self.config['app_info']['website'],
            'support_url': self.config['app_info']['support_url'],
            'privacy_policy': self.config['app_info']['privacy_policy'],
            'license': 'MIT',
            'age_rating': '4+',
            'languages': self.config['localization']['supported_languages'],
            'system_requirements': {
                'windows': 'Windows 10 version 1903 or higher',
                'macos': 'macOS 10.15 or later',
                'linux': 'Ubuntu 18.04 or equivalent'
            }
        }
        
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r', encoding='utf-8') as f:
                user_metadata = json.load(f)
                default_metadata.update(user_metadata)
        
        return AppMetadata(**default_metadata)
    
    def _merge_config(self, default: Dict, user: Dict):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def publish_to_microsoft_store(self) -> PublishResult:
        """发布到Microsoft Store"""
        store_config = self.config['stores']['microsoft_store']
        
        if not store_config['enabled']:
            return PublishResult(
                store=AppStore.MICROSOFT_STORE,
                status=PublishStatus.FAILED,
                submission_id="",
                message="Microsoft Store发布未启用"
            )
        
        try:
            # 1. 创建MSIX包
            msix_path = self._create_msix_package()
            
            # 2. 获取访问令牌
            access_token = self._get_microsoft_store_token(store_config)
            
            # 3. 创建提交
            submission_id = self._create_microsoft_store_submission(access_token, store_config)
            
            # 4. 上传包
            self._upload_to_microsoft_store(access_token, submission_id, msix_path, store_config)
            
            # 5. 提交审核
            self._commit_microsoft_store_submission(access_token, submission_id, store_config)
            
            return PublishResult(
                store=AppStore.MICROSOFT_STORE,
                status=PublishStatus.REVIEWING,
                submission_id=submission_id,
                message="已提交到Microsoft Store审核",
                estimated_review_time="1-3个工作日"
            )
            
        except Exception as e:
            return PublishResult(
                store=AppStore.MICROSOFT_STORE,
                status=PublishStatus.FAILED,
                submission_id="",
                message=f"发布失败: {str(e)}"
            )
    
    def _create_msix_package(self) -> str:
        """创建MSIX包"""
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='msix_')
        
        # 生成Package.appxmanifest
        manifest_content = self._generate_appx_manifest()
        manifest_path = os.path.join(temp_dir, 'Package.appxmanifest')
        with open(manifest_path, 'w', encoding='utf-8') as f:
            f.write(manifest_content)
        
        # 复制应用文件
        app_dir = os.path.join(temp_dir, 'app')
        shutil.copytree(self.project_path, app_dir, 
                       ignore=shutil.ignore_patterns('*.git*', '__pycache__', '*.pyc'))
        
        # 复制资源文件
        assets_dir = os.path.join(temp_dir, 'Assets')
        os.makedirs(assets_dir, exist_ok=True)
        
        # 复制图标
        for size in [16, 32, 44, 48, 64, 88, 150, 310]:
            icon_file = f"icon-{size}.png"
            src_path = os.path.join(self.project_path, 'assets', icon_file)
            if os.path.exists(src_path):
                shutil.copy2(src_path, os.path.join(assets_dir, icon_file))
        
        # 使用MakeAppx工具创建MSIX包
        msix_path = os.path.join(self.project_path, 'dist', f"{self.metadata.name}.msix")
        os.makedirs(os.path.dirname(msix_path), exist_ok=True)
        
        subprocess.run([
            'makeappx', 'pack',
            '/d', temp_dir,
            '/p', msix_path,
            '/o'
        ], check=True)
        
        return msix_path
    
    def _generate_appx_manifest(self) -> str:
        """生成AppX清单文件"""
        bundle_id = self.config['app_info']['bundle_id']
        publisher = self.config['app_info']['publisher_name']
        
        return f"""<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
         xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10">
  <Identity Name="{bundle_id}"
            Version="{self.metadata.version}.0"
            Publisher="CN={publisher}" />
  
  <Properties>
    <DisplayName>{self.metadata.name}</DisplayName>
    <PublisherDisplayName>{self.config['app_info']['publisher_display_name']}</PublisherDisplayName>
    <Logo>Assets\\icon-150.png</Logo>
    <Description>{self.metadata.description}</Description>
  </Properties>
  
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.22000.0" />
  </Dependencies>
  
  <Resources>
    <Resource Language="en-us" />
    <Resource Language="zh-cn" />
  </Resources>
  
  <Applications>
    <Application Id="App" Executable="app\\main.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="{self.metadata.name}"
                          Square150x150Logo="Assets\\icon-150.png"
                          Square44x44Logo="Assets\\icon-44.png"
                          Description="{self.metadata.short_description}"
                          BackgroundColor="transparent">
        <uap:DefaultTile Wide310x150Logo="Assets\\icon-310x150.png"
                         Square310x310Logo="Assets\\icon-310.png"
                         Square71x71Logo="Assets\\icon-71.png">
        </uap:DefaultTile>
        <uap:SplashScreen Image="Assets\\splash-620x300.png" />
      </uap:VisualElements>
    </Application>
  </Applications>
  
  <Capabilities>
    <Capability Name="internetClient" />
    <Capability Name="privateNetworkClientServer" />
  </Capabilities>
</Package>"""
    
    def _get_microsoft_store_token(self, store_config: Dict) -> str:
        """获取Microsoft Store访问令牌"""
        token_url = f"https://login.microsoftonline.com/{store_config['tenant_id']}/oauth2/v2.0/token"
        
        data = {
            'client_id': store_config['client_id'],
            'client_secret': store_config['client_secret'],
            'scope': 'https://manage.devcenter.microsoft.com/.default',
            'grant_type': 'client_credentials'
        }
        
        response = requests.post(token_url, data=data)
        response.raise_for_status()
        
        return response.json()['access_token']
    
    def publish_to_snap_store(self) -> PublishResult:
        """发布到Snap Store"""
        store_config = self.config['stores']['snap_store']
        
        try:
            # 1. 创建snapcraft.yaml
            snapcraft_yaml = self._generate_snapcraft_yaml()
            snapcraft_path = os.path.join(self.project_path, 'snap', 'snapcraft.yaml')
            os.makedirs(os.path.dirname(snapcraft_path), exist_ok=True)
            
            with open(snapcraft_path, 'w', encoding='utf-8') as f:
                f.write(snapcraft_yaml)
            
            # 2. 构建snap包
            subprocess.run(['snapcraft'], cwd=self.project_path, check=True)
            
            # 3. 上传到Snap Store
            snap_file = f"{store_config['snap_name']}_{self.metadata.version}_amd64.snap"
            subprocess.run([
                'snapcraft', 'upload', snap_file,
                '--release', store_config['channel']
            ], cwd=self.project_path, check=True)
            
            return PublishResult(
                store=AppStore.SNAP_STORE,
                status=PublishStatus.PUBLISHED,
                submission_id="",
                message="已发布到Snap Store"
            )
            
        except Exception as e:
            return PublishResult(
                store=AppStore.SNAP_STORE,
                status=PublishStatus.FAILED,
                submission_id="",
                message=f"发布失败: {str(e)}"
            )
    
    def _generate_snapcraft_yaml(self) -> str:
        """生成snapcraft.yaml"""
        store_config = self.config['stores']['snap_store']
        
        return f"""name: {store_config['snap_name']}
version: '{self.metadata.version}'
summary: {self.metadata.short_description}
description: |
  {self.metadata.description}

grade: {store_config['grade']}
confinement: {store_config['confinement']}
base: core22

architectures:
  - build-on: amd64
    build-for: amd64

apps:
  ai-code-generator:
    command: bin/main
    desktop: share/applications/ai-code-generator.desktop
    plugs:
      - home
      - network
      - network-bind
      - desktop
      - desktop-legacy
      - wayland
      - x11

parts:
  ai-code-generator:
    plugin: python
    source: .
    requirements:
      - requirements.txt
    stage-packages:
      - python3-tk
      - python3-pil
      - python3-pil.imagetk
    organize:
      bin/main.py: bin/main
    override-build: |
      craftctl default
      chmod +x $CRAFT_PART_INSTALL/bin/main
    
  desktop-file:
    plugin: dump
    source: snap/local/
    organize:
      ai-code-generator.desktop: share/applications/ai-code-generator.desktop
"""
    
    def publish_to_flathub(self) -> PublishResult:
        """发布到Flathub"""
        store_config = self.config['stores']['flathub']
        
        try:
            # 1. 创建Flatpak清单
            manifest = self._generate_flatpak_manifest()
            manifest_path = os.path.join(self.project_path, f"{store_config['app_id']}.json")
            
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, indent=2)
            
            # 2. 构建Flatpak
            subprocess.run([
                'flatpak-builder', '--force-clean', '--repo=repo',
                'build-dir', manifest_path
            ], cwd=self.project_path, check=True)
            
            # 3. 创建bundle
            bundle_path = f"{store_config['app_id']}.flatpak"
            subprocess.run([
                'flatpak', 'build-bundle', 'repo', bundle_path,
                store_config['app_id'], '--runtime-repo=https://flathub.org/repo/flathub.flatpakrepo'
            ], cwd=self.project_path, check=True)
            
            return PublishResult(
                store=AppStore.FLATHUB,
                status=PublishStatus.PREPARING,
                submission_id="",
                message="Flatpak包已创建，请提交PR到Flathub仓库",
                review_notes="需要手动提交PR到https://github.com/flathub/flathub"
            )
            
        except Exception as e:
            return PublishResult(
                store=AppStore.FLATHUB,
                status=PublishStatus.FAILED,
                submission_id="",
                message=f"构建失败: {str(e)}"
            )
    
    def _generate_flatpak_manifest(self) -> Dict:
        """生成Flatpak清单"""
        store_config = self.config['stores']['flathub']
        
        return {
            "app-id": store_config['app_id'],
            "runtime": store_config['runtime'],
            "runtime-version": store_config['runtime_version'],
            "sdk": store_config['sdk'],
            "command": "ai-code-generator",
            "finish-args": [
                "--share=network",
                "--share=ipc",
                "--socket=x11",
                "--socket=wayland",
                "--filesystem=home",
                "--device=dri"
            ],
            "modules": [
                {
                    "name": "python3-requirements",
                    "buildsystem": "simple",
                    "build-commands": [
                        "pip3 install --verbose --exists-action=i --no-index --find-links=\"file://${PWD}\" --prefix=${FLATPAK_DEST} -r requirements.txt"
                    ],
                    "sources": [
                        {
                            "type": "file",
                            "path": "requirements.txt"
                        }
                    ]
                },
                {
                    "name": "ai-code-generator",
                    "buildsystem": "simple",
                    "build-commands": [
                        "install -Dm755 main.py ${FLATPAK_DEST}/bin/ai-code-generator",
                        "cp -r ui ai tools ${FLATPAK_DEST}/lib/python3.9/site-packages/",
                        "install -Dm644 assets/icon-128.png ${FLATPAK_DEST}/share/icons/hicolor/128x128/apps/${FLATPAK_ID}.png",
                        "install -Dm644 ai-code-generator.desktop ${FLATPAK_DEST}/share/applications/${FLATPAK_ID}.desktop"
                    ],
                    "sources": [
                        {
                            "type": "dir",
                            "path": "."
                        }
                    ]
                }
            ]
        }
    
    def publish_to_chocolatey(self) -> PublishResult:
        """发布到Chocolatey"""
        store_config = self.config['stores']['chocolatey']
        
        try:
            # 1. 创建Chocolatey包规范
            nuspec_content = self._generate_chocolatey_nuspec()
            nuspec_path = os.path.join(self.project_path, 'chocolatey', f"{store_config['package_id']}.nuspec")
            os.makedirs(os.path.dirname(nuspec_path), exist_ok=True)
            
            with open(nuspec_path, 'w', encoding='utf-8') as f:
                f.write(nuspec_content)
            
            # 2. 创建安装脚本
            install_script = self._generate_chocolatey_install_script()
            script_dir = os.path.join(os.path.dirname(nuspec_path), 'tools')
            os.makedirs(script_dir, exist_ok=True)
            
            with open(os.path.join(script_dir, 'chocolateyinstall.ps1'), 'w', encoding='utf-8') as f:
                f.write(install_script)
            
            # 3. 打包
            subprocess.run(['choco', 'pack', nuspec_path], 
                         cwd=os.path.dirname(nuspec_path), check=True)
            
            # 4. 推送到Chocolatey
            nupkg_file = f"{store_config['package_id']}.{self.metadata.version}.nupkg"
            if store_config.get('api_key'):
                subprocess.run([
                    'choco', 'push', nupkg_file,
                    '--api-key', store_config['api_key']
                ], cwd=os.path.dirname(nuspec_path), check=True)
            
            return PublishResult(
                store=AppStore.CHOCOLATEY,
                status=PublishStatus.REVIEWING,
                submission_id="",
                message="已提交到Chocolatey审核",
                estimated_review_time="1-7个工作日"
            )
            
        except Exception as e:
            return PublishResult(
                store=AppStore.CHOCOLATEY,
                status=PublishStatus.FAILED,
                submission_id="",
                message=f"发布失败: {str(e)}"
            )
    
    def _generate_chocolatey_nuspec(self) -> str:
        """生成Chocolatey nuspec文件"""
        store_config = self.config['stores']['chocolatey']
        
        return f"""<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2015/06/nuspec.xsd">
  <metadata>
    <id>{store_config['package_id']}</id>
    <version>{self.metadata.version}</version>
    <packageSourceUrl>https://github.com/ai-code-generator/chocolatey-package</packageSourceUrl>
    <owners>{store_config['maintainer']}</owners>
    <title>{self.metadata.name}</title>
    <authors>{self.config['app_info']['publisher_name']}</authors>
    <projectUrl>{self.metadata.website}</projectUrl>
    <iconUrl>https://raw.githubusercontent.com/ai-code-generator/assets/main/icon-256.png</iconUrl>
    <copyright>Copyright © 2024 {self.config['app_info']['publisher_name']}</copyright>
    <licenseUrl>https://github.com/ai-code-generator/ai-code-generator/blob/main/LICENSE</licenseUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <projectSourceUrl>https://github.com/ai-code-generator/ai-code-generator</projectSourceUrl>
    <docsUrl>https://aicodegen.com/docs</docsUrl>
    <bugTrackerUrl>https://github.com/ai-code-generator/ai-code-generator/issues</bugTrackerUrl>
    <tags>ai code generator development programming automation</tags>
    <summary>{self.metadata.short_description}</summary>
    <description>{self.metadata.description}</description>
    <releaseNotes>https://github.com/ai-code-generator/ai-code-generator/releases/tag/v{self.metadata.version}</releaseNotes>
  </metadata>
  <files>
    <file src="tools\\**" target="tools" />
  </files>
</package>"""
    
    def _generate_chocolatey_install_script(self) -> str:
        """生成Chocolatey安装脚本"""
        return f"""$ErrorActionPreference = 'Stop'

$packageName = '{self.config['stores']['chocolatey']['package_id']}'
$toolsDir = "$(Split-Path -parent $MyInvocation.MyCommand.Definition)"
$url64 = 'https://github.com/ai-code-generator/ai-code-generator/releases/download/v{self.metadata.version}/ai-code-generator-{self.metadata.version}-windows-x64.exe'

$packageArgs = @{{
  packageName   = $packageName
  unzipLocation = $toolsDir
  fileType      = 'EXE'
  url64bit      = $url64
  softwareName  = '{self.metadata.name}*'
  checksum64    = ''
  checksumType64= 'sha256'
  silentArgs    = '/S'
  validExitCodes= @(0)
}}

Install-ChocolateyPackage @packageArgs"""
    
    def publish_to_homebrew(self) -> PublishResult:
        """发布到Homebrew"""
        store_config = self.config['stores']['homebrew']
        
        try:
            # 1. 生成Homebrew Formula
            formula_content = self._generate_homebrew_formula()
            
            # 2. 创建或更新tap仓库
            tap_dir = os.path.expanduser(f"~/homebrew-{store_config['tap_name'].split('/')[-1]}")
            if not os.path.exists(tap_dir):
                subprocess.run([
                    'git', 'clone', 
                    f"https://github.com/{store_config['tap_name']}.git",
                    tap_dir
                ], check=True)
            
            # 3. 写入Formula文件
            formula_path = os.path.join(tap_dir, 'Formula', f"{store_config['formula_name']}.rb")
            os.makedirs(os.path.dirname(formula_path), exist_ok=True)
            
            with open(formula_path, 'w', encoding='utf-8') as f:
                f.write(formula_content)
            
            # 4. 提交更改
            subprocess.run(['git', 'add', '.'], cwd=tap_dir, check=True)
            subprocess.run([
                'git', 'commit', '-m', 
                f"Update {store_config['formula_name']} to {self.metadata.version}"
            ], cwd=tap_dir, check=True)
            subprocess.run(['git', 'push'], cwd=tap_dir, check=True)
            
            return PublishResult(
                store=AppStore.HOMEBREW,
                status=PublishStatus.PUBLISHED,
                submission_id="",
                message="已发布到Homebrew Tap"
            )
            
        except Exception as e:
            return PublishResult(
                store=AppStore.HOMEBREW,
                status=PublishStatus.FAILED,
                submission_id="",
                message=f"发布失败: {str(e)}"
            )
    
    def _generate_homebrew_formula(self) -> str:
        """生成Homebrew Formula"""
        store_config = self.config['stores']['homebrew']
        
        return f"""class {store_config['formula_name'].replace('-', '').title()} < Formula
  desc "{self.metadata.short_description}"
  homepage "{self.metadata.website}"
  url "https://github.com/ai-code-generator/ai-code-generator/archive/v{self.metadata.version}.tar.gz"
  sha256 ""  # 需要计算实际的SHA256
  license "{self.metadata.license}"

  depends_on "python@3.9"

  def install
    virtualenv_install_with_resources
  end

  test do
    system "#{bin}/ai-code-generator", "--version"
  end
end"""
    
    def publish_all_enabled_stores(self) -> Dict[AppStore, PublishResult]:
        """发布到所有启用的应用商店"""
        results = {}
        
        # 检查每个商店的配置并发布
        store_methods = {
            AppStore.MICROSOFT_STORE: self.publish_to_microsoft_store,
            AppStore.SNAP_STORE: self.publish_to_snap_store,
            AppStore.FLATHUB: self.publish_to_flathub,
            AppStore.CHOCOLATEY: self.publish_to_chocolatey,
            AppStore.HOMEBREW: self.publish_to_homebrew
        }
        
        for store, method in store_methods.items():
            store_key = store.value
            if self.config['stores'].get(store_key, {}).get('enabled', False):
                print(f"正在发布到 {store.value}...")
                results[store] = method()
            else:
                results[store] = PublishResult(
                    store=store,
                    status=PublishStatus.FAILED,
                    submission_id="",
                    message="未启用"
                )
        
        return results
    
    def generate_store_assets(self):
        """生成应用商店资源文件"""
        assets_dir = os.path.join(self.project_path, 'store_assets')
        os.makedirs(assets_dir, exist_ok=True)
        
        # 生成不同尺寸的图标
        icon_sizes = [16, 32, 44, 48, 64, 71, 88, 128, 150, 256, 310, 512, 1024]
        
        print("📱 生成应用商店资源文件...")
        print("请确保在assets/目录下有以下文件:")
        for size in icon_sizes:
            print(f"  - icon-{size}.png")
        
        print("  - screenshot-1.png (1920x1080)")
        print("  - screenshot-2.png (1920x1080)")
        print("  - screenshot-3.png (1920x1080)")
        print("  - banner.png (1920x1080)")
        print("  - feature-graphic.png (1024x500)")
        
        # 生成desktop文件
        desktop_content = f"""[Desktop Entry]
Type=Application
Name={self.metadata.name}
Comment={self.metadata.short_description}
Exec=ai-code-generator
Icon=ai-code-generator
Categories=Development;IDE;
Keywords={';'.join(self.metadata.keywords)};
StartupNotify=true
"""
        
        with open(os.path.join(assets_dir, 'ai-code-generator.desktop'), 'w') as f:
            f.write(desktop_content)
        
        print("✅ 应用商店资源文件模板已生成!")
    
    def validate_store_requirements(self, store: AppStore) -> List[str]:
        """验证应用商店要求"""
        issues = []
        
        # 通用验证
        if not self.metadata.name:
            issues.append("应用名称不能为空")
        
        if not self.metadata.description or len(self.metadata.description) < 50:
            issues.append("应用描述至少需要50个字符")
        
        if not os.path.exists(self.metadata.icon):
            issues.append(f"图标文件不存在: {self.metadata.icon}")
        
        # 商店特定验证
        if store == AppStore.MICROSOFT_STORE:
            store_config = self.config['stores']['microsoft_store']
            if not store_config.get('tenant_id'):
                issues.append("Microsoft Store需要配置tenant_id")
            if not store_config.get('client_id'):
                issues.append("Microsoft Store需要配置client_id")
        
        elif store == AppStore.GOOGLE_PLAY:
            store_config = self.config['stores']['google_play']
            if not store_config.get('service_account_key'):
                issues.append("Google Play需要配置service_account_key")
        
        return issues
    
    def create_publish_files(self):
        """创建发布相关文件"""
        # 创建配置文件
        config_path = os.path.join(self.project_path, 'publish_config.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        # 创建元数据文件
        metadata_dict = {
            'name': self.metadata.name,
            'description': self.metadata.description,
            'short_description': self.metadata.short_description,
            'version': self.metadata.version,
            'category': self.metadata.category,
            'keywords': self.metadata.keywords,
            'screenshots': self.metadata.screenshots,
            'icon': self.metadata.icon,
            'website': self.metadata.website,
            'support_url': self.metadata.support_url,
            'privacy_policy': self.metadata.privacy_policy,
            'license': self.metadata.license,
            'age_rating': self.metadata.age_rating,
            'languages': self.metadata.languages,
            'system_requirements': self.metadata.system_requirements
        }
        
        metadata_path = os.path.join(self.project_path, 'app_metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata_dict, f, indent=2, ensure_ascii=False)
        
        # 生成应用商店资源
        self.generate_store_assets()
        
        print("✅ 应用商店发布文件创建完成!")
        print("📁 生成的文件:")
        print("  - publish_config.json (发布配置)")
        print("  - app_metadata.json (应用元数据)")
        print("  - store_assets/ (商店资源文件)")