# 代码生成器模块

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QTextEdit, QComboBox, QProgressBar, QMessageBox, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QSyntaxHighlighter, QTextCharFormat, QColor

class PythonSyntaxHighlighter(QSyntaxHighlighter):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 这里可以实现Python语法高亮
        pass

class CodeGeneratorWidget(QWidget):
    code_generated = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.architecture = None
        self.api_providers = [
            # 国外主流AI提供商
            'OpenAI GPT-4',
            'Anthropic Claude',
            'Google Gemini',
            'Microsoft Azure OpenAI',
            'Cohere Command',
            'Hugging Face',
            'Replicate',
            'Together AI',
            'Fireworks AI',
            'Groq',
            'Perplexity AI',
            'Mistral AI',
            
            # 国内主流AI提供商
            '百度文心一言',
            '阿里通义千问',
            '腾讯混元大模型',
            'DeepSeek',
            '月之暗面',
            '智谱清言',
            '讯飞星火',
            '商汤日日新',
            'MiniMax',
            '百川智能',
            '零一万物',
            '阶跃星辰',
            '字节豆包',
            '百度千帆',
            '阿里灵积'
        ]
        self.init_ui()

    def init_ui(self):
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题
        title_label = QLabel('代码生成器')
        title_label.setFont(QFont('SimHei', 14, QFont.Bold))
        main_layout.addWidget(title_label)

        # 创建API提供商选择
        api_layout = QHBoxLayout()
        api_label = QLabel('API提供商:')
        self.api_combo = QComboBox()
        self.api_combo.addItems(self.api_providers)
        api_layout.addWidget(api_label)
        api_layout.addWidget(self.api_combo)

        # API密钥输入
        key_label = QLabel('API密钥:')
        self.key_edit = QTextEdit()
        self.key_edit.setMaximumHeight(40)
        api_layout.addWidget(key_label)
        api_layout.addWidget(self.key_edit)
        main_layout.addLayout(api_layout)

        # 创建请求输入和代码输出分割器
        splitter = QSplitter(Qt.Vertical)

        # 请求输入区域
        request_widget = QWidget()
        request_layout = QVBoxLayout(request_widget)
        request_label = QLabel('生成请求:')
        self.request_edit = QTextEdit()
        self.request_edit.setPlaceholderText('请输入您的代码生成请求...')
        request_layout.addWidget(request_label)
        request_layout.addWidget(self.request_edit)
        splitter.addWidget(request_widget)

        # 代码输出区域
        code_widget = QWidget()
        code_layout = QVBoxLayout(code_widget)
        code_label = QLabel('生成的代码:')
        self.code_edit = QTextEdit()
        self.code_edit.setReadOnly(True)
        self.highlighter = PythonSyntaxHighlighter(self.code_edit.document())
        code_layout.addWidget(code_label)
        code_layout.addWidget(self.code_edit)
        splitter.addWidget(code_widget)

        # 设置分割器初始大小
        splitter.setSizes([300, 400])
        main_layout.addWidget(splitter)

        # 创建生成按钮和进度条
        action_layout = QHBoxLayout()
        self.generate_btn = QPushButton('生成代码')
        self.generate_btn.clicked.connect(self.generate_code)
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        action_layout.addWidget(self.generate_btn)
        action_layout.addWidget(self.progress_bar)
        main_layout.addLayout(action_layout)

    def set_architecture(self, architecture):
        """设置架构"""
        self.architecture = architecture
        # 更新请求编辑框，加入架构信息
        if self.architecture:
            arch_info = f"架构名称: {self.architecture['name']}\n"
            arch_info += f"模块: {', '.join(self.architecture['modules'])}\n"
            arch_info += f"依赖: {', '.join(self.architecture['dependencies'])}\n\n"
            self.request_edit.setText(arch_info + "请在此添加详细的代码生成要求...")

    def generate_code(self):
        """生成代码"""
        # 检查是否设置了架构
        if not self.architecture:
            QMessageBox.warning(self, '警告', '请先在架构设计器中确认架构!')
            return

        # 检查API密钥
        api_key = self.key_edit.toPlainText().strip()
        if not api_key:
            QMessageBox.warning(self, '警告', '请输入API密钥!')
            return

        # 检查生成请求
        request = self.request_edit.toPlainText().strip()
        if not request:
            QMessageBox.warning(self, '警告', '请输入生成请求!')
            return

        # 调用AI API生成代码
        self.progress_bar.setValue(30)
        # 为了展示进度，我们添加一些日志到状态栏
        try:
            main_window = self.parent().parent().parent().parent()
            main_window.statusBar().showMessage('正在调用AI API生成代码...')
        except:
            pass  # 如果无法获取主窗口，忽略状态栏更新

        # 获取父窗口的API集成器
        try:
            main_window = self.parent().parent().parent().parent()
            api_integrator = main_window.api_integrator

            # 设置API密钥
            provider = self.api_combo.currentText()
            api_key = self.key_edit.toPlainText().strip()
            api_integrator.set_api_key(provider, api_key)

            # 调用API生成代码
            request = self.request_edit.toPlainText().strip()
            code, message = api_integrator.generate_code(provider, request, self.architecture)
        except Exception as e:
            code, message = None, f'获取API集成器失败: {str(e)}'

        if code:
            self.code_edit.setText(code)
            self.progress_bar.setValue(100)
            # 发送代码生成信号
            self.code_generated.emit(code)
            QMessageBox.information(self, '成功', '代码生成完成!')
        else:
            self.progress_bar.setValue(0)
            QMessageBox.critical(self, '失败', f'代码生成失败: {message}')