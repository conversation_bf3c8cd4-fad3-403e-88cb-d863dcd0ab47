# 现代化用户界面
import sys
import os
import requests
import asyncio
import aiohttp
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import json
import threading
import time
import subprocess
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# API相关类
class APIProvider(Enum):
    """API提供商"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    HUGGINGFACE = "huggingface"
    COHERE = "cohere"
    DEEPSEEK = "deepseek"
    MOONSHOT = "moonshot"
    ZHIPU = "zhipu"
    BAIDU = "baidu"
    ALIBABA = "alibaba"
    TENCENT = "tencent"
    
    @staticmethod
    def get_default():
        """获取默认API提供商"""
        try:
            with open("api_configs.json", "r", encoding="utf-8") as f:
                config = json.load(f)
                for provider in APIProvider:
                    if config.get(provider.value, {}).get("enabled", False):
                        return provider
        except Exception:
            pass
        return APIProvider.OPENAI  # 默认使用OpenAI
        
    @staticmethod
    def get_display_name(provider: 'APIProvider') -> str:
        """获取提供商的显示名称"""
        display_names = {
            APIProvider.OPENAI: "OpenAI",
            APIProvider.ANTHROPIC: "Anthropic",
            APIProvider.GOOGLE: "Google AI",
            APIProvider.AZURE: "Azure OpenAI",
            APIProvider.HUGGINGFACE: "Hugging Face",
            APIProvider.COHERE: "Cohere",
            APIProvider.DEEPSEEK: "DeepSeek",
            APIProvider.MOONSHOT: "MoonShot",
            APIProvider.ZHIPU: "智谱AI",
            APIProvider.BAIDU: "百度文心",
            APIProvider.ALIBABA: "阿里通义",
            APIProvider.TENCENT: "腾讯混元"
        }
        return display_names.get(provider, provider.value)

@dataclass
class APIConfig:
    """API配置"""
    provider: APIProvider
    api_key: str
    base_url: str
    model: str
    max_tokens: int = 4096
    temperature: float = 0.7
    enabled: bool = True

class APIManager:
    """API管理器"""
    
    def __init__(self):
        self.configs: Dict[APIProvider, APIConfig] = {}
        self.default_provider: Optional[APIProvider] = None
        self.current_provider: Optional[APIProvider] = None
        self.load_configs()
    
    def set_current_provider(self, provider_name: str) -> bool:
        """设置当前API提供商
        
        Args:
            provider_name: API提供商名称
            
        Returns:
            bool: 是否设置成功
        """
        try:
            provider = APIProvider(provider_name)
            if provider in self.configs:
                self.current_provider = provider
                self.configs[provider].enabled = True
                # 禁用其他提供商
                for p in self.configs:
                    if p != provider:
                        self.configs[p].enabled = False
                return True
        except Exception:
            pass
        return False
    
    def load_configs(self):
        """加载API配置"""
        try:
            with open('api_configs.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                for provider_name, config_data in data.items():
                    try:
                        provider = APIProvider(provider_name)
                        self.configs[provider] = APIConfig(
                            provider=provider,
                            api_key=config_data.get('api_key', ''),
                            base_url=config_data.get('base_url', '').rstrip('/'),  # 移除尾部斜杠
                            model=config_data.get('model', ''),
                            max_tokens=config_data.get('max_tokens', 4096),
                            temperature=config_data.get('temperature', 0.7),
                            enabled=config_data.get('enabled', True)
                        )
                        # 如果配置启用，设置为当前提供商
                        if config_data.get('enabled', False):
                            self.current_provider = provider
                    except ValueError:
                        print(f"警告: 未知的API提供商 {provider_name}")
                        continue
        except FileNotFoundError:
            self.create_default_configs()
            
    def save_configs(self):
        """保存API配置到文件"""
        config_data = {}
        for provider, config in self.configs.items():
            config_data[provider.value] = {
                'api_key': config.api_key,
                'base_url': config.base_url,
                'model': config.model,
                'max_tokens': config.max_tokens,
                'temperature': config.temperature,
                'enabled': config.enabled
            }
        
        try:
            with open('api_configs.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            return False
            
    def get_config(self, provider: APIProvider) -> Optional[APIConfig]:
        """获取指定提供商的配置"""
        return self.configs.get(provider)
    
    def update_config(self, provider: APIProvider, **kwargs) -> bool:
        """更新提供商配置
        
        Args:
            provider: API提供商
            **kwargs: 要更新的配置项
            
        Returns:
            bool: 是否更新成功
        """
        if provider not in self.configs:
            return False
            
        config = self.configs[provider]
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
                
        return self.save_configs()
    
    def create_default_configs(self):
        """创建默认配置"""
        default_configs = {
            APIProvider.OPENAI: APIConfig(
                provider=APIProvider.OPENAI,
                api_key="",
                base_url="https://api.openai.com/v1",
                model="gpt-3.5-turbo"
            ),
            APIProvider.ANTHROPIC: APIConfig(
                provider=APIProvider.ANTHROPIC,
                api_key="",
                base_url="https://api.anthropic.com",
                model="claude-3-sonnet-20240229"
            ),
            APIProvider.GOOGLE: APIConfig(
                provider=APIProvider.GOOGLE,
                api_key="",
                base_url="https://generativelanguage.googleapis.com/v1beta",
                model="gemini-pro"
            ),
            APIProvider.AZURE: APIConfig(
                provider=APIProvider.AZURE,
                api_key="",
                base_url="https://your-resource.openai.azure.com",
                model="gpt-35-turbo"
            ),
            APIProvider.HUGGINGFACE: APIConfig(
                provider=APIProvider.HUGGINGFACE,
                api_key="",
                base_url="https://api-inference.huggingface.co/models",
                model="microsoft/DialoGPT-medium"
            ),
            APIProvider.COHERE: APIConfig(
                provider=APIProvider.COHERE,
                api_key="",
                base_url="https://api.cohere.ai/v1",
                model="command"
            ),
            APIProvider.DEEPSEEK: APIConfig(
                provider=APIProvider.DEEPSEEK,
                api_key="",
                base_url="https://api.deepseek.com/v1",
                model="deepseek-chat"
            ),
            APIProvider.MOONSHOT: APIConfig(
                provider=APIProvider.MOONSHOT,
                api_key="",
                base_url="https://api.moonshot.cn/v1",
                model="moonshot-v1-8k"
            ),
            APIProvider.ZHIPU: APIConfig(
                provider=APIProvider.ZHIPU,
                api_key="",
                base_url="https://open.bigmodel.cn/api/paas/v4",
                model="glm-4"
            ),
            APIProvider.BAIDU: APIConfig(
                provider=APIProvider.BAIDU,
                api_key="",
                base_url="https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop",
                model="ernie-bot-turbo"
            ),
            APIProvider.ALIBABA: APIConfig(
                provider=APIProvider.ALIBABA,
                api_key="",
                base_url="https://dashscope.aliyuncs.com/api/v1",
                model="qwen-turbo"
            ),
            APIProvider.TENCENT: APIConfig(
                provider=APIProvider.TENCENT,
                api_key="",
                base_url="https://hunyuan.tencentcloudapi.com",
                model="hunyuan-lite"
            )
        }
        
        self.configs = default_configs
        self.save_configs()
    
    def save_configs(self):
        """保存API配置"""
        data = {}
        for provider, config in self.configs.items():
            data[provider.value] = {
                'api_key': config.api_key,
                'base_url': config.base_url,
                'model': config.model,
                'max_tokens': config.max_tokens,
                'temperature': config.temperature,
                'enabled': config.enabled
            }
        
        with open('api_configs.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def get_config(self, provider: APIProvider) -> Optional[APIConfig]:
        """获取API配置"""
        return self.configs.get(provider)
    
    def update_config(self, provider: APIProvider, config: APIConfig):
        """更新API配置"""
        self.configs[provider] = config
        self.save_configs()
    
    def get_enabled_providers(self) -> List[APIProvider]:
        """获取启用的提供商"""
        return [provider for provider, config in self.configs.items() if config.enabled]
    
    def get_proxy_url(self) -> Optional[str]:
        """获取代理URL"""
        # 检查环境变量中的代理设置
        import os
        proxy_url = (
            os.environ.get('HTTPS_PROXY') or 
            os.environ.get('HTTP_PROXY') or 
            os.environ.get('https_proxy') or 
            os.environ.get('http_proxy')
        )
        return proxy_url
    
    def set_proxy(self, proxy_url: str):
        """设置代理"""
        import os
        if proxy_url:
            os.environ['HTTPS_PROXY'] = proxy_url
            os.environ['HTTP_PROXY'] = proxy_url
        else:
            # 清除代理设置
            for key in ['HTTPS_PROXY', 'HTTP_PROXY', 'https_proxy', 'http_proxy']:
                os.environ.pop(key, None)
    
    async def test_api(self, provider: APIProvider) -> bool:
        """测试API连接"""
        config = self.get_config(provider)
        if not config or not config.api_key:
            return False
        
        try:
            if provider == APIProvider.OPENAI:
                return await self._test_openai_api(config)
            elif provider == APIProvider.ANTHROPIC:
                return await self._test_anthropic_api(config)
            elif provider == APIProvider.GOOGLE:
                return await self._test_google_api(config)
            # 添加更多API测试方法
            else:
                return await self._test_generic_api(config)
        except Exception as e:
            print(f"API测试失败 {provider.value}: {e}")
            return False
    
    async def _test_openai_api(self, config: APIConfig) -> bool:
        """测试OpenAI API"""
        headers = {
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': config.model,
            'messages': [{'role': 'user', 'content': 'Hello'}],
            'max_tokens': 10
        }
        
        # 创建连接器，支持代理
        connector = aiohttp.TCPConnector(
            ssl=False,  # 如果有SSL问题可以设置为False
            limit=30,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        # 设置超时
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        try:
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                async with session.post(
                    f"{config.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    proxy=self.get_proxy_url()  # 添加代理支持
                ) as response:
                    return response.status == 200
        except asyncio.TimeoutError:
            print(f"OpenAI API 连接超时")
            return False
        except aiohttp.ClientConnectorError as e:
            print(f"OpenAI API 连接错误: {e}")
            return False
        except Exception as e:
            print(f"OpenAI API 测试失败: {e}")
            return False
    
    async def _test_anthropic_api(self, config: APIConfig) -> bool:
        """测试Anthropic API"""
        headers = {
            'x-api-key': config.api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }
        
        data = {
            'model': config.model,
            'max_tokens': 10,
            'messages': [{'role': 'user', 'content': 'Hello'}]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{config.base_url}/v1/messages",
                headers=headers,
                json=data
            ) as response:
                return response.status == 200
    
    async def _test_google_api(self, config: APIConfig) -> bool:
        """测试Google API"""
        url = f"{config.base_url}/models/{config.model}:generateContent?key={config.api_key}"
        
        data = {
            'contents': [{
                'parts': [{'text': 'Hello'}]
            }]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                return response.status == 200
    
    async def _test_generic_api(self, config: APIConfig) -> bool:
        """通用API测试"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(config.base_url, timeout=5) as response:
                    return response.status < 500
        except:
            return False

class CodeGenerator:
    """代码生成器"""
    
    def __init__(self, api_manager: APIManager):
        self.api_manager = api_manager
    
    async def generate_code(self, prompt: str, language: str = "python", 
                          provider: Optional[APIProvider] = None,
                          with_comments: bool = True,
                          with_tests: bool = False,
                          with_docs: bool = False) -> str:
        """生成代码
        
        Args:
            prompt: 代码生成提示
            language: 编程语言
            provider: API提供商
            with_comments: 是否包含注释
            with_tests: 是否生成测试代码
            with_docs: 是否生成文档
            
        Returns:
            str: 生成的代码
            
        Raises:
            ValueError: 当没有可用的API提供商时
        """
        # 如果没有指定provider，使用当前启用的provider
        if provider is None:
            provider = self.api_manager.current_provider
            if provider is None:
                # 如果没有当前provider，尝试获取任意启用的provider
                for p, config in self.api_manager.configs.items():
                    if config.enabled:
                        provider = p
                        break
                
        if provider is None or provider not in self.api_manager.configs:
            raise ValueError("未找到可用的API提供商")
            
        config = self.api_manager.get_config(provider)
        if not config or not config.enabled:
            raise ValueError(f"API提供商 {provider.value} 未配置或未启用")
        
        # 构建提示信息
        prompt_parts = [
            f"请使用{language}语言实现以下功能：\n{prompt}\n",
            "\n要求：",
            "1. 代码必须完整可运行",
            "2. 使用最新的语言特性和最佳实践",
            "3. 包含适当的错误处理",
        ]
        
        if with_comments:
            prompt_parts.append("4. 包含清晰的中文注释，解释关键逻辑")
        
        if with_tests:
            prompt_parts.append("5. 包含单元测试代码")
            
        if with_docs:
            prompt_parts.append("6. 包含详细的文档字符串")
            
        prompt_parts.append("\n请按照以下格式返回代码：")
        prompt_parts.append("```python")
        prompt_parts.append("<在这里放置生成的代码>")
        prompt_parts.append("```")
        
        if with_tests:
            prompt_parts.append("\n测试代码：")
            prompt_parts.append("```python")
            prompt_parts.append("<在这里放置测试代码>")
            prompt_parts.append("```")
            
        full_prompt = "\n".join(prompt_parts)
        
        if provider == APIProvider.OPENAI:
            return await self._generate_with_openai(config, full_prompt)
        elif provider == APIProvider.ANTHROPIC:
            return await self._generate_with_anthropic(config, full_prompt)
        elif provider == APIProvider.GOOGLE:
            return await self._generate_with_google(config, full_prompt)
        elif provider == APIProvider.ZHIPU:
            return await self._generate_with_zhipu(config, full_prompt)
        else:
            return await self._generate_with_generic(config, full_prompt)
            
    async def _generate_with_zhipu(self, config: APIConfig, prompt: str) -> str:
        """使用智谱AI生成代码"""
        import time
        import jwt  # 需要安装: pip install pyjwt
        import json
        
        # 生成API请求所需的认证token
        def generate_token(api_key: str) -> str:
            try:
                # API密钥格式: "api_key_id.api_key_secret"
                if "." not in api_key:
                    raise ValueError("API密钥格式错误，应为'api_key_id.api_key_secret'格式")
                    
                api_key_id, api_key_secret = api_key.split(".")
                
                # 当前时间戳
                now = int(time.time())
                
                # JWT payload
                payload = {
                    "api_key": api_key_id,
                    "exp": now + 3600,  # 1小时过期
                    "timestamp": now
                }
                
                # 生成JWT token
                token = jwt.encode(
                    payload,
                    api_key_secret,
                    algorithm="HS256",
                    headers={"alg": "HS256", "sign_type": "SIGN"}
                )
                return token
                
            except Exception as e:
                raise ValueError(f"智谱AI API密钥格式错误或token生成失败: {str(e)}")
        
        try:
            # 准备请求头
            token = generate_token(config.api_key)
            headers = {
                "Content-Type": "application/json",
                "Authorization": token  # 智谱AI不需要 "Bearer " 前缀
            }
            
            # 准备请求体
            data = {
                "model": config.model or "glm-4",  # 默认使用 glm-4 模型
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的代码开发助手，请生成简洁、高效、符合最佳实践的代码。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": min(max(config.temperature or 0.7, 0.1), 0.9),  # 限制在0.1-0.9之间
                "max_tokens": min(config.max_tokens or 4096, 8192)  # 限制最大token数
            }
            
            url = f"{config.base_url}/chat/completions"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise ValueError(f"智谱AI API调用失败 (HTTP {response.status}): {error_text}")
                    
                    result = await response.json()
                    
                    # 提取生成的代码
                    if "choices" in result and len(result["choices"]) > 0:
                        generated_code = result["choices"][0]["message"]["content"]
                        # 清理代码（去除可能的代码块标记）
                        generated_code = generated_code.strip()
                        if generated_code.startswith("```"):
                            generated_code = "\n".join(generated_code.split("\n")[1:-1])
                        return generated_code
                    else:
                        raise ValueError("智谱AI返回的结果格式不正确")
        except Exception as e:
            raise ValueError(f"智谱AI代码生成失败: {str(e)}")
    
    async def _generate_with_openai(self, config: APIConfig, prompt: str) -> str:
        """使用OpenAI生成代码"""
        headers = {
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': config.model,
            'messages': [{'role': 'user', 'content': prompt}],
            'max_tokens': config.max_tokens,
            'temperature': config.temperature
        }
        
        # 创建连接器，支持代理和更长的超时时间
        connector = aiohttp.TCPConnector(
            ssl=False,  # 如果有SSL问题可以设置为False
            limit=30,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        # 设置更长的超时时间
        timeout = aiohttp.ClientTimeout(total=60, connect=30)
        
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                ) as session:
                    async with session.post(
                        f"{config.base_url}/chat/completions",
                        headers=headers,
                        json=data,
                        proxy=self.api_manager.get_proxy_url()
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result['choices'][0]['message']['content']
                        elif response.status == 429:
                            # 速率限制，等待后重试
                            await asyncio.sleep(2 ** attempt)
                            continue
                        else:
                            error_text = await response.text()
                            raise Exception(f"API请求失败 (状态码: {response.status}): {error_text}")
            
            except asyncio.TimeoutError:
                if attempt == max_retries - 1:
                    raise Exception("连接超时，请检查网络连接或代理设置")
                await asyncio.sleep(1)
                continue
            
            except aiohttp.ClientConnectorError as e:
                if attempt == max_retries - 1:
                    raise Exception(f"网络连接失败: {str(e)}。请检查网络连接或配置代理")
                await asyncio.sleep(1)
                continue
            
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                await asyncio.sleep(1)
                continue
        
        raise Exception("重试次数已用完，请检查网络连接")
    
    async def _generate_with_anthropic(self, config: APIConfig, prompt: str) -> str:
        """使用Anthropic生成代码"""
        headers = {
            'x-api-key': config.api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }
        
        data = {
            'model': config.model,
            'max_tokens': config.max_tokens,
            'messages': [{'role': 'user', 'content': prompt}]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{config.base_url}/v1/messages",
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result['content'][0]['text']
                else:
                    raise Exception(f"API请求失败: {response.status}")
    
    async def _generate_with_google(self, config: APIConfig, prompt: str) -> str:
        """使用Google生成代码"""
        try:
            url = f"{config.base_url}/models/{config.model}:generateContent?key={config.api_key}"
            
            data = {
                'contents': [
                    {
                        'role': 'system',
                        'parts': [{'text': '你是一个专业的代码开发助手。请生成简洁、高效、符合最佳实践的代码，优先使用中文注释。'}]
                    },
                    {
                        'role': 'user',
                        'parts': [{'text': prompt}]
                    }
                ],
                'generationConfig': {
                    'temperature': min(max(config.temperature or 0.7, 0.1), 0.9),
                    'maxOutputTokens': min(config.max_tokens or 4096, 8192),
                    'topK': 40,
                    'topP': 0.95
                },
                'safetySettings': [
                    {
                        'category': 'HARM_CATEGORY_HARASSMENT',
                        'threshold': 'BLOCK_NONE'
                    },
                    {
                        'category': 'HARM_CATEGORY_HATE_SPEECH',
                        'threshold': 'BLOCK_NONE'
                    },
                    {
                        'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                        'threshold': 'BLOCK_NONE'
                    },
                    {
                        'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
                        'threshold': 'BLOCK_NONE'
                    }
                ]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if 'candidates' in result and len(result['candidates']) > 0:
                            return result['candidates'][0]['content']['parts'][0]['text']
                        else:
                            raise ValueError("Google API返回的结果格式不正确")
                    else:
                        raise ValueError(f"Google API请求失败: HTTP {response.status}")
        except Exception as e:
            raise ValueError(f"Google API代码生成失败: {str(e)}")
    
    async def _generate_with_generic(self, config: APIConfig, prompt: str) -> str:
        """通用代码生成"""
        # 这里可以实现其他API的调用逻辑
        return f"# 使用 {config.provider.value} 生成的代码\n# TODO: 实现具体的API调用逻辑"

class ProjectManager:
    """项目管理器"""
    
    def __init__(self):
        self.current_project = None
        self.projects_dir = "projects"
        os.makedirs(self.projects_dir, exist_ok=True)
    
    def create_project(self, name: str, template: str = "basic") -> str:
        """创建新项目"""
        project_path = os.path.join(self.projects_dir, name)
        os.makedirs(project_path, exist_ok=True)
        
        # 创建项目配置
        config = {
            'name': name,
            'template': template,
            'created_at': time.time(),
            'language': 'python',
            'framework': 'none',
            'dependencies': []
        }
        
        with open(os.path.join(project_path, 'project.json'), 'w') as f:
            json.dump(config, f, indent=2)
        
        # 创建基本文件结构
        if template == "flask":
            self._create_flask_template(project_path)
        elif template == "django":
            self._create_django_template(project_path)
        elif template == "fastapi":
            self._create_fastapi_template(project_path)
        else:
            self._create_basic_template(project_path)
        
        self.current_project = project_path
        return project_path
    
    def _create_basic_template(self, project_path: str):
        """创建基本模板"""
        with open(os.path.join(project_path, 'main.py'), 'w') as f:
            f.write('''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目主文件
"""

def main():
    """主函数"""
    print("Hello, World!")

if __name__ == "__main__":
    main()
''')
        
        with open(os.path.join(project_path, 'requirements.txt'), 'w') as f:
            f.write('# 项目依赖\n')
        
        with open(os.path.join(project_path, 'README.md'), 'w') as f:
            f.write('# 项目说明\n\n这是一个基本的Python项目。\n')
    
    def _create_flask_template(self, project_path: str):
        """创建Flask模板"""
        with open(os.path.join(project_path, 'app.py'), 'w') as f:
            f.write('''from flask import Flask, render_template, jsonify

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/hello')
def api_hello():
    return jsonify({'message': 'Hello from Flask API!'})

if __name__ == '__main__':
    app.run(debug=True)
''')
        
        # 创建模板目录
        templates_dir = os.path.join(project_path, 'templates')
        os.makedirs(templates_dir, exist_ok=True)
        
        with open(os.path.join(templates_dir, 'index.html'), 'w') as f:
            f.write('''<!DOCTYPE html>
<html>
<head>
    <title>Flask App</title>
</head>
<body>
    <h1>Welcome to Flask!</h1>
    <p>Your Flask application is running.</p>
</body>
</html>
''')
        
        with open(os.path.join(project_path, 'requirements.txt'), 'w') as f:
            f.write('Flask==2.3.3\n')
    
    def _create_fastapi_template(self, project_path: str):
        """创建FastAPI模板"""
        with open(os.path.join(project_path, 'main.py'), 'w') as f:
            f.write('''from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI(title="FastAPI Application", version="1.0.0")

class Item(BaseModel):
    name: str
    description: str = None
    price: float
    tax: float = None

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/items/{item_id}")
async def read_item(item_id: int, q: str = None):
    return {"item_id": item_id, "q": q}

@app.post("/items/")
async def create_item(item: Item):
    return item

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
''')
        
        with open(os.path.join(project_path, 'requirements.txt'), 'w') as f:
            f.write('fastapi==0.104.1\nuvicorn==0.24.0\n')
    
    def load_project(self, project_path: str) -> dict:
        """加载项目"""
        config_path = os.path.join(project_path, 'project.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        return {}
    
    def get_projects(self) -> List[str]:
        """获取所有项目"""
        projects = []
        if os.path.exists(self.projects_dir):
            for item in os.listdir(self.projects_dir):
                project_path = os.path.join(self.projects_dir, item)
                if os.path.isdir(project_path) and os.path.exists(os.path.join(project_path, 'project.json')):
                    projects.append(item)
        return projects

class ModernTheme:
    """现代化主题管理"""
    
    THEMES = {
        'dark': {
            'primary': '#2D3748',
            'secondary': '#4A5568',
            'accent': '#3182CE',
            'success': '#38A169',
            'warning': '#D69E2E',
            'error': '#E53E3E',
            'background': '#1A202C',
            'surface': '#2D3748',
            'text': '#F7FAFC',
            'text_secondary': '#A0AEC0',
            'border': '#4A5568'
        },
        'light': {
            'primary': '#3182CE',
            'secondary': '#718096',
            'accent': '#3182CE',
            'success': '#38A169',
            'warning': '#D69E2E',
            'error': '#E53E3E',
            'background': '#FFFFFF',
            'surface': '#F7FAFC',
            'text': '#1A202C',
            'text_secondary': '#718096',
            'border': '#E2E8F0'
        },
        'blue': {
            'primary': '#1E3A8A',
            'secondary': '#3B82F6',
            'accent': '#60A5FA',
            'success': '#10B981',
            'warning': '#F59E0B',
            'error': '#EF4444',
            'background': '#F8FAFC',
            'surface': '#FFFFFF',
            'text': '#1E293B',
            'text_secondary': '#64748B',
            'border': '#CBD5E1'
        }
    }
    
    @classmethod
    def get_stylesheet(cls, theme_name: str = 'dark') -> str:
        """获取主题样式表"""
        theme = cls.THEMES.get(theme_name, cls.THEMES['dark'])
        
        return f"""
        /* 全局样式 */
        QMainWindow {{
            background-color: {theme['background']};
            color: {theme['text']};
            font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
        }}
        
        /* 工具栏 */
        QToolBar {{
            background-color: {theme['surface']};
            border: none;
            padding: 8px;
            spacing: 4px;
        }}
        
        QToolBar QToolButton {{
            background-color: transparent;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 2px;
            color: {theme['text']};
        }}
        
        QToolBar QToolButton:hover {{
            background-color: {theme['secondary']};
        }}
        
        QToolBar QToolButton:pressed {{
            background-color: {theme['accent']};
        }}
        
        /* 菜单栏 */
        QMenuBar {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border: none;
            padding: 4px;
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {theme['secondary']};
        }}
        
        QMenu {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border: 1px solid {theme['border']};
            border-radius: 8px;
            padding: 4px;
        }}
        
        QMenu::item {{
            padding: 8px 16px;
            border-radius: 4px;
        }}
        
        QMenu::item:selected {{
            background-color: {theme['accent']};
        }}
        
        /* 按钮 */
        QPushButton {{
            background-color: {theme['primary']};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-weight: 500;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: {theme['accent']};
        }}
        
        QPushButton:pressed {{
            background-color: {theme['secondary']};
        }}
        
        QPushButton:disabled {{
            background-color: {theme['border']};
            color: {theme['text_secondary']};
        }}
        
        /* 成功按钮 */
        QPushButton[class="success"] {{
            background-color: {theme['success']};
        }}
        
        QPushButton[class="success"]:hover {{
            background-color: #48BB78;
        }}
        
        /* 警告按钮 */
        QPushButton[class="warning"] {{
            background-color: {theme['warning']};
        }}
        
        QPushButton[class="warning"]:hover {{
            background-color: #ECC94B;
        }}
        
        /* 错误按钮 */
        QPushButton[class="error"] {{
            background-color: {theme['error']};
        }}
        
        QPushButton[class="error"]:hover {{
            background-color: #F56565;
        }}
        
        /* 输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {theme['accent']};
        }}
        
        /* 组合框 */
        QComboBox {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 8px 12px;
            min-width: 120px;
        }}
        
        QComboBox:focus {{
            border-color: {theme['accent']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzk5QTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
        }}
        
        /* 标签页 */
        QTabWidget::pane {{
            background-color: {theme['surface']};
            border: 1px solid {theme['border']};
            border-radius: 8px;
            margin-top: -1px;
        }}
        
        QTabBar::tab {{
            background-color: transparent;
            color: {theme['text_secondary']};
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border-bottom: 2px solid {theme['accent']};
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {theme['secondary']};
        }}
        
        /* 滚动条 */
        QScrollBar:vertical {{
            background-color: {theme['background']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {theme['secondary']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {theme['accent']};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        /* 进度条 */
        QProgressBar {{
            background-color: {theme['border']};
            border: none;
            border-radius: 6px;
            height: 8px;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {theme['accent']};
            border-radius: 6px;
        }}
        
        /* 状态栏 */
        QStatusBar {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border-top: 1px solid {theme['border']};
            padding: 4px;
        }}
        
        /* 分割器 */
        QSplitter::handle {{
            background-color: {theme['border']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 2px;
        }}
        
        QSplitter::handle:vertical {{
            height: 2px;
        }}
        
        /* 树形视图 */
        QTreeView {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border: 1px solid {theme['border']};
            border-radius: 6px;
            selection-background-color: {theme['accent']};
        }}
        
        QTreeView::item {{
            padding: 4px;
            border-radius: 4px;
        }}
        
        QTreeView::item:hover {{
            background-color: {theme['secondary']};
        }}
        
        QTreeView::item:selected {{
            background-color: {theme['accent']};
        }}
        
        /* 列表视图 */
        QListView {{
            background-color: {theme['surface']};
            color: {theme['text']};
            border: 1px solid {theme['border']};
            border-radius: 6px;
            selection-background-color: {theme['accent']};
        }}
        
        QListView::item {{
            padding: 8px;
            border-radius: 4px;
        }}
        
        QListView::item:hover {{
            background-color: {theme['secondary']};
        }}
        
        QListView::item:selected {{
            background-color: {theme['accent']};
        }}
        
        /* 分组框 */
        QGroupBox {{
            color: {theme['text']};
            border: 2px solid {theme['border']};
            border-radius: 8px;
            margin-top: 12px;
            font-weight: 500;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: {theme['background']};
        }}
        
        /* 复选框 */
        QCheckBox {{
            color: {theme['text']};
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {theme['border']};
            border-radius: 4px;
            background-color: {theme['surface']};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {theme['accent']};
            border-color: {theme['accent']};
        }}
        
        /* 单选框 */
        QRadioButton {{
            color: {theme['text']};
            spacing: 8px;
        }}
        
        QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {theme['border']};
            border-radius: 9px;
            background-color: {theme['surface']};
        }}
        
        QRadioButton::indicator:checked {{
            background-color: {theme['accent']};
            border-color: {theme['accent']};
        }}
        
        /* 滑块 */
        QSlider::groove:horizontal {{
            background-color: {theme['border']};
            height: 6px;
            border-radius: 3px;
        }}
        
        QSlider::handle:horizontal {{
            background-color: {theme['accent']};
            width: 18px;
            height: 18px;
            border-radius: 9px;
            margin: -6px 0;
        }}
        
        QSlider::handle:horizontal:hover {{
            background-color: {theme['primary']};
        }}
        
        /* 工具提示 */
        QToolTip {{
            background-color: {theme['secondary']};
            color: {theme['text']};
            border: 1px solid {theme['border']};
            border-radius: 6px;
            padding: 8px;
            font-size: 12px;
        }}
        """

class AnimatedButton(QPushButton):
    """带动画效果的按钮"""
    
    def __init__(self, text: str, parent=None):
        super().__init__(text, parent)
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 添加阴影效果
        self.shadow = QGraphicsDropShadowEffect()
        self.shadow.setBlurRadius(10)
        self.shadow.setColor(QColor(0, 0, 0, 50))
        self.shadow.setOffset(0, 2)
        self.setGraphicsEffect(self.shadow)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        self.shadow.setBlurRadius(15)
        self.shadow.setOffset(0, 4)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        self.shadow.setBlurRadius(10)
        self.shadow.setOffset(0, 2)

class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            ModernCard {
                background-color: #2D3748;
                border-radius: 12px;
                border: 1px solid #4A5568;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        # 布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        if title:
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: 600;
                    color: #F7FAFC;
                    margin-bottom: 8px;
                }
            """)
            layout.addWidget(title_label)
    
    def addWidget(self, widget):
        """添加组件到卡片"""
        self.layout().addWidget(widget)

class ModernProgressBar(QProgressBar):
    """现代化进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QProgressBar {
                background-color: #4A5568;
                border: none;
                border-radius: 8px;
                height: 16px;
                text-align: center;
                color: white;
                font-weight: 500;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3182CE, stop:1 #60A5FA);
                border-radius: 8px;
            }
        """)
        
        # 动画效果
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def setValueAnimated(self, value: int):
        """带动画的设置值"""
        self.animation.setStartValue(self.value())
        self.animation.setEndValue(value)
        self.animation.start()

class ModernNotification(QWidget):
    """现代化通知组件"""
    
    def __init__(self, message: str, type_: str = "info", parent=None):
        super().__init__(parent)
        self.setFixedHeight(60)
        self.setAttribute(Qt.WA_DeleteOnClose)
        
        # 设置样式
        colors = {
            'info': '#3182CE',
            'success': '#38A169',
            'warning': '#D69E2E',
            'error': '#E53E3E'
        }
        
        color = colors.get(type_, colors['info'])
        self.setStyleSheet(f"""
            ModernNotification {{
                background-color: {color};
                border-radius: 8px;
                border: none;
            }}
        """)
        
        # 布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        
        # 图标
        icon_label = QLabel()
        icons = {
            'info': '🛈',
            'success': '✓',
            'warning': '⚠',
            'error': '✗'
        }
        icon_label.setText(icons.get(type_, icons['info']))
        icon_label.setStyleSheet("font-size: 18px; color: white;")
        layout.addWidget(icon_label)
        
        # 消息
        message_label = QLabel(message)
        message_label.setStyleSheet("color: white; font-weight: 500;")
        layout.addWidget(message_label)
        
        layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(24, 24)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        # 自动关闭定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.close)
        self.timer.start(5000)  # 5秒后自动关闭
        
        # 显示动画
        self.show_animation = QPropertyAnimation(self, b"geometry")
        self.show_animation.setDuration(300)
        self.show_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def showNotification(self, parent_widget):
        """显示通知"""
        # 设置初始位置（屏幕外）
        parent_rect = parent_widget.rect()
        start_rect = QRect(parent_rect.width(), 10, self.width(), self.height())
        end_rect = QRect(parent_rect.width() - self.width() - 20, 10, self.width(), self.height())
        
        self.setGeometry(start_rect)
        self.show()
        
        # 动画进入
        self.show_animation.setStartValue(start_rect)
        self.show_animation.setEndValue(end_rect)
        self.show_animation.start()

class ModernSidebar(QWidget):
    """现代化侧边栏"""

    itemClicked = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.default_width = 250
        self.collapsed_width = 60
        self.is_collapsed = False
        self.items = {}

        self.setFixedWidth(self.default_width)
        self.setStyleSheet("""
            ModernSidebar {
                background-color: #2D3748;
                border-right: 1px solid #4A5568;
            }
        """)

        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        # 创建新的布局
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)

        # 标题
        title_label = QLabel("AI Code Generator")
        title_label.setStyleSheet("""
            QLabel {
                background-color: #1A202C;
                color: #F7FAFC;
                font-size: 16px;
                font-weight: 600;
                padding: 20px;
                border-bottom: 1px solid #4A5568;
            }
        """)
        self.main_layout.addWidget(title_label)

        # 菜单项容器
        self.menu_container = QWidget()
        self.menu_layout = QVBoxLayout(self.menu_container)
        self.menu_layout.setContentsMargins(0, 0, 0, 0)
        self.menu_layout.setSpacing(2)

        # 添加菜单项
        self.add_menu_item("🏠", "主页", "home")
        self.add_menu_item("🤖", "AI助手", "ai")
        self.add_menu_item("🔧", "代码生成", "generate")
        self.add_menu_item("🐛", "调试器", "debug")
        self.add_menu_item("📦", "打包器", "package")
        self.add_menu_item("🚀", "部署", "deploy")
        self.add_menu_item("⚙️", "设置", "settings")

        self.main_layout.addWidget(self.menu_container)
        self.main_layout.addStretch()

        # 底部信息
        info_label = QLabel("v2.0.0")
        info_label.setStyleSheet("""
            QLabel {
                color: #A0AEC0;
                font-size: 12px;
                padding: 20px;
                border-top: 1px solid #4A5568;
            }
        """)
        self.main_layout.addWidget(info_label)
    
    def addMenuItem(self, icon: str, text: str, key: str):
        """添加菜单项 (兼容性方法)"""
        # 这个方法现在被add_menu_item替代，但保留以兼容现有代码
        pass

    def add_menu_item(self, icon: str, text: str, key: str):
        """添加菜单项"""
        item = QPushButton(f"{icon}  {text}")
        item.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #E2E8F0;
                border: none;
                text-align: left;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #4A5568;
                color: #FFFFFF;
            }
            QPushButton:pressed {
                background-color: #3182CE;
            }
        """)

        item.clicked.connect(lambda: self.on_item_clicked(key))
        self.menu_layout.addWidget(item)
        self.items[key] = item

    def on_item_clicked(self, key: str):
        """菜单项点击事件"""
        # 发送信号
        self.itemClicked.emit(key)
    
    def select_item(self, key: str):
        """选中菜单项"""
        # 清除之前的选中状态
        for item in self.items.values():
            item.setStyleSheet(item.styleSheet().replace("background-color: #3182CE;", ""))

        # 设置新的选中状态
        if key in self.items:
            item = self.items[key]
            current_style = item.styleSheet()
            if "background-color: #3182CE;" not in current_style:
                item.setStyleSheet(current_style + "QPushButton { background-color: #3182CE; }")

        # 发送信号
        self.itemClicked.emit(key)

    def collapse(self):
        """折叠侧边栏"""
        if not self.is_collapsed:
            self.is_collapsed = True
            self.setFixedWidth(self.collapsed_width)

            # 隐藏文本，只显示图标
            for item in self.items.values():
                if hasattr(item, 'text_label'):
                    item.text_label.hide()

    def expand(self):
        """展开侧边栏"""
        if self.is_collapsed:
            self.is_collapsed = False
            self.setFixedWidth(self.default_width)

            # 显示文本
            for item in self.items.values():
                if hasattr(item, 'text_label'):
                    item.text_label.show()

    def toggle_collapse(self):
        """切换折叠状态"""
        if self.is_collapsed:
            self.expand()
        else:
            self.collapse()

class ModernMainWindow(QMainWindow):
    """现代化主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI Code Generator - 现代化界面")

        # 初始化基本属性
        self.current_theme = "dark"

        # 尝试导入布局管理器和主题管理器
        try:
            from .layout_manager import LayoutManager
            from .theme_manager import ThemeManager
            self.layout_manager = LayoutManager(self)
            self.theme_manager = ThemeManager()
        except ImportError as e:
            print(f"警告: 无法导入布局管理器: {e}")
            self.layout_manager = None
            self.theme_manager = None

        # 设置窗口属性
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 设置窗口居中
        self.center_window()

        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon("assets/icon.png"))
        except:
            pass  # 忽略图标加载失败
        
        # 初始化管理器
        self.api_manager = APIManager()
        self.code_generator = CodeGenerator(self.api_manager)
        self.project_manager = ProjectManager()
        
        # 应用主题
        self.current_theme = 'dark'
        self.applyTheme()
        
        # 创建界面
        self.setupUI()
        
    def loadDefaultAPIProvider(self):
        """加载默认API提供商设置"""
        default_provider = APIProvider.get_default()
        if default_provider in self.api_provider_actions:
            self.api_provider_actions[default_provider].setChecked(True)
            
    def onAPIProviderChanged(self, provider: APIProvider):
        """处理API提供商更改"""
        try:
            with open("api_configs.json", "r", encoding="utf-8") as f:
                config = json.load(f)
                
            # 禁用所有提供商
            for p in APIProvider:
                if p.value in config:
                    config[p.value]["enabled"] = False
                    
            # 启用选中的提供商
            if provider.value in config:
                config[provider.value]["enabled"] = True
                
            with open("api_configs.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            # 更新APIManager的当前提供商
            if hasattr(self, 'api_manager'):
                self.api_manager.set_current_provider(provider.value)
                # 更新代码生成器
                if hasattr(self, 'code_generator'):
                    self.code_generator = CodeGenerator(self.api_manager)
                
            # 更新API状态显示
            self.refresh_api_status()
                
            QMessageBox.information(self, "成功", f"已将默认API提供商设置为: {provider.value}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存API设置时出错: {str(e)}")
        
        # 居中显示
        self.centerWindow()
    
    def applyTheme(self, theme_name: str = None):
        """应用主题"""
        if theme_name:
            self.current_theme = theme_name
        else:
            theme_name = self.current_theme

        # 使用新的主题管理器
        if self.theme_manager:
            try:
                self.theme_manager.apply_theme(self, theme_name)
            except Exception as e:
                print(f"主题应用失败，使用默认样式: {e}")
                self.apply_default_theme()
        else:
            self.apply_default_theme()

    def apply_default_theme(self):
        """应用默认主题"""
        default_style = """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
        }
        QWidget {
            background-color: #ffffff;
            color: #212529;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: 500;
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
        QPushButton:pressed {
            background-color: #004085;
        }
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
        }
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #007bff;
        }
        """
        self.setStyleSheet(default_style)
    
    def setupUI(self):
        """设置用户界面"""
        if self.layout_manager:
            self.setupUI_with_layout_manager()
        else:
            self.setupUI_fallback()

    def setupUI_with_layout_manager(self):
        """使用布局管理器设置界面"""
        try:
            # 使用布局管理器创建主布局
            central_widget = self.layout_manager.create_main_layout()
            self.setCentralWidget(central_widget)

            # 创建主分割器，使用响应式比例
            main_splitter = self.layout_manager.create_splitter(Qt.Horizontal, [0.2, 0.8])
            central_widget.layout().addWidget(main_splitter)

            # 侧边栏
            self.sidebar = ModernSidebar()
            self.sidebar.itemClicked.connect(self.onSidebarItemClicked)
            self.layout_manager.apply_widget_style(self.sidebar, 'sidebar')
            self.layout_manager.responsive.register_widget('sidebar', self.sidebar)
            main_splitter.addWidget(self.sidebar)

            # 内容区域容器
            content_container = QWidget()
            self.layout_manager.apply_widget_style(content_container, 'main_area')
            content_layout = QVBoxLayout(content_container)
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(0)

            # 内容区域
            self.content_area = QStackedWidget()
            content_layout.addWidget(self.content_area)
            main_splitter.addWidget(content_container)

            # 注册分割器以保存状态
            self.main_splitter = main_splitter

        except Exception as e:
            print(f"布局管理器设置失败，使用回退方案: {e}")
            self.setupUI_fallback()
            return

        # 创建页面
        self.createPages()

        # 创建菜单栏
        self.createMenuBar()

        # 创建工具栏
        self.createToolBar()

        # 创建状态栏
        self.createStatusBar()

        # 通知容器
        self.notification_container = QWidget(self)
        self.notification_container.setGeometry(0, 0, self.width(), self.height())
        self.notification_container.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.notification_layout = QVBoxLayout(self.notification_container)
        self.notification_layout.setContentsMargins(20, 20, 20, 20)
        self.notification_layout.addStretch()

        # 恢复布局状态
        QTimer.singleShot(100, self.restore_layout_state)

    def setupUI_fallback(self):
        """回退的界面设置方案"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 侧边栏
        self.sidebar = ModernSidebar()
        self.sidebar.itemClicked.connect(self.onSidebarItemClicked)
        main_layout.addWidget(self.sidebar)

        # 内容区域
        self.content_area = QStackedWidget()
        main_layout.addWidget(self.content_area)

        # 创建页面
        self.createPages()

        # 创建菜单栏
        self.createMenuBar()

        # 创建工具栏
        self.createToolBar()

        # 创建状态栏
        self.createStatusBar()

        # 通知容器
        self.notification_container = QWidget(self)
        self.notification_container.setGeometry(0, 0, self.width(), self.height())
        self.notification_container.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.notification_layout = QVBoxLayout(self.notification_container)
        self.notification_layout.setContentsMargins(20, 20, 20, 20)
        self.notification_layout.addStretch()
    
    def createPages(self):
        """创建页面"""
        # 主页
        home_page = self.createHomePage()
        self.content_area.addWidget(home_page)
        
        # AI助手页面
        ai_page = self.createAIPage()
        self.content_area.addWidget(ai_page)
        
        # 代码生成页面
        generate_page = self.createGeneratePage()
        self.content_area.addWidget(generate_page)
        
        # 调试器页面
        debug_page = self.createDebugPage()
        self.content_area.addWidget(debug_page)
        
        # 打包器页面
        package_page = self.createPackagePage()
        self.content_area.addWidget(package_page)
        
        # 部署页面
        deploy_page = self.createDeployPage()
        self.content_area.addWidget(deploy_page)
        
        # 设置页面
        settings_page = self.createSettingsPage()
        self.content_area.addWidget(settings_page)
    
    def createHomePage(self) -> QWidget:
        """创建主页"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 欢迎标题
        title = QLabel("欢迎使用 AI Code Generator")
        title.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: 700;
                color: #F7FAFC;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # 副标题
        subtitle = QLabel("智能代码生成，让开发更高效")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #A0AEC0;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(subtitle)
        
        # 功能卡片网格
        cards_layout = QGridLayout()
        cards_layout.setSpacing(20)
        
        # 功能卡片数据
        features = [
            ("🤖", "AI代码生成", "使用先进的AI模型生成高质量代码", "ai"),
            ("🔧", "智能重构", "自动优化和重构现有代码", "refactor"),
            ("🐛", "智能调试", "AI辅助的代码调试和错误修复", "debug"),
            ("📦", "一键打包", "多平台应用打包和分发", "package"),
            ("🚀", "快速部署", "容器化部署和CI/CD集成", "deploy"),
            ("⚡", "性能优化", "代码性能分析和优化建议", "optimize")
        ]
        
        for i, (icon, title, desc, action) in enumerate(features):
            card = self.createFeatureCard(icon, title, desc, action)
            cards_layout.addWidget(card, i // 3, i % 3)
        
        layout.addLayout(cards_layout)
        layout.addStretch()
        
        return page
    
    def createFeatureCard(self, icon: str, title: str, description: str, action: str) -> ModernCard:
        """创建功能卡片"""
        card = ModernCard()
        card.setFixedHeight(180)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #3182CE;
                margin-bottom: 12px;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        card.addWidget(icon_label)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #F7FAFC;
                margin-bottom: 8px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        card.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #A0AEC0;
                margin-bottom: 16px;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        card.addWidget(desc_label)
        
        # 按钮
        btn = AnimatedButton("开始使用")
        btn.clicked.connect(lambda: self.onFeatureCardClicked(action))
        card.addWidget(btn)
        
        return card
    
    def createAIPage(self) -> QWidget:
        """创建AI助手页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("AI 智能助手")
        title.setStyleSheet("font-size: 24px; font-weight: 600; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # API状态栏
        status_card = ModernCard("API 状态")
        status_layout = QHBoxLayout()
        
        self.api_status_label = QLabel("检查API状态中...")
        status_layout.addWidget(self.api_status_label)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_api_status)
        status_layout.addWidget(refresh_btn)
        
        status_card.layout().addLayout(status_layout)
        layout.addWidget(status_card)
        
        # AI功能卡片
        cards_layout = QGridLayout()
        
        # 代码生成卡片
        code_gen_card = ModernCard("智能代码生成")
        code_gen_card.addWidget(QLabel("使用AI生成高质量代码"))
        code_gen_btn = AnimatedButton("🤖 开始生成")
        code_gen_btn.clicked.connect(self.show_code_generation_dialog)
        code_gen_card.addWidget(code_gen_btn)
        cards_layout.addWidget(code_gen_card, 0, 0)
        
        # 代码优化卡片
        code_opt_card = ModernCard("代码优化")
        code_opt_card.addWidget(QLabel("AI驱动的代码优化和重构"))
        code_opt_btn = AnimatedButton("⚡ 优化代码")
        code_opt_btn.clicked.connect(self.show_code_optimization_dialog)
        code_opt_card.addWidget(code_opt_btn)
        cards_layout.addWidget(code_opt_card, 0, 1)
        
        # 错误修复卡片
        bug_fix_card = ModernCard("智能调试")
        bug_fix_card.addWidget(QLabel("自动检测和修复代码错误"))
        bug_fix_btn = AnimatedButton("🐛 修复错误")
        bug_fix_btn.clicked.connect(self.show_bug_fix_dialog)
        bug_fix_card.addWidget(bug_fix_btn)
        cards_layout.addWidget(bug_fix_card, 1, 0)
        
        # 文档生成卡片
        doc_gen_card = ModernCard("文档生成")
        doc_gen_card.addWidget(QLabel("自动生成项目文档"))
        doc_gen_btn = AnimatedButton("📚 生成文档")
        doc_gen_btn.clicked.connect(self.show_doc_generation_dialog)
        doc_gen_card.addWidget(doc_gen_btn)
        cards_layout.addWidget(doc_gen_card, 1, 1)
        
        layout.addLayout(cards_layout)
        
        # AI对话区域
        chat_card = ModernCard("AI 对话")
        
        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("选择模型:"))
        self.model_combo = QComboBox()
        self.update_model_combo()
        model_layout.addWidget(self.model_combo)
        model_layout.addStretch()
        chat_card.layout().addLayout(model_layout)
        
        # 聊天历史
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setPlaceholderText("AI助手将在这里回复...")
        self.chat_history.setMinimumHeight(300)
        chat_card.addWidget(self.chat_history)
        
        # 输入区域
        input_layout = QHBoxLayout()
        self.chat_input = QLineEdit()
        self.chat_input.setPlaceholderText("输入您的问题...")
        self.chat_input.returnPressed.connect(self.send_chat_message)
        input_layout.addWidget(self.chat_input)
        
        send_btn = AnimatedButton("发送")
        send_btn.setProperty("class", "primary")
        send_btn.clicked.connect(self.send_chat_message)
        input_layout.addWidget(send_btn)
        
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.clear_chat_history)
        input_layout.addWidget(clear_btn)
        
        chat_card.layout().addLayout(input_layout)
        layout.addWidget(chat_card)
        
        # 初始化API状态检查
        self.refresh_api_status()
        
        return page
    
    def createGeneratePage(self) -> QWidget:
        """创建代码生成页面"""
        page = QWidget()
        layout = QHBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 左侧配置面板
        config_card = ModernCard("生成配置")
        config_card.setFixedWidth(350)
        
        # 语言选择
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("编程语言:"))
        self.gen_lang_combo = QComboBox()
        self.gen_lang_combo.addItems(["Python", "JavaScript", "Java", "C++", "Go", "Rust", "C#", "PHP"])
        lang_layout.addWidget(self.gen_lang_combo)
        config_card.layout().addLayout(lang_layout)
        
        # 框架选择
        framework_layout = QHBoxLayout()
        framework_layout.addWidget(QLabel("框架:"))
        self.gen_framework_combo = QComboBox()
        self.gen_framework_combo.addItems(["无", "Flask", "Django", "FastAPI", "React", "Vue", "Angular", "Spring", "Express"])
        framework_layout.addWidget(self.gen_framework_combo)
        config_card.layout().addLayout(framework_layout)
        
        # AI模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("AI模型:"))
        self.gen_model_combo = QComboBox()
        self.update_gen_model_combo()
        model_layout.addWidget(self.gen_model_combo)
        config_card.layout().addLayout(model_layout)
        
        # 需求描述
        config_card.addWidget(QLabel("需求描述:"))
        self.gen_requirement_text = QTextEdit()
        self.gen_requirement_text.setPlaceholderText("请详细描述您要生成的代码功能...\n\n示例：\n- 创建一个用户管理系统\n- 实现文件上传功能\n- 编写数据库操作类")
        self.gen_requirement_text.setMaximumHeight(150)
        config_card.addWidget(self.gen_requirement_text)
        
        # 高级选项
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.gen_include_comments = QCheckBox("包含详细注释")
        self.gen_include_comments.setChecked(True)
        advanced_layout.addWidget(self.gen_include_comments)
        
        self.gen_include_tests = QCheckBox("生成测试代码")
        advanced_layout.addWidget(self.gen_include_tests)
        
        self.gen_include_docs = QCheckBox("生成文档")
        advanced_layout.addWidget(self.gen_include_docs)
        
        config_card.addWidget(advanced_group)
        
        # 生成按钮
        generate_btn = AnimatedButton("🚀 生成代码")
        generate_btn.setProperty("class", "success")
        generate_btn.clicked.connect(self.generate_code_from_page)
        config_card.addWidget(generate_btn)
        
        # 进度条
        self.gen_progress = ModernProgressBar()
        self.gen_progress.setVisible(False)
        config_card.addWidget(self.gen_progress)
        
        layout.addWidget(config_card)
        
        # 右侧代码显示区域
        code_card = ModernCard("生成的代码")
        
        # 代码编辑器
        self.gen_code_editor = QTextEdit()
        self.gen_code_editor.setPlaceholderText("生成的代码将显示在这里...")
        self.gen_code_editor.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 13px;
                line-height: 1.4;
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 10px;
            }
        """)
        code_card.addWidget(self.gen_code_editor)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        copy_btn = AnimatedButton("📋 复制")
        copy_btn.clicked.connect(self.copy_generated_code)
        
        save_btn = AnimatedButton("💾 保存")
        save_btn.clicked.connect(self.save_generated_code)
        
        run_btn = AnimatedButton("▶️ 运行")
        run_btn.setProperty("class", "success")
        run_btn.clicked.connect(self.run_generated_code)
        
        clear_btn = AnimatedButton("🗑️ 清空")
        clear_btn.clicked.connect(self.clear_generated_code)
        
        action_layout.addWidget(copy_btn)
        action_layout.addWidget(save_btn)
        action_layout.addWidget(run_btn)
        action_layout.addWidget(clear_btn)
        action_layout.addStretch()
        
        code_card.layout().addLayout(action_layout)
        
        layout.addWidget(code_card)
        
        return page
    
    def createDebugPage(self) -> QWidget:
        """创建调试器页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("智能调试器")
        title.setStyleSheet("font-size: 24px; font-weight: 600; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 调试工具栏
        toolbar_layout = QHBoxLayout()
        
        start_debug_btn = AnimatedButton("🐛 开始调试")
        start_debug_btn.setProperty("class", "success")
        start_debug_btn.clicked.connect(self.start_debugging)
        
        stop_debug_btn = AnimatedButton("⏹️ 停止调试")
        stop_debug_btn.setProperty("class", "error")
        stop_debug_btn.clicked.connect(self.stop_debugging)
        
        step_btn = AnimatedButton("👣 单步执行")
        step_btn.clicked.connect(self.step_debug)
        
        continue_btn = AnimatedButton("▶️ 继续执行")
        continue_btn.clicked.connect(self.continue_debug)
        
        analyze_btn = AnimatedButton("🔍 AI分析")
        analyze_btn.clicked.connect(self.ai_analyze_code)
        
        toolbar_layout.addWidget(start_debug_btn)
        toolbar_layout.addWidget(stop_debug_btn)
        toolbar_layout.addWidget(step_btn)
        toolbar_layout.addWidget(continue_btn)
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # 调试面板
        debug_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：代码和断点
        left_panel = ModernCard("代码调试")
        self.debug_code_area = QTextEdit()
        self.debug_code_area.setPlaceholderText("在此处粘贴要调试的代码...")
        self.debug_code_area.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 13px;
                line-height: 1.4;
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 10px;
            }
        """)
        left_panel.addWidget(self.debug_code_area)
        
        # 断点控制
        breakpoint_layout = QHBoxLayout()
        add_breakpoint_btn = QPushButton("➕ 添加断点")
        add_breakpoint_btn.clicked.connect(self.add_breakpoint)
        clear_breakpoints_btn = QPushButton("🗑️ 清空断点")
        clear_breakpoints_btn.clicked.connect(self.clear_breakpoints)
        
        breakpoint_layout.addWidget(add_breakpoint_btn)
        breakpoint_layout.addWidget(clear_breakpoints_btn)
        breakpoint_layout.addStretch()
        left_panel.layout().addLayout(breakpoint_layout)
        
        debug_splitter.addWidget(left_panel)
        
        # 右侧：变量和调用栈
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # 变量监视
        vars_card = ModernCard("变量监视")
        self.debug_vars_tree = QTreeWidget()
        self.debug_vars_tree.setHeaderLabels(["变量", "值", "类型"])
        vars_card.addWidget(self.debug_vars_tree)
        right_layout.addWidget(vars_card)
        
        # 调用栈
        stack_card = ModernCard("调用栈")
        self.debug_stack_list = QListWidget()
        stack_card.addWidget(self.debug_stack_list)
        right_layout.addWidget(stack_card)
        
        # 调试输出
        output_card = ModernCard("调试输出")
        self.debug_output = QTextEdit()
        self.debug_output.setReadOnly(True)
        self.debug_output.setMaximumHeight(150)
        output_card.addWidget(self.debug_output)
        right_layout.addWidget(output_card)
        
        debug_splitter.addWidget(right_panel)
        debug_splitter.setSizes([600, 400])
        
        layout.addWidget(debug_splitter)
        
        return page
    
    def createPackagePage(self) -> QWidget:
        """创建打包器页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("多平台打包器")
        title.setStyleSheet("font-size: 24px; font-weight: 600; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 打包配置
        config_layout = QHBoxLayout()
        
        # 左侧配置
        left_config = ModernCard("打包配置")
        left_config.setFixedWidth(400)
        
        # 项目路径
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("项目路径:"))
        self.package_path_input = QLineEdit()
        self.package_path_input.setPlaceholderText("选择要打包的项目路径...")
        browse_btn = QPushButton("📁 浏览")
        browse_btn.clicked.connect(self.browse_package_path)
        path_layout.addWidget(self.package_path_input)
        path_layout.addWidget(browse_btn)
        left_config.layout().addLayout(path_layout)
        
        # 应用信息
        app_info_group = QGroupBox("应用信息")
        app_info_layout = QGridLayout(app_info_group)
        
        app_info_layout.addWidget(QLabel("应用名称:"), 0, 0)
        self.package_app_name = QLineEdit()
        self.package_app_name.setPlaceholderText("MyApp")
        app_info_layout.addWidget(self.package_app_name, 0, 1)
        
        app_info_layout.addWidget(QLabel("版本号:"), 1, 0)
        self.package_version = QLineEdit()
        self.package_version.setPlaceholderText("1.0.0")
        app_info_layout.addWidget(self.package_version, 1, 1)
        
        app_info_layout.addWidget(QLabel("作者:"), 2, 0)
        self.package_author = QLineEdit()
        self.package_author.setPlaceholderText("Your Name")
        app_info_layout.addWidget(self.package_author, 2, 1)
        
        left_config.addWidget(app_info_group)
        
        # 平台选择
        platform_group = QGroupBox("目标平台")
        platform_layout = QVBoxLayout(platform_group)
        
        self.package_windows_cb = QCheckBox("🪟 Windows (exe, msi)")
        self.package_windows_cb.setChecked(True)
        self.package_macos_cb = QCheckBox("🍎 macOS (app, dmg)")
        self.package_linux_cb = QCheckBox("🐧 Linux (appimage, deb, rpm)")
        
        platform_layout.addWidget(self.package_windows_cb)
        platform_layout.addWidget(self.package_macos_cb)
        platform_layout.addWidget(self.package_linux_cb)
        left_config.addWidget(platform_group)
        
        # 打包选项
        options_group = QGroupBox("打包选项")
        options_layout = QVBoxLayout(options_group)
        
        self.package_optimize_cb = QCheckBox("⚡ 代码优化")
        self.package_optimize_cb.setChecked(True)
        self.package_compress_cb = QCheckBox("🗜️ 文件压缩")
        self.package_compress_cb.setChecked(True)
        self.package_icon_cb = QCheckBox("🎨 包含图标")
        self.package_sign_cb = QCheckBox("🔐 代码签名")
        
        options_layout.addWidget(self.package_optimize_cb)
        options_layout.addWidget(self.package_compress_cb)
        options_layout.addWidget(self.package_icon_cb)
        options_layout.addWidget(self.package_sign_cb)
        left_config.addWidget(options_group)
        
        # 打包按钮
        package_btn = AnimatedButton("📦 开始打包")
        package_btn.setProperty("class", "success")
        package_btn.clicked.connect(self.start_packaging)
        left_config.addWidget(package_btn)
        
        config_layout.addWidget(left_config)
        
        # 右侧进度和日志
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 进度显示
        progress_card = ModernCard("打包进度")
        
        self.package_progress_bar = ModernProgressBar()
        progress_card.addWidget(self.package_progress_bar)
        
        self.package_status_label = QLabel("准备就绪")
        self.package_status_label.setStyleSheet("color: #A0AEC0; font-size: 14px;")
        progress_card.addWidget(self.package_status_label)
        
        right_layout.addWidget(progress_card)
        
        # 日志输出
        log_card = ModernCard("打包日志")
        self.package_log_output = QTextEdit()
        self.package_log_output.setReadOnly(True)
        self.package_log_output.setPlaceholderText("打包日志将显示在这里...")
        log_card.addWidget(self.package_log_output)
        
        right_layout.addWidget(log_card)
        
        # 结果显示
        result_card = ModernCard("打包结果")
        self.package_result_list = QListWidget()
        result_card.addWidget(self.package_result_list)
        
        # 结果操作按钮
        result_buttons = QHBoxLayout()
        open_folder_btn = QPushButton("📁 打开文件夹")
        open_folder_btn.clicked.connect(self.open_package_folder)
        test_package_btn = QPushButton("🧪 测试安装包")
        test_package_btn.clicked.connect(self.test_package)
        
        result_buttons.addWidget(open_folder_btn)
        result_buttons.addWidget(test_package_btn)
        result_buttons.addStretch()
        result_card.layout().addLayout(result_buttons)
        
        right_layout.addWidget(result_card)
        
        config_layout.addWidget(right_panel)
        layout.addLayout(config_layout)
        log_output = QTextEdit()
        log_output.setReadOnly(True)
        log_output.setPlaceholderText("打包日志将显示在这里...")
        log_card.addWidget(log_output)
        
        right_layout.addWidget(log_card)
        
        config_layout.addWidget(right_panel)
        layout.addLayout(config_layout)
        
        return page
    
    def createDeployPage(self) -> QWidget:
        """创建部署页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("智能部署")
        title.setStyleSheet("font-size: 24px; font-weight: 600; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 部署选项卡
        deploy_tabs = QTabWidget()
        
        # Docker部署
        docker_tab = QWidget()
        docker_layout = QVBoxLayout(docker_tab)
        
        docker_card = ModernCard("Docker 部署")
        
        # Docker配置
        docker_config_layout = QGridLayout()
        docker_config_layout.addWidget(QLabel("镜像名称:"), 0, 0)
        self.docker_image_input = QLineEdit("aicodegen/app:latest")
        docker_config_layout.addWidget(self.docker_image_input, 0, 1)
        
        docker_config_layout.addWidget(QLabel("端口映射:"), 1, 0)
        self.docker_port_input = QLineEdit("8080:8080")
        docker_config_layout.addWidget(self.docker_port_input, 1, 1)
        
        docker_config_layout.addWidget(QLabel("环境变量:"), 2, 0)
        self.docker_env_input = QTextEdit()
        self.docker_env_input.setPlaceholderText("KEY1=value1\\nKEY2=value2")
        self.docker_env_input.setMaximumHeight(80)
        docker_config_layout.addWidget(self.docker_env_input, 2, 1)
        
        docker_card.layout().addLayout(docker_config_layout)
        
        docker_btn = AnimatedButton("🐳 部署到 Docker")
        docker_btn.setProperty("class", "success")
        docker_btn.clicked.connect(self.deploy_to_docker)
        docker_card.addWidget(docker_btn)
        
        # Docker状态
        self.docker_status = QLabel("状态: 未部署")
        docker_card.addWidget(self.docker_status)
        
        docker_layout.addWidget(docker_card)
        docker_layout.addStretch()
        
        deploy_tabs.addTab(docker_tab, "Docker")
        
        # Kubernetes部署
        k8s_tab = QWidget()
        k8s_layout = QVBoxLayout(k8s_tab)
        
        k8s_card = ModernCard("Kubernetes 部署")
        
        k8s_config_layout = QGridLayout()
        k8s_config_layout.addWidget(QLabel("命名空间:"), 0, 0)
        self.k8s_namespace_input = QLineEdit("ai-code-generator")
        k8s_config_layout.addWidget(self.k8s_namespace_input, 0, 1)
        
        k8s_config_layout.addWidget(QLabel("副本数:"), 1, 0)
        self.k8s_replicas_input = QLineEdit("3")
        k8s_config_layout.addWidget(self.k8s_replicas_input, 1, 1)
        
        k8s_config_layout.addWidget(QLabel("资源限制:"), 2, 0)
        self.k8s_resources_input = QLineEdit("CPU: 500m, Memory: 512Mi")
        k8s_config_layout.addWidget(self.k8s_resources_input, 2, 1)
        
        k8s_card.layout().addLayout(k8s_config_layout)
        
        k8s_btn = AnimatedButton("☸️ 部署到 Kubernetes")
        k8s_btn.setProperty("class", "success")
        k8s_btn.clicked.connect(self.deploy_to_kubernetes)
        k8s_card.addWidget(k8s_btn)
        
        # K8s状态
        self.k8s_status = QLabel("状态: 未部署")
        k8s_card.addWidget(self.k8s_status)
        
        k8s_layout.addWidget(k8s_card)
        k8s_layout.addStretch()
        
        deploy_tabs.addTab(k8s_tab, "Kubernetes")
        
        # 云平台部署
        cloud_tab = QWidget()
        cloud_layout = QVBoxLayout(cloud_tab)
        
        cloud_card = ModernCard("云平台部署")
        
        # 云平台选择
        cloud_platform_layout = QHBoxLayout()
        cloud_platform_layout.addWidget(QLabel("云平台:"))
        self.cloud_combo = QComboBox()
        self.cloud_combo.addItems(["AWS", "Azure", "Google Cloud", "阿里云", "腾讯云"])
        cloud_platform_layout.addWidget(self.cloud_combo)
        cloud_card.layout().addLayout(cloud_platform_layout)
        
        # 云平台配置
        cloud_config_layout = QGridLayout()
        cloud_config_layout.addWidget(QLabel("访问密钥:"), 0, 0)
        self.cloud_access_key = QLineEdit()
        self.cloud_access_key.setEchoMode(QLineEdit.Password)
        cloud_config_layout.addWidget(self.cloud_access_key, 0, 1)
        
        cloud_config_layout.addWidget(QLabel("区域:"), 1, 0)
        self.cloud_region = QLineEdit("us-east-1")
        cloud_config_layout.addWidget(self.cloud_region, 1, 1)
        
        cloud_card.layout().addLayout(cloud_config_layout)
        
        cloud_btn = AnimatedButton("☁️ 部署到云平台")
        cloud_btn.setProperty("class", "success")
        cloud_btn.clicked.connect(self.deploy_to_cloud)
        cloud_card.addWidget(cloud_btn)
        
        # 云平台状态
        self.cloud_status = QLabel("状态: 未部署")
        cloud_card.addWidget(self.cloud_status)
        
        cloud_layout.addWidget(cloud_card)
        cloud_layout.addStretch()
        
        deploy_tabs.addTab(cloud_tab, "云平台")
        
        layout.addWidget(deploy_tabs)
        
        return page
    
    def createSettingsPage(self) -> QWidget:
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("设置")
        title.setStyleSheet("font-size: 24px; font-weight: 600; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 设置选项卡
        settings_tabs = QTabWidget()
        
        # 外观设置
        appearance_tab = QWidget()
        appearance_layout = QVBoxLayout(appearance_tab)
        
        appearance_card = ModernCard("外观设置")
        
        # 主题选择
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("主题:"))
        theme_combo = QComboBox()
        theme_combo.addItems(["深色主题", "浅色主题", "蓝色主题"])
        theme_combo.currentTextChanged.connect(self.onThemeChanged)
        theme_layout.addWidget(theme_combo)
        theme_layout.addStretch()
        appearance_card.layout().addLayout(theme_layout)
        
        # 字体大小
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体大小:"))
        font_slider = QSlider(Qt.Horizontal)
        font_slider.setRange(10, 20)
        font_slider.setValue(14)
        font_size_label = QLabel("14px")
        font_layout.addWidget(font_slider)
        font_layout.addWidget(font_size_label)
        appearance_card.layout().addLayout(font_layout)
        
        appearance_layout.addWidget(appearance_card)
        appearance_layout.addStretch()
        
        settings_tabs.addTab(appearance_tab, "外观")
        
        # AI设置
        ai_tab = QWidget()
        ai_layout = QVBoxLayout(ai_tab)
        
        # API提供商配置
        providers_card = ModernCard("API 提供商配置")
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 存储API配置控件
        self.api_config_widgets = {}
        
        # 为每个API提供商创建配置界面
        for provider in APIProvider:
            provider_group = self.create_provider_config_group(provider)
            scroll_layout.addWidget(provider_group)
            
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(400)
        providers_card.addWidget(scroll_area)
        
        # 网络配置
        network_card = ModernCard("网络配置")
        
        # 代理设置
        proxy_layout = QHBoxLayout()
        proxy_layout.addWidget(QLabel("代理服务器:"))
        self.proxy_input = QLineEdit()
        self.proxy_input.setPlaceholderText("http://127.0.0.1:7890 (可选)")
        proxy_layout.addWidget(self.proxy_input)
        
        set_proxy_btn = QPushButton("设置代理")
        set_proxy_btn.clicked.connect(self.set_proxy_config)
        proxy_layout.addWidget(set_proxy_btn)
        
        clear_proxy_btn = QPushButton("清除代理")
        clear_proxy_btn.clicked.connect(self.clear_proxy_config)
        proxy_layout.addWidget(clear_proxy_btn)
        
        network_card.layout().addLayout(proxy_layout)
        
        # 当前代理状态
        self.proxy_status_label = QLabel("当前代理: 未设置")
        self.proxy_status_label.setStyleSheet("color: #A0AEC0; font-size: 12px;")
        network_card.addWidget(self.proxy_status_label)
        
        ai_layout.addWidget(network_card)
        
        # API配置按钮
        api_buttons_layout = QHBoxLayout()
        
        test_all_btn = QPushButton("🔍 测试所有API")
        test_all_btn.clicked.connect(self.test_all_apis)
        api_buttons_layout.addWidget(test_all_btn)
        
        save_config_btn = QPushButton("💾 保存配置")
        save_config_btn.clicked.connect(self.save_api_configs)
        api_buttons_layout.addWidget(save_config_btn)
        
        load_config_btn = QPushButton("📂 加载配置")
        load_config_btn.clicked.connect(self.load_api_configs)
        api_buttons_layout.addWidget(load_config_btn)
        
        api_buttons_layout.addStretch()
        providers_card.layout().addLayout(api_buttons_layout)
        
        ai_layout.addWidget(providers_card)
        
        # API使用统计
        stats_card = ModernCard("API 使用统计")
        self.api_stats_label = QLabel("暂无统计数据")
        stats_card.addWidget(self.api_stats_label)
        ai_layout.addWidget(stats_card)
        
        ai_layout.addStretch()
        
        settings_tabs.addTab(ai_tab, "AI")
        
        layout.addWidget(settings_tabs)
        
        return page
    
    def createMenuBar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        file_menu.addAction('新建项目', self.newProject)
        file_menu.addAction('打开项目', self.openProject)
        file_menu.addSeparator()
        file_menu.addAction('退出', self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')
        edit_menu.addAction('撤销', lambda: None)
        edit_menu.addAction('重做', lambda: None)
        edit_menu.addSeparator()
        edit_menu.addAction('复制', lambda: None)
        edit_menu.addAction('粘贴', lambda: None)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图')
        view_menu.addAction('全屏', self.toggleFullScreen)
        
        # API设置菜单
        api_menu = menubar.addMenu('API设置')
        
        # 创建API提供商选择菜单
        self.api_provider_group = QActionGroup(self)
        self.api_provider_actions = {}
        
        # 从APIProvider枚举中获取所有提供商
        for provider in APIProvider:
            action = QAction(provider.value, self)
            action.setCheckable(True)
            self.api_provider_group.addAction(action)
            api_menu.addAction(action)
            self.api_provider_actions[provider] = action
            action.triggered.connect(lambda checked, p=provider: self.onAPIProviderChanged(p))
            
        # 加载默认API提供商设置
        self.loadDefaultAPIProvider()
        
        # 主题子菜单
        theme_menu = view_menu.addMenu('主题')
        theme_menu.addAction('深色主题', lambda: self.applyTheme('dark'))
        theme_menu.addAction('浅色主题', lambda: self.applyTheme('light'))
        theme_menu.addAction('蓝色主题', lambda: self.applyTheme('blue'))
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        help_menu.addAction('关于', self.showAbout)
        help_menu.addAction('用户手册', lambda: None)
    
    def createToolBar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        # 新建
        new_action = QAction('🆕', self)
        new_action.setToolTip('新建项目')
        new_action.triggered.connect(self.newProject)
        toolbar.addAction(new_action)
        
        # 打开
        open_action = QAction('📁', self)
        open_action.setToolTip('打开项目')
        open_action.triggered.connect(self.openProject)
        toolbar.addAction(open_action)
        
        # 保存
        save_action = QAction('💾', self)
        save_action.setToolTip('保存')
        save_action.triggered.connect(lambda: None)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 运行
        run_action = QAction('▶️', self)
        run_action.setToolTip('运行')
        run_action.triggered.connect(lambda: None)
        toolbar.addAction(run_action)
        
        # 调试
        debug_action = QAction('🐛', self)
        debug_action.setToolTip('调试')
        debug_action.triggered.connect(lambda: None)
        toolbar.addAction(debug_action)
        
        toolbar.addSeparator()
        
        # 设置
        settings_action = QAction('⚙️', self)
        settings_action.setToolTip('设置')
        settings_action.triggered.connect(lambda: self.sidebar.selectItem('settings'))
        toolbar.addAction(settings_action)
    
    def createStatusBar(self):
        """创建状态栏"""
        statusbar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        statusbar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        statusbar.addPermanentWidget(self.progress_bar)
        
        # 版本信息
        version_label = QLabel("v1.0.0")
        statusbar.addPermanentWidget(version_label)
    
    def centerWindow(self):
        """居中显示窗口"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def onSidebarItemClicked(self, key: str):
        """侧边栏项目点击事件"""
        page_map = {
            'home': 0,
            'ai': 1,
            'generate': 2,
            'debug': 3,
            'package': 4,
            'deploy': 5,
            'settings': 6
        }
        
        if key in page_map:
            self.content_area.setCurrentIndex(page_map[key])
    
    def onFeatureCardClicked(self, action: str):
        """功能卡片点击事件"""
        if action == 'ai':
            self.sidebar.selectItem('ai')
        elif action == 'debug':
            self.sidebar.selectItem('debug')
        elif action == 'package':
            self.sidebar.selectItem('package')
        elif action == 'deploy':
            self.sidebar.selectItem('deploy')
    
    def onThemeChanged(self, theme_text: str):
        """主题改变事件"""
        theme_map = {
            '深色主题': 'dark',
            '浅色主题': 'light',
            '蓝色主题': 'blue'
        }
        
        theme = theme_map.get(theme_text, 'dark')
        self.applyTheme(theme)
    
    def showNotification(self, message: str, type_: str = "info"):
        """显示通知"""
        notification = ModernNotification(message, type_, self)
        notification.setFixedWidth(350)
        notification.showNotification(self)
    
    def newProject(self):
        """新建项目"""
        self.showNotification("新建项目功能开发中...", "info")
    
    def openProject(self):
        """打开项目"""
        self.showNotification("打开项目功能开发中...", "info")
    
    def toggleFullScreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def showAbout(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "AI Code Generator v1.0.0\n\n"
                         "智能代码生成工具\n"
                         "让开发更高效、更智能")
    
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if hasattr(self, 'notification_container'):
            self.notification_container.setGeometry(0, 0, self.width(), self.height())

        # 更新响应式布局
        if hasattr(self, 'layout_manager'):
            self.layout_manager.responsive.update_breakpoint(event.size().width())

    def restore_layout_state(self):
        """恢复布局状态"""
        if hasattr(self, 'main_splitter') and self.layout_manager:
            try:
                splitters = {'main': self.main_splitter}
                self.layout_manager.restore_layout_state(splitters)
            except Exception as e:
                print(f"恢复布局状态失败: {e}")

    def save_layout_state(self):
        """保存布局状态"""
        if hasattr(self, 'main_splitter') and self.layout_manager:
            try:
                splitters = {'main': self.main_splitter}
                self.layout_manager.save_layout_state(splitters)
            except Exception as e:
                print(f"保存布局状态失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_layout_state()
        super().closeEvent(event)
    
    # AI功能相关方法
    def refresh_api_status(self):
        """刷新API状态"""
        enabled_providers = self.api_manager.get_enabled_providers()
        if enabled_providers:
            status_text = f"✅ 已配置 {len(enabled_providers)} 个API提供商: "
            status_text += ", ".join([p.value for p in enabled_providers])
        else:
            status_text = "❌ 未配置任何API提供商"
        
        self.api_status_label.setText(status_text)
    
    def update_model_combo(self):
        """更新模型选择框"""
        if hasattr(self, 'model_combo'):
            self.model_combo.clear()
            for provider in self.api_manager.get_enabled_providers():
                config = self.api_manager.get_config(provider)
                if config:
                    self.model_combo.addItem(f"{provider.value}: {config.model}", provider)
    
    def send_chat_message(self):
        """发送聊天消息"""
        message = self.chat_input.text().strip()
        if not message:
            return
        
        # 显示用户消息
        self.chat_history.append(f"<b>用户:</b> {message}")
        self.chat_input.clear()
        
        # 获取选中的模型
        current_data = self.model_combo.currentData()
        if not current_data:
            self.chat_history.append("<b>系统:</b> 请先配置API提供商")
            return
        
        provider = current_data
        
        # 异步发送消息
        self.chat_history.append("<b>AI:</b> 正在思考...")
        
        # 创建工作线程
        self.chat_thread = ChatThread(self.code_generator, message, provider)
        self.chat_thread.response_received.connect(self.on_chat_response)
        self.chat_thread.error_occurred.connect(self.on_chat_error)
        self.chat_thread.start()
    
    def on_chat_response(self, response):
        """处理聊天响应"""
        # 移除"正在思考..."
        text = self.chat_history.toPlainText()
        if "正在思考..." in text:
            text = text.replace("AI: 正在思考...", f"AI: {response}")
            self.chat_history.setPlainText(text)
        else:
            self.chat_history.append(f"<b>AI:</b> {response}")
    
    def on_chat_error(self, error):
        """处理聊天错误"""
        text = self.chat_history.toPlainText()
        if "正在思考..." in text:
            text = text.replace("AI: 正在思考...", f"AI: 错误: {error}")
            self.chat_history.setPlainText(text)
        else:
            self.chat_history.append(f"<b>系统:</b> 错误: {error}")
    
    def clear_chat_history(self):
        """清空聊天历史"""
        self.chat_history.clear()
    
    def show_code_generation_dialog(self):
        """显示代码生成对话框"""
        dialog = CodeGenerationDialog(self.code_generator, self)
        dialog.exec_()
    
    def show_code_optimization_dialog(self):
        """显示代码优化对话框"""
        dialog = CodeOptimizationDialog(self.code_generator, self)
        dialog.exec_()
    
    def show_bug_fix_dialog(self):
        """显示错误修复对话框"""
        dialog = BugFixDialog(self.code_generator, self)
        dialog.exec_()
    
    def show_doc_generation_dialog(self):
        """显示文档生成对话框"""
        dialog = DocGenerationDialog(self.code_generator, self)
        dialog.exec_()
    
    # 项目管理方法
    def newProject(self):
        """新建项目"""
        dialog = NewProjectDialog(self.project_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            project_info = dialog.get_project_info()
            try:
                project_path = self.project_manager.create_project(
                    project_info['name'], 
                    project_info['template']
                )
                self.showNotification(f"项目创建成功: {project_path}", "success")
            except Exception as e:
                self.showNotification(f"项目创建失败: {str(e)}", "error")
    
    def openProject(self):
        """打开项目"""
        projects = self.project_manager.get_projects()
        if not projects:
            self.showNotification("没有找到任何项目", "warning")
            return
        
        project, ok = QInputDialog.getItem(
            self, "选择项目", "请选择要打开的项目:", projects, 0, False
        )
        
        if ok and project:
            project_path = os.path.join(self.project_manager.projects_dir, project)
            self.project_manager.current_project = project_path
            self.showNotification(f"项目已打开: {project}", "success")
    
    def onThemeChanged(self, theme_text):
        """主题改变事件"""
        theme_map = {
            "深色主题": "dark",
            "浅色主题": "light", 
            "蓝色主题": "blue"
        }
        
        theme = theme_map.get(theme_text, "dark")
        self.applyTheme(theme)
    
    def create_provider_config_group(self, provider: APIProvider) -> QWidget:
        """创建API提供商配置组"""
        group = QWidget()
        group.setStyleSheet("""
            QWidget {
                background-color: #2D3748;
                border: 1px solid #4A5568;
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 提供商标题和启用开关
        header_layout = QHBoxLayout()
        
        # 提供商图标和名称
        provider_info = self.get_provider_info(provider)
        title_label = QLabel(f"{provider_info['icon']} {provider_info['name']}")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #F7FAFC;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 启用开关
        enabled_checkbox = QCheckBox("启用")
        enabled_checkbox.setStyleSheet("color: #F7FAFC;")
        config = self.api_manager.get_config(provider)
        if config:
            enabled_checkbox.setChecked(config.enabled)
        header_layout.addWidget(enabled_checkbox)
        
        layout.addLayout(header_layout)
        
        # 配置表单
        form_layout = QGridLayout()
        
        # API密钥
        form_layout.addWidget(QLabel("API密钥:"), 0, 0)
        api_key_input = QLineEdit()
        api_key_input.setEchoMode(QLineEdit.Password)
        api_key_input.setPlaceholderText("输入API密钥...")
        if config:
            api_key_input.setText(config.api_key)
        form_layout.addWidget(api_key_input, 0, 1)
        
        # 基础URL
        form_layout.addWidget(QLabel("基础URL:"), 1, 0)
        base_url_input = QLineEdit()
        base_url_input.setPlaceholderText("API基础URL...")
        if config:
            base_url_input.setText(config.base_url)
        form_layout.addWidget(base_url_input, 1, 1)
        
        # 模型
        form_layout.addWidget(QLabel("模型:"), 2, 0)
        model_combo = QComboBox()
        model_combo.setEditable(True)
        models = provider_info['models']
        model_combo.addItems(models)
        if config and config.model:
            model_combo.setCurrentText(config.model)
        form_layout.addWidget(model_combo, 2, 1)
        
        # 最大令牌数
        form_layout.addWidget(QLabel("最大令牌:"), 3, 0)
        max_tokens_input = QLineEdit()
        max_tokens_input.setPlaceholderText("4096")
        if config:
            max_tokens_input.setText(str(config.max_tokens))
        form_layout.addWidget(max_tokens_input, 3, 1)
        
        # 温度
        form_layout.addWidget(QLabel("温度:"), 4, 0)
        temperature_input = QLineEdit()
        temperature_input.setPlaceholderText("0.7")
        if config:
            temperature_input.setText(str(config.temperature))
        form_layout.addWidget(temperature_input, 4, 1)
        
        layout.addLayout(form_layout)
        
        # 测试按钮和状态
        test_layout = QHBoxLayout()
        
        test_btn = QPushButton("🔍 测试连接")
        test_btn.clicked.connect(lambda: self.test_single_api(provider))
        test_layout.addWidget(test_btn)
        
        status_label = QLabel("未测试")
        status_label.setStyleSheet("color: #A0AEC0;")
        test_layout.addWidget(status_label)
        
        test_layout.addStretch()
        layout.addLayout(test_layout)
        
        # 保存控件引用
        self.api_config_widgets[provider] = {
            'enabled': enabled_checkbox,
            'api_key': api_key_input,
            'base_url': base_url_input,
            'model': model_combo,
            'max_tokens': max_tokens_input,
            'temperature': temperature_input,
            'status': status_label
        }
        
        return group
    
    def get_provider_info(self, provider: APIProvider) -> dict:
        """获取提供商信息"""
        provider_info = {
            APIProvider.OPENAI: {
                'name': 'OpenAI',
                'icon': '🤖',
                'models': ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo-preview']
            },
            APIProvider.ANTHROPIC: {
                'name': 'Anthropic',
                'icon': '🧠',
                'models': ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-3-opus-20240229']
            },
            APIProvider.GOOGLE: {
                'name': 'Google',
                'icon': '🔍',
                'models': ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro']
            },
            APIProvider.AZURE: {
                'name': 'Azure OpenAI',
                'icon': '☁️',
                'models': ['gpt-35-turbo', 'gpt-4', 'gpt-4-32k']
            },
            APIProvider.HUGGINGFACE: {
                'name': 'Hugging Face',
                'icon': '🤗',
                'models': ['microsoft/DialoGPT-medium', 'facebook/blenderbot-400M-distill']
            },
            APIProvider.COHERE: {
                'name': 'Cohere',
                'icon': '🌊',
                'models': ['command', 'command-light', 'command-nightly']
            },
            APIProvider.DEEPSEEK: {
                'name': 'DeepSeek',
                'icon': '🔬',
                'models': ['deepseek-chat', 'deepseek-coder']
            },
            APIProvider.MOONSHOT: {
                'name': '月之暗面',
                'icon': '🌙',
                'models': ['moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k']
            },
            APIProvider.ZHIPU: {
                'name': '智谱AI',
                'icon': '🧮',
                'models': ['glm-4', 'glm-4-vision', 'glm-3-turbo']
            },
            APIProvider.BAIDU: {
                'name': '百度文心',
                'icon': '🐻',
                'models': ['ernie-bot-turbo', 'ernie-bot', 'ernie-bot-4']
            },
            APIProvider.ALIBABA: {
                'name': '阿里通义',
                'icon': '🐱',
                'models': ['qwen-turbo', 'qwen-plus', 'qwen-max']
            },
            APIProvider.TENCENT: {
                'name': '腾讯混元',
                'icon': '🐧',
                'models': ['hunyuan-lite', 'hunyuan-standard', 'hunyuan-pro']
            }
        }
        
        return provider_info.get(provider, {
            'name': provider.value,
            'icon': '🤖',
            'models': ['default-model']
        })
    
    def test_single_api(self, provider: APIProvider):
        """测试单个API"""
        if provider not in self.api_config_widgets:
            return
        
        widgets = self.api_config_widgets[provider]
        status_label = widgets['status']
        
        # 更新状态
        status_label.setText("测试中...")
        status_label.setStyleSheet("color: #D69E2E;")
        
        # 获取配置
        try:
            api_key = widgets['api_key'].text().strip()
            base_url = widgets['base_url'].text().strip()
            model = widgets['model'].currentText().strip()
            
            if not api_key:
                status_label.setText("❌ 缺少API密钥")
                status_label.setStyleSheet("color: #E53E3E;")
                return
            
            # 创建临时配置
            temp_config = APIConfig(
                provider=provider,
                api_key=api_key,
                base_url=base_url,
                model=model,
                max_tokens=int(widgets['max_tokens'].text() or "4096"),
                temperature=float(widgets['temperature'].text() or "0.7"),
                enabled=True
            )
            
            # 更新到API管理器
            self.api_manager.update_config(provider, temp_config)
            
            # 创建测试线程
            self.test_thread = APITestThread(self.api_manager, provider)
            self.test_thread.test_completed.connect(
                lambda success, msg: self.on_api_test_completed(provider, success, msg)
            )
            self.test_thread.start()
            
        except ValueError as e:
            status_label.setText(f"❌ 配置错误: {str(e)}")
            status_label.setStyleSheet("color: #E53E3E;")
        except Exception as e:
            status_label.setText(f"❌ 测试失败: {str(e)}")
            status_label.setStyleSheet("color: #E53E3E;")
    
    def on_api_test_completed(self, provider: APIProvider, success: bool, message: str):
        """API测试完成回调"""
        if provider in self.api_config_widgets:
            status_label = self.api_config_widgets[provider]['status']
            if success:
                status_label.setText("✅ 连接成功")
                status_label.setStyleSheet("color: #38A169;")
            else:
                status_label.setText(f"❌ {message}")
                status_label.setStyleSheet("color: #E53E3E;")
    
    def test_all_apis(self):
        """测试所有启用的API"""
        enabled_count = 0
        for provider, widgets in self.api_config_widgets.items():
            if widgets['enabled'].isChecked():
                enabled_count += 1
                self.test_single_api(provider)
        
        if enabled_count == 0:
            self.showNotification("请先启用至少一个API提供商", "warning")
    
    def save_api_configs(self):
        """保存API配置"""
        try:
            for provider, widgets in self.api_config_widgets.items():
                config = APIConfig(
                    provider=provider,
                    api_key=widgets['api_key'].text().strip(),
                    base_url=widgets['base_url'].text().strip(),
                    model=widgets['model'].currentText().strip(),
                    max_tokens=int(widgets['max_tokens'].text() or "4096"),
                    temperature=float(widgets['temperature'].text() or "0.7"),
                    enabled=widgets['enabled'].isChecked()
                )
                self.api_manager.update_config(provider, config)
            
            self.showNotification("API配置保存成功", "success")
            self.refresh_api_status()
            self.update_model_combo()
            
        except Exception as e:
            self.showNotification(f"保存配置失败: {str(e)}", "error")
    
    def load_api_configs(self):
        """加载API配置"""
        try:
            self.api_manager.load_configs()
            
            # 更新界面
            for provider, widgets in self.api_config_widgets.items():
                config = self.api_manager.get_config(provider)
                if config:
                    widgets['enabled'].setChecked(config.enabled)
                    widgets['api_key'].setText(config.api_key)
                    widgets['base_url'].setText(config.base_url)
                    widgets['model'].setCurrentText(config.model)
                    widgets['max_tokens'].setText(str(config.max_tokens))
                    widgets['temperature'].setText(str(config.temperature))
            
            self.showNotification("API配置加载成功", "success")
            self.refresh_api_status()
            self.update_model_combo()
            
        except Exception as e:
            self.showNotification(f"加载配置失败: {str(e)}", "error")
    
    def set_proxy_config(self):
        """设置代理配置"""
        proxy_url = self.proxy_input.text().strip()
        if proxy_url:
            # 验证代理URL格式
            if not (proxy_url.startswith('http://') or proxy_url.startswith('https://')):
                proxy_url = 'http://' + proxy_url
            
            self.api_manager.set_proxy(proxy_url)
            self.proxy_status_label.setText(f"当前代理: {proxy_url}")
            self.proxy_status_label.setStyleSheet("color: #38A169; font-size: 12px;")
            self.showNotification("代理设置成功", "success")
        else:
            self.showNotification("请输入代理服务器地址", "warning")
    
    def clear_proxy_config(self):
        """清除代理配置"""
        self.api_manager.set_proxy("")
        self.proxy_input.clear()
        self.proxy_status_label.setText("当前代理: 未设置")
        self.proxy_status_label.setStyleSheet("color: #A0AEC0; font-size: 12px;")
        self.showNotification("代理已清除", "success")
    
    def update_proxy_status(self):
        """更新代理状态显示"""
        proxy_url = self.api_manager.get_proxy_url()
        if proxy_url:
            self.proxy_status_label.setText(f"当前代理: {proxy_url}")
            self.proxy_status_label.setStyleSheet("color: #38A169; font-size: 12px;")
            if hasattr(self, 'proxy_input'):
                self.proxy_input.setText(proxy_url)
        else:
            self.proxy_status_label.setText("当前代理: 未设置")
            self.proxy_status_label.setStyleSheet("color: #A0AEC0; font-size: 12px;")
    
    # 代码生成页面功能方法
    def update_gen_model_combo(self):
        """更新代码生成模型选择框"""
        if hasattr(self, 'gen_model_combo'):
            self.gen_model_combo.clear()
            for provider in self.api_manager.get_enabled_providers():
                config = self.api_manager.get_config(provider)
                if config:
                    self.gen_model_combo.addItem(f"{provider.value}: {config.model}", provider)
    
    def generate_code_from_page(self):
        """从页面生成代码"""
        requirement = self.gen_requirement_text.toPlainText().strip()
        if not requirement:
            self.showNotification("请输入代码需求描述", "warning")
            return
        
        language = self.gen_lang_combo.currentText().lower()
        framework = self.gen_framework_combo.currentText()
        
        # 构建详细提示
        prompt = f"请生成{language}代码来实现以下需求：\n{requirement}\n\n"
        
        if framework != "无":
            prompt += f"使用{framework}框架。\n"
        
        if self.gen_include_comments.isChecked():
            prompt += "包含详细的中文注释。\n"
        
        if self.gen_include_tests.isChecked():
            prompt += "包含单元测试代码。\n"
        
        if self.gen_include_docs.isChecked():
            prompt += "包含API文档。\n"
        
        prompt += "\n要求：\n1. 代码要完整可运行\n2. 遵循最佳实践\n3. 包含错误处理\n4. 代码结构清晰"
        
        # 显示进度
        self.gen_progress.setVisible(True)
        self.gen_progress.setValueAnimated(0)
        self.gen_code_editor.setText("正在生成代码，请稍候...")
        
        # 获取选中的模型
        current_data = self.gen_model_combo.currentData()
        if not current_data:
            self.showNotification("请先配置API提供商", "warning")
            self.gen_progress.setVisible(False)
            return
        
        provider = current_data
        
        # 创建生成线程
        self.code_gen_thread = CodeGenThread(self.code_generator, prompt, language)
        self.code_gen_thread.code_generated.connect(self.on_code_generated_from_page)
        self.code_gen_thread.error_occurred.connect(self.on_code_gen_error)
        self.code_gen_thread.start()
    
    def on_code_generated_from_page(self, code):
        """代码生成完成"""
        self.gen_code_editor.setText(code)
        self.gen_progress.setValueAnimated(100)
        QTimer.singleShot(1000, lambda: self.gen_progress.setVisible(False))
        self.showNotification("代码生成完成", "success")
    
    def on_code_gen_error(self, error):
        """代码生成错误"""
        self.gen_code_editor.setText(f"代码生成失败: {error}")
        self.gen_progress.setVisible(False)
        self.showNotification(f"代码生成失败: {error}", "error")
    
    def copy_generated_code(self):
        """复制生成的代码"""
        code = self.gen_code_editor.toPlainText()
        if code and not code.startswith("正在生成代码"):
            QApplication.clipboard().setText(code)
            self.showNotification("代码已复制到剪贴板", "success")
        else:
            self.showNotification("没有可复制的代码", "warning")
    
    def save_generated_code(self):
        """保存生成的代码"""
        code = self.gen_code_editor.toPlainText()
        if not code or code.startswith("正在生成代码"):
            self.showNotification("没有可保存的代码", "warning")
            return
        
        language = self.gen_lang_combo.currentText().lower()
        ext_map = {
            "python": ".py",
            "javascript": ".js",
            "java": ".java",
            "c++": ".cpp",
            "go": ".go",
            "rust": ".rs",
            "c#": ".cs",
            "php": ".php"
        }
        
        ext = ext_map.get(language, ".txt")
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存代码", f"generated_code{ext}", f"代码文件 (*{ext})"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                self.showNotification(f"代码已保存到: {filename}", "success")
            except Exception as e:
                self.showNotification(f"保存失败: {str(e)}", "error")
    
    def run_generated_code(self):
        """运行生成的代码"""
        code = self.gen_code_editor.toPlainText()
        if not code or code.startswith("正在生成代码"):
            self.showNotification("没有可运行的代码", "warning")
            return
        
        language = self.gen_lang_combo.currentText().lower()
        
        if language == "python":
            self.run_python_code(code)
        else:
            self.showNotification(f"暂不支持运行{language}代码", "warning")
    
    def run_python_code(self, code):
        """运行Python代码"""
        try:
            # 创建临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                f.write(code)
                temp_file = f.name
            
            # 运行代码
            result = subprocess.run(
                [sys.executable, temp_file],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # 显示结果
            if result.returncode == 0:
                output = result.stdout
                if output:
                    self.showNotification("代码运行成功", "success")
                    # 可以在这里显示输出结果
                else:
                    self.showNotification("代码运行成功（无输出）", "success")
            else:
                error = result.stderr
                self.showNotification(f"代码运行失败: {error}", "error")
            
            # 清理临时文件
            os.unlink(temp_file)
            
        except subprocess.TimeoutExpired:
            self.showNotification("代码运行超时", "error")
        except Exception as e:
            self.showNotification(f"运行失败: {str(e)}", "error")
    
    def clear_generated_code(self):
        """清空生成的代码"""
        self.gen_code_editor.clear()
        self.gen_requirement_text.clear()
        self.showNotification("代码已清空", "success")
    
    # 调试页面功能方法
    def start_debugging(self):
        """开始调试"""
        code = self.debug_code_area.toPlainText().strip()
        if not code:
            self.showNotification("请输入要调试的代码", "warning")
            return
        
        self.debug_output.append("🐛 开始调试会话...")
        self.debug_output.append(f"📝 代码行数: {len(code.split('\\n'))}")
        
        # 模拟调试过程
        self.debug_vars_tree.clear()
        self.debug_stack_list.clear()
        
        # 添加一些示例变量
        root = QTreeWidgetItem(self.debug_vars_tree)
        root.setText(0, "局部变量")
        
        var1 = QTreeWidgetItem(root)
        var1.setText(0, "x")
        var1.setText(1, "10")
        var1.setText(2, "int")
        
        var2 = QTreeWidgetItem(root)
        var2.setText(0, "message")
        var2.setText(1, "'Hello World'")
        var2.setText(2, "str")
        
        self.debug_vars_tree.expandAll()
        
        # 添加调用栈
        self.debug_stack_list.addItem("main() - line 1")
        self.debug_stack_list.addItem("function() - line 5")
        
        self.showNotification("调试会话已启动", "success")
    
    def stop_debugging(self):
        """停止调试"""
        self.debug_output.append("⏹️ 调试会话已停止")
        self.debug_vars_tree.clear()
        self.debug_stack_list.clear()
        self.showNotification("调试会话已停止", "success")
    
    def step_debug(self):
        """单步执行"""
        self.debug_output.append("👣 单步执行...")
        self.showNotification("单步执行", "success")
    
    def continue_debug(self):
        """继续执行"""
        self.debug_output.append("▶️ 继续执行...")
        self.showNotification("继续执行", "success")
    
    def add_breakpoint(self):
        """添加断点"""
        line_num, ok = QInputDialog.getInt(self, "添加断点", "请输入行号:", 1, 1, 1000)
        if ok:
            self.debug_output.append(f"📍 在第{line_num}行添加断点")
            self.showNotification(f"断点已添加到第{line_num}行", "success")
    
    def clear_breakpoints(self):
        """清空断点"""
        self.debug_output.append("🗑️ 已清空所有断点")
        self.showNotification("所有断点已清空", "success")
    
    def ai_analyze_code(self):
        """AI分析代码"""
        code = self.debug_code_area.toPlainText().strip()
        if not code:
            self.showNotification("请输入要分析的代码", "warning")
            return
        
        self.debug_output.append("🔍 AI正在分析代码...")
        
        # 这里可以调用AI API进行代码分析
        # 暂时显示模拟结果
        QTimer.singleShot(2000, lambda: self.debug_output.append(
            "✅ AI分析完成:\\n"
            "- 代码结构良好\\n"
            "- 建议添加异常处理\\n"
            "- 可以优化变量命名"
        ))
        
        self.showNotification("AI代码分析已启动", "success")
    
    # 打包页面功能方法
    def browse_package_path(self):
        """浏览打包路径"""
        folder = QFileDialog.getExistingDirectory(self, "选择项目文件夹")
        if folder:
            self.package_path_input.setText(folder)
            # 自动填充应用名称
            app_name = os.path.basename(folder)
            if app_name and not self.package_app_name.text():
                self.package_app_name.setText(app_name)
    
    def start_packaging(self):
        """开始打包"""
        project_path = self.package_path_input.text().strip()
        if not project_path or not os.path.exists(project_path):
            self.showNotification("请选择有效的项目路径", "warning")
            return
        
        app_name = self.package_app_name.text().strip()
        if not app_name:
            self.showNotification("请输入应用名称", "warning")
            return
        
        # 检查选择的平台
        platforms = []
        if self.package_windows_cb.isChecked():
            platforms.append("Windows")
        if self.package_macos_cb.isChecked():
            platforms.append("macOS")
        if self.package_linux_cb.isChecked():
            platforms.append("Linux")
        
        if not platforms:
            self.showNotification("请至少选择一个目标平台", "warning")
            return
        
        # 开始打包过程
        self.package_progress_bar.setValueAnimated(0)
        self.package_status_label.setText("正在准备打包...")
        self.package_log_output.clear()
        self.package_result_list.clear()
        
        self.package_log_output.append(f"📦 开始打包应用: {app_name}")
        self.package_log_output.append(f"📁 项目路径: {project_path}")
        self.package_log_output.append(f"🎯 目标平台: {', '.join(platforms)}")
        
        # 创建打包线程
        self.package_thread = PackagingThread(
            project_path, app_name, platforms, 
            self.package_version.text() or "1.0.0",
            self.package_author.text() or "Unknown"
        )
        self.package_thread.progress_updated.connect(self.on_package_progress)
        self.package_thread.log_updated.connect(self.on_package_log)
        self.package_thread.completed.connect(self.on_package_completed)
        self.package_thread.start()
        
        self.showNotification("打包已开始", "success")
    
    def on_package_progress(self, value, status):
        """打包进度更新"""
        self.package_progress_bar.setValueAnimated(value)
        self.package_status_label.setText(status)
    
    def on_package_log(self, message):
        """打包日志更新"""
        self.package_log_output.append(message)
    
    def on_package_completed(self, results):
        """打包完成"""
        self.package_result_list.clear()
        for result in results:
            self.package_result_list.addItem(result)
        
        self.showNotification("打包完成", "success")
    
    def open_package_folder(self):
        """打开打包文件夹"""
        # 打开输出文件夹
        output_dir = os.path.join(os.getcwd(), "dist")
        if os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
        else:
            self.showNotification("输出文件夹不存在", "warning")
    
    def test_package(self):
        """测试安装包"""
        selected_items = self.package_result_list.selectedItems()
        if not selected_items:
            self.showNotification("请选择要测试的安装包", "warning")
            return
        
        package_name = selected_items[0].text()
        self.showNotification(f"正在测试安装包: {package_name}", "success")
        # 这里可以添加实际的测试逻辑
    
    # 部署页面功能方法
    def deploy_to_docker(self):
        """部署到Docker"""
        image_name = self.docker_image_input.text().strip()
        port_mapping = self.docker_port_input.text().strip()
        
        if not image_name:
            self.showNotification("请输入镜像名称", "warning")
            return
        
        self.docker_status.setText("状态: 正在部署...")
        self.docker_status.setStyleSheet("color: #D69E2E;")
        
        # 模拟Docker部署过程
        QTimer.singleShot(3000, lambda: self.on_docker_deploy_completed(True))
        
        self.showNotification("Docker部署已开始", "success")
    
    def on_docker_deploy_completed(self, success):
        """Docker部署完成"""
        if success:
            self.docker_status.setText("状态: 部署成功 ✅")
            self.docker_status.setStyleSheet("color: #38A169;")
            self.showNotification("Docker部署成功", "success")
        else:
            self.docker_status.setText("状态: 部署失败 ❌")
            self.docker_status.setStyleSheet("color: #E53E3E;")
            self.showNotification("Docker部署失败", "error")
    
    def deploy_to_kubernetes(self):
        """部署到Kubernetes"""
        namespace = self.k8s_namespace_input.text().strip()
        replicas = self.k8s_replicas_input.text().strip()
        
        if not namespace:
            self.showNotification("请输入命名空间", "warning")
            return
        
        try:
            replica_count = int(replicas)
            if replica_count <= 0:
                raise ValueError("副本数必须大于0")
        except ValueError:
            self.showNotification("请输入有效的副本数", "warning")
            return
        
        self.k8s_status.setText("状态: 正在部署...")
        self.k8s_status.setStyleSheet("color: #D69E2E;")
        
        # 模拟K8s部署过程
        QTimer.singleShot(4000, lambda: self.on_k8s_deploy_completed(True))
        
        self.showNotification("Kubernetes部署已开始", "success")
    
    def on_k8s_deploy_completed(self, success):
        """Kubernetes部署完成"""
        if success:
            self.k8s_status.setText("状态: 部署成功 ✅")
            self.k8s_status.setStyleSheet("color: #38A169;")
            self.showNotification("Kubernetes部署成功", "success")
        else:
            self.k8s_status.setText("状态: 部署失败 ❌")
            self.k8s_status.setStyleSheet("color: #E53E3E;")
            self.showNotification("Kubernetes部署失败", "error")
    
    def deploy_to_cloud(self):
        """部署到云平台"""
        platform = self.cloud_combo.currentText()
        access_key = self.cloud_access_key.text().strip()
        region = self.cloud_region.text().strip()
        
        if not access_key:
            self.showNotification("请输入访问密钥", "warning")
            return
        
        if not region:
            self.showNotification("请输入区域", "warning")
            return
        
        self.cloud_status.setText("状态: 正在部署...")
        self.cloud_status.setStyleSheet("color: #D69E2E;")
        
        # 模拟云平台部署过程
        QTimer.singleShot(5000, lambda: self.on_cloud_deploy_completed(True, platform))
        
        self.showNotification(f"正在部署到{platform}", "success")
    
    def on_cloud_deploy_completed(self, success, platform):
        """云平台部署完成"""
        if success:
            self.cloud_status.setText(f"状态: 已部署到{platform} ✅")
            self.cloud_status.setStyleSheet("color: #38A169;")
            self.showNotification(f"{platform}部署成功", "success")
        else:
            self.cloud_status.setText("状态: 部署失败 ❌")
            self.cloud_status.setStyleSheet("color: #E53E3E;")
            self.showNotification(f"{platform}部署失败", "error")


# 工作线程类
class PackagingThread(QThread):
    """打包工作线程"""
    progress_updated = pyqtSignal(int, str)
    log_updated = pyqtSignal(str)
    completed = pyqtSignal(list)
    
    def __init__(self, project_path, app_name, platforms, version, author):
        super().__init__()
        self.project_path = project_path
        self.app_name = app_name
        self.platforms = platforms
        self.version = version
        self.author = author
    
    def run(self):
        """运行打包过程"""
        try:
            results = []
            total_steps = len(self.platforms) * 3  # 每个平台3个步骤
            current_step = 0
            
            for platform in self.platforms:
                # 步骤1: 准备
                current_step += 1
                progress = int((current_step / total_steps) * 100)
                self.progress_updated.emit(progress, f"准备{platform}打包环境...")
                self.log_updated.emit(f"🔧 准备{platform}打包环境...")
                self.msleep(1000)  # 模拟处理时间
                
                # 步骤2: 构建
                current_step += 1
                progress = int((current_step / total_steps) * 100)
                self.progress_updated.emit(progress, f"构建{platform}应用...")
                self.log_updated.emit(f"🏗️ 构建{platform}应用...")
                self.msleep(2000)
                
                # 步骤3: 打包
                current_step += 1
                progress = int((current_step / total_steps) * 100)
                self.progress_updated.emit(progress, f"生成{platform}安装包...")
                self.log_updated.emit(f"📦 生成{platform}安装包...")
                self.msleep(1500)
                
                # 生成结果
                if platform == "Windows":
                    results.append(f"✅ {self.app_name}-{self.version}-windows-x64.exe")
                    results.append(f"✅ {self.app_name}-{self.version}-windows-x64.msi")
                elif platform == "macOS":
                    results.append(f"✅ {self.app_name}-{self.version}-macos.app")
                    results.append(f"✅ {self.app_name}-{self.version}-macos.dmg")
                elif platform == "Linux":
                    results.append(f"✅ {self.app_name}-{self.version}-linux-x64.AppImage")
                    results.append(f"✅ {self.app_name}-{self.version}-linux-x64.deb")
                
                self.log_updated.emit(f"✅ {platform}打包完成")
            
            self.progress_updated.emit(100, "打包完成")
            self.log_updated.emit("🎉 所有平台打包完成！")
            self.completed.emit(results)
            
        except Exception as e:
            self.log_updated.emit(f"❌ 打包失败: {str(e)}")
            self.completed.emit([])


class APITestThread(QThread):
    """API测试线程"""
    test_completed = pyqtSignal(bool, str)
    
    def __init__(self, api_manager, provider):
        super().__init__()
        self.api_manager = api_manager
        self.provider = provider
    
    def run(self):
        """运行测试"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success = loop.run_until_complete(
                self.api_manager.test_api(self.provider)
            )
            if success:
                self.test_completed.emit(True, "连接成功")
            else:
                self.test_completed.emit(False, "连接失败")
        except Exception as e:
            self.test_completed.emit(False, str(e))


class ChatThread(QThread):
    """聊天工作线程"""
    response_received = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, code_generator, message, provider):
        super().__init__()
        self.code_generator = code_generator
        self.message = message
        self.provider = provider
    
    def run(self):
        """运行线程"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            response = loop.run_until_complete(
                self.code_generator.generate_code(self.message, "python", self.provider)
            )
            self.response_received.emit(response)
        except Exception as e:
            self.error_occurred.emit(str(e))


# 对话框类
class CodeGenerationDialog(QDialog):
    """代码生成对话框"""
    
    def __init__(self, code_generator, parent=None):
        super().__init__(parent)
        self.code_generator = code_generator
        self.setWindowTitle("智能代码生成")
        self.setMinimumSize(600, 500)
        self.setupUI()
    
    def setupUI(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 需求输入
        layout.addWidget(QLabel("请描述您的代码需求:"))
        self.requirement_input = QTextEdit()
        self.requirement_input.setPlaceholderText("例如: 创建一个计算斐波那契数列的函数")
        layout.addWidget(self.requirement_input)
        
        # 语言选择
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("编程语言:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems(["python", "javascript", "java", "cpp", "csharp", "go", "rust"])
        lang_layout.addWidget(self.language_combo)
        lang_layout.addStretch()
        layout.addLayout(lang_layout)
        
        # 生成按钮
        generate_btn = QPushButton("🤖 生成代码")
        generate_btn.clicked.connect(self.generate_code)
        layout.addWidget(generate_btn)
        
        # 结果显示
        layout.addWidget(QLabel("生成的代码:"))
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)
        layout.addWidget(self.result_display)
        
        # 按钮
        button_layout = QHBoxLayout()
        copy_btn = QPushButton("复制代码")
        copy_btn.clicked.connect(self.copy_code)
        save_btn = QPushButton("保存文件")
        save_btn.clicked.connect(self.save_code)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(save_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
    
    def generate_code(self):
        """生成代码"""
        requirement = self.requirement_input.toPlainText().strip()
        if not requirement:
            QMessageBox.warning(self, "警告", "请输入代码需求")
            return
        
        language = self.language_combo.currentText()
        self.result_display.setText("正在生成代码，请稍候...")
        
        # 创建工作线程
        self.gen_thread = CodeGenThread(self.code_generator, requirement, language)
        self.gen_thread.code_generated.connect(self.on_code_generated)
        self.gen_thread.error_occurred.connect(self.on_error)
        self.gen_thread.start()
    
    def on_code_generated(self, code):
        """代码生成完成"""
        self.result_display.setText(code)
    
    def on_error(self, error):
        """生成错误"""
        self.result_display.setText(f"生成失败: {error}")
    
    def copy_code(self):
        """复制代码"""
        code = self.result_display.toPlainText()
        if code and code != "正在生成代码，请稍候...":
            QApplication.clipboard().setText(code)
            QMessageBox.information(self, "成功", "代码已复制到剪贴板")
    
    def save_code(self):
        """保存代码"""
        code = self.result_display.toPlainText()
        if not code or code == "正在生成代码，请稍候...":
            QMessageBox.warning(self, "警告", "没有可保存的代码")
            return
        
        language = self.language_combo.currentText()
        ext_map = {
            "python": ".py",
            "javascript": ".js", 
            "java": ".java",
            "cpp": ".cpp",
            "csharp": ".cs",
            "go": ".go",
            "rust": ".rs"
        }
        
        ext = ext_map.get(language, ".txt")
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存代码", f"generated_code{ext}", f"代码文件 (*{ext})"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                QMessageBox.information(self, "成功", f"代码已保存到: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")


class CodeGenThread(QThread):
    """代码生成线程"""
    code_generated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, code_generator, requirement, language):
        super().__init__()
        self.code_generator = code_generator
        self.requirement = requirement
        self.language = language
    
    def run(self):
        """运行线程"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            code = loop.run_until_complete(
                self.code_generator.generate_code(self.requirement, self.language)
            )
            self.code_generated.emit(code)
        except Exception as e:
            self.error_occurred.emit(str(e))


class CodeOptimizationDialog(QDialog):
    """代码优化对话框"""
    
    def __init__(self, code_generator, parent=None):
        super().__init__(parent)
        self.code_generator = code_generator
        self.setWindowTitle("代码优化")
        self.setMinimumSize(800, 600)
        self.setupUI()
    
    def setupUI(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 原始代码输入
        layout.addWidget(QLabel("请粘贴需要优化的代码:"))
        self.code_input = QTextEdit()
        self.code_input.setPlaceholderText("粘贴您的代码...")
        layout.addWidget(self.code_input)
        
        # 优化选项
        options_layout = QHBoxLayout()
        self.performance_check = QCheckBox("性能优化")
        self.readability_check = QCheckBox("可读性优化")
        self.security_check = QCheckBox("安全性检查")
        
        self.performance_check.setChecked(True)
        self.readability_check.setChecked(True)
        
        options_layout.addWidget(self.performance_check)
        options_layout.addWidget(self.readability_check)
        options_layout.addWidget(self.security_check)
        options_layout.addStretch()
        layout.addLayout(options_layout)
        
        # 优化按钮
        optimize_btn = QPushButton("⚡ 开始优化")
        optimize_btn.clicked.connect(self.optimize_code)
        layout.addWidget(optimize_btn)
        
        # 结果显示
        layout.addWidget(QLabel("优化后的代码:"))
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)
        layout.addWidget(self.result_display)
        
        # 按钮
        button_layout = QHBoxLayout()
        copy_btn = QPushButton("复制优化代码")
        copy_btn.clicked.connect(self.copy_code)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(copy_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
    
    def optimize_code(self):
        """优化代码"""
        code = self.code_input.toPlainText().strip()
        if not code:
            QMessageBox.warning(self, "警告", "请输入需要优化的代码")
            return
        
        # 构建优化提示
        options = []
        if self.performance_check.isChecked():
            options.append("性能优化")
        if self.readability_check.isChecked():
            options.append("可读性优化")
        if self.security_check.isChecked():
            options.append("安全性检查")
        
        prompt = f"""
请对以下代码进行优化，重点关注: {', '.join(options)}

原始代码:
```
{code}
```

请提供优化后的代码，并简要说明优化的地方。
"""
        
        self.result_display.setText("正在优化代码，请稍候...")
        
        # 创建工作线程
        self.opt_thread = CodeGenThread(self.code_generator, prompt, "python")
        self.opt_thread.code_generated.connect(self.on_code_optimized)
        self.opt_thread.error_occurred.connect(self.on_error)
        self.opt_thread.start()
    
    def on_code_optimized(self, result):
        """代码优化完成"""
        self.result_display.setText(result)
    
    def on_error(self, error):
        """优化错误"""
        self.result_display.setText(f"优化失败: {error}")
    
    def copy_code(self):
        """复制代码"""
        code = self.result_display.toPlainText()
        if code and code != "正在优化代码，请稍候...":
            QApplication.clipboard().setText(code)
            QMessageBox.information(self, "成功", "优化代码已复制到剪贴板")


class BugFixDialog(QDialog):
    """错误修复对话框"""
    
    def __init__(self, code_generator, parent=None):
        super().__init__(parent)
        self.code_generator = code_generator
        self.setWindowTitle("智能调试")
        self.setMinimumSize(800, 600)
        self.setupUI()
    
    def setupUI(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 错误代码输入
        layout.addWidget(QLabel("请粘贴有问题的代码:"))
        self.code_input = QTextEdit()
        self.code_input.setPlaceholderText("粘贴您的代码...")
        layout.addWidget(self.code_input)
        
        # 错误信息输入
        layout.addWidget(QLabel("错误信息 (可选):"))
        self.error_input = QTextEdit()
        self.error_input.setPlaceholderText("粘贴错误信息...")
        self.error_input.setMaximumHeight(100)
        layout.addWidget(self.error_input)
        
        # 修复按钮
        fix_btn = QPushButton("🐛 智能修复")
        fix_btn.clicked.connect(self.fix_bug)
        layout.addWidget(fix_btn)
        
        # 结果显示
        layout.addWidget(QLabel("修复建议:"))
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)
        layout.addWidget(self.result_display)
        
        # 按钮
        button_layout = QHBoxLayout()
        copy_btn = QPushButton("复制修复代码")
        copy_btn.clicked.connect(self.copy_code)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(copy_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
    
    def fix_bug(self):
        """修复错误"""
        code = self.code_input.toPlainText().strip()
        if not code:
            QMessageBox.warning(self, "警告", "请输入有问题的代码")
            return
        
        error_msg = self.error_input.toPlainText().strip()
        
        prompt = f"""
请分析以下代码中的问题并提供修复方案:

代码:
```
{code}
```
"""
        
        if error_msg:
            prompt += f"""
错误信息:
```
{error_msg}
```
"""
        
        prompt += """
请提供:
1. 问题分析
2. 修复后的代码
3. 修复说明
"""
        
        self.result_display.setText("正在分析问题，请稍候...")
        
        # 创建工作线程
        self.fix_thread = CodeGenThread(self.code_generator, prompt, "python")
        self.fix_thread.code_generated.connect(self.on_bug_fixed)
        self.fix_thread.error_occurred.connect(self.on_error)
        self.fix_thread.start()
    
    def on_bug_fixed(self, result):
        """错误修复完成"""
        self.result_display.setText(result)
    
    def on_error(self, error):
        """修复错误"""
        self.result_display.setText(f"修复失败: {error}")
    
    def copy_code(self):
        """复制代码"""
        code = self.result_display.toPlainText()
        if code and code != "正在分析问题，请稍候...":
            QApplication.clipboard().setText(code)
            QMessageBox.information(self, "成功", "修复建议已复制到剪贴板")


class DocGenerationDialog(QDialog):
    """文档生成对话框"""
    
    def __init__(self, code_generator, parent=None):
        super().__init__(parent)
        self.code_generator = code_generator
        self.setWindowTitle("文档生成")
        self.setMinimumSize(800, 600)
        self.setupUI()
    
    def setupUI(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 代码输入
        layout.addWidget(QLabel("请粘贴需要生成文档的代码:"))
        self.code_input = QTextEdit()
        self.code_input.setPlaceholderText("粘贴您的代码...")
        layout.addWidget(self.code_input)
        
        # 文档类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("文档类型:"))
        self.doc_type_combo = QComboBox()
        self.doc_type_combo.addItems(["API文档", "用户手册", "开发文档", "README"])
        type_layout.addWidget(self.doc_type_combo)
        type_layout.addStretch()
        layout.addLayout(type_layout)
        
        # 生成按钮
        generate_btn = QPushButton("📚 生成文档")
        generate_btn.clicked.connect(self.generate_doc)
        layout.addWidget(generate_btn)
        
        # 结果显示
        layout.addWidget(QLabel("生成的文档:"))
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)
        layout.addWidget(self.result_display)
        
        # 按钮
        button_layout = QHBoxLayout()
        copy_btn = QPushButton("复制文档")
        copy_btn.clicked.connect(self.copy_doc)
        save_btn = QPushButton("保存文档")
        save_btn.clicked.connect(self.save_doc)
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(save_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
    
    def generate_doc(self):
        """生成文档"""
        code = self.code_input.toPlainText().strip()
        if not code:
            QMessageBox.warning(self, "警告", "请输入需要生成文档的代码")
            return
        
        doc_type = self.doc_type_combo.currentText()
        
        prompt = f"""
请为以下代码生成{doc_type}:

代码:
```
{code}
```

请生成详细的{doc_type}，包括:
1. 功能说明
2. 参数说明
3. 返回值说明
4. 使用示例
5. 注意事项
"""
        
        self.result_display.setText("正在生成文档，请稍候...")
        
        # 创建工作线程
        self.doc_thread = CodeGenThread(self.code_generator, prompt, "markdown")
        self.doc_thread.code_generated.connect(self.on_doc_generated)
        self.doc_thread.error_occurred.connect(self.on_error)
        self.doc_thread.start()
    
    def on_doc_generated(self, result):
        """文档生成完成"""
        self.result_display.setText(result)
    
    def on_error(self, error):
        """生成错误"""
        self.result_display.setText(f"生成失败: {error}")
    
    def copy_doc(self):
        """复制文档"""
        doc = self.result_display.toPlainText()
        if doc and doc != "正在生成文档，请稍候...":
            QApplication.clipboard().setText(doc)
            QMessageBox.information(self, "成功", "文档已复制到剪贴板")
    
    def save_doc(self):
        """保存文档"""
        doc = self.result_display.toPlainText()
        if not doc or doc == "正在生成文档，请稍候...":
            QMessageBox.warning(self, "警告", "没有可保存的文档")
            return
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存文档", "documentation.md", "Markdown文件 (*.md);;文本文件 (*.txt)"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(doc)
                QMessageBox.information(self, "成功", f"文档已保存到: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")


class NewProjectDialog(QDialog):
    """新建项目对话框"""
    
    def __init__(self, project_manager, parent=None):
        super().__init__(parent)
        self.project_manager = project_manager
        self.setWindowTitle("新建项目")
        self.setMinimumSize(400, 300)
        self.setupUI()
    
    def setupUI(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 项目名称
        layout.addWidget(QLabel("项目名称:"))
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("输入项目名称...")
        layout.addWidget(self.name_input)
        
        # 项目模板
        layout.addWidget(QLabel("项目模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItems(["basic", "flask", "django", "fastapi", "react", "vue"])
        layout.addWidget(self.template_combo)
        
        # 项目描述
        layout.addWidget(QLabel("项目描述:"))
        self.desc_input = QTextEdit()
        self.desc_input.setPlaceholderText("输入项目描述...")
        self.desc_input.setMaximumHeight(100)
        layout.addWidget(self.desc_input)
        
        # 按钮
        button_layout = QHBoxLayout()
        create_btn = QPushButton("创建项目")
        create_btn.clicked.connect(self.accept)
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(create_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def get_project_info(self):
        """获取项目信息"""
        return {
            'name': self.name_input.text().strip(),
            'template': self.template_combo.currentText(),
            'description': self.desc_input.toPlainText().strip()
        }


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("AI Code Generator")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Code Team")
    
    # 创建主窗口
    window = ModernMainWindow()
    window.show()
    
    # 显示欢迎通知
    QTimer.singleShot(1000, lambda: window.showNotification("欢迎使用 AI Code Generator！", "success"))
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()