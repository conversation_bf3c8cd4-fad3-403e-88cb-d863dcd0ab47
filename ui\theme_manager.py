#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器
提供多种主题和自定义主题功能
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from typing import Dict, Any
import json
import os

class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "dark"
        self.themes_dir = "themes"
        self.custom_themes_file = "custom_themes.json"
        self.ensure_themes_dir()
        self.load_custom_themes()
    
    def ensure_themes_dir(self):
        """确保主题目录存在"""
        if not os.path.exists(self.themes_dir):
            os.makedirs(self.themes_dir)
    
    def load_custom_themes(self):
        """加载自定义主题"""
        self.custom_themes = {}
        if os.path.exists(self.custom_themes_file):
            try:
                with open(self.custom_themes_file, 'r', encoding='utf-8') as f:
                    self.custom_themes = json.load(f)
            except Exception as e:
                print(f"加载自定义主题失败: {e}")
    
    def save_custom_themes(self):
        """保存自定义主题"""
        try:
            with open(self.custom_themes_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_themes, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存自定义主题失败: {e}")
    
    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        builtin_themes = ["dark", "light", "blue", "green", "purple", "orange"]
        custom_themes = list(self.custom_themes.keys())
        return builtin_themes + custom_themes
    
    def get_theme_colors(self, theme_name: str) -> Dict[str, str]:
        """获取主题颜色配置"""
        if theme_name in self.custom_themes:
            return self.custom_themes[theme_name]
        
        # 内置主题颜色
        builtin_themes = {
            "dark": {
                "primary": "#2D3748",
                "secondary": "#4A5568",
                "accent": "#3182CE",
                "background": "#1A202C",
                "surface": "#2D3748",
                "text": "#FFFFFF",
                "text_secondary": "#A0AEC0",
                "border": "#4A5568",
                "hover": "#4A5568",
                "selected": "#3182CE",
                "error": "#E53E3E",
                "warning": "#D69E2E",
                "success": "#38A169",
                "info": "#3182CE"
            },
            "light": {
                "primary": "#FFFFFF",
                "secondary": "#F7FAFC",
                "accent": "#3182CE",
                "background": "#FFFFFF",
                "surface": "#F7FAFC",
                "text": "#1A202C",
                "text_secondary": "#4A5568",
                "border": "#E2E8F0",
                "hover": "#EDF2F7",
                "selected": "#3182CE",
                "error": "#E53E3E",
                "warning": "#D69E2E",
                "success": "#38A169",
                "info": "#3182CE"
            },
            "blue": {
                "primary": "#1E3A8A",
                "secondary": "#3B82F6",
                "accent": "#60A5FA",
                "background": "#0F172A",
                "surface": "#1E293B",
                "text": "#F1F5F9",
                "text_secondary": "#94A3B8",
                "border": "#334155",
                "hover": "#475569",
                "selected": "#3B82F6",
                "error": "#EF4444",
                "warning": "#F59E0B",
                "success": "#10B981",
                "info": "#3B82F6"
            },
            "green": {
                "primary": "#14532D",
                "secondary": "#16A34A",
                "accent": "#4ADE80",
                "background": "#0F172A",
                "surface": "#1E293B",
                "text": "#F0FDF4",
                "text_secondary": "#86EFAC",
                "border": "#166534",
                "hover": "#15803D",
                "selected": "#16A34A",
                "error": "#EF4444",
                "warning": "#F59E0B",
                "success": "#10B981",
                "info": "#3B82F6"
            },
            "purple": {
                "primary": "#581C87",
                "secondary": "#9333EA",
                "accent": "#A855F7",
                "background": "#0F0A1A",
                "surface": "#1E1B2E",
                "text": "#FAF5FF",
                "text_secondary": "#C4B5FD",
                "border": "#6B21A8",
                "hover": "#7C3AED",
                "selected": "#9333EA",
                "error": "#EF4444",
                "warning": "#F59E0B",
                "success": "#10B981",
                "info": "#3B82F6"
            },
            "orange": {
                "primary": "#9A3412",
                "secondary": "#EA580C",
                "accent": "#FB923C",
                "background": "#1A0F0A",
                "surface": "#2E1B1A",
                "text": "#FFF7ED",
                "text_secondary": "#FDBA74",
                "border": "#C2410C",
                "hover": "#DC2626",
                "selected": "#EA580C",
                "error": "#EF4444",
                "warning": "#F59E0B",
                "success": "#10B981",
                "info": "#3B82F6"
            }
        }
        
        return builtin_themes.get(theme_name, builtin_themes["dark"])
    
    def generate_stylesheet(self, theme_name: str) -> str:
        """生成主题样式表"""
        colors = self.get_theme_colors(theme_name)
        
        return f"""
        /* 主窗口 */
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text']};
        }}
        
        /* 通用组件 */
        QWidget {{
            background-color: {colors['background']};
            color: {colors['text']};
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        
        /* 按钮 */
        QPushButton {{
            background-color: {colors['primary']};
            color: {colors['text']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {colors['hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {colors['selected']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['secondary']};
            color: {colors['text_secondary']};
        }}
        
        /* 输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {colors['surface']};
            color: {colors['text']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            padding: 8px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors['accent']};
        }}
        
        /* 标签 */
        QLabel {{
            color: {colors['text']};
            background-color: transparent;
        }}
        
        /* 选项卡 */
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            background-color: {colors['surface']};
        }}
        
        QTabBar::tab {{
            background-color: {colors['secondary']};
            color: {colors['text']};
            border: 1px solid {colors['border']};
            padding: 8px 16px;
            margin-right: 2px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['accent']};
            color: {colors['text']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['hover']};
        }}
        
        /* 列表和树 */
        QListWidget, QTreeWidget {{
            background-color: {colors['surface']};
            color: {colors['text']};
            border: 1px solid {colors['border']};
            alternate-background-color: {colors['secondary']};
        }}
        
        QListWidget::item:selected, QTreeWidget::item:selected {{
            background-color: {colors['selected']};
            color: {colors['text']};
        }}
        
        QListWidget::item:hover, QTreeWidget::item:hover {{
            background-color: {colors['hover']};
        }}
        
        /* 滚动条 */
        QScrollBar:vertical {{
            background-color: {colors['secondary']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['accent']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['selected']};
        }}
        
        QScrollBar:horizontal {{
            background-color: {colors['secondary']};
            height: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {colors['accent']};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['selected']};
        }}
        
        /* 菜单 */
        QMenuBar {{
            background-color: {colors['primary']};
            color: {colors['text']};
            border-bottom: 1px solid {colors['border']};
        }}
        
        QMenuBar::item:selected {{
            background-color: {colors['hover']};
        }}
        
        QMenu {{
            background-color: {colors['surface']};
            color: {colors['text']};
            border: 1px solid {colors['border']};
        }}
        
        QMenu::item:selected {{
            background-color: {colors['selected']};
        }}
        
        /* 工具栏 */
        QToolBar {{
            background-color: {colors['primary']};
            border-bottom: 1px solid {colors['border']};
            spacing: 4px;
        }}
        
        QToolButton {{
            background-color: transparent;
            color: {colors['text']};
            border: none;
            border-radius: 4px;
            padding: 6px;
        }}
        
        QToolButton:hover {{
            background-color: {colors['hover']};
        }}
        
        QToolButton:pressed {{
            background-color: {colors['selected']};
        }}
        
        /* 状态栏 */
        QStatusBar {{
            background-color: {colors['primary']};
            color: {colors['text']};
            border-top: 1px solid {colors['border']};
        }}
        
        /* 进度条 */
        QProgressBar {{
            background-color: {colors['secondary']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {colors['accent']};
            border-radius: 3px;
        }}
        
        /* 分割器 */
        QSplitter::handle {{
            background-color: {colors['border']};
        }}
        
        QSplitter::handle:hover {{
            background-color: {colors['accent']};
        }}
        """
    
    def apply_theme(self, widget: QWidget, theme_name: str):
        """应用主题到组件"""
        stylesheet = self.generate_stylesheet(theme_name)
        widget.setStyleSheet(stylesheet)
        self.current_theme = theme_name
        self.theme_changed.emit(theme_name)
    
    def create_custom_theme(self, name: str, colors: Dict[str, str]):
        """创建自定义主题"""
        self.custom_themes[name] = colors
        self.save_custom_themes()
    
    def delete_custom_theme(self, name: str):
        """删除自定义主题"""
        if name in self.custom_themes:
            del self.custom_themes[name]
            self.save_custom_themes()
    
    def export_theme(self, theme_name: str, file_path: str):
        """导出主题"""
        colors = self.get_theme_colors(theme_name)
        theme_data = {
            "name": theme_name,
            "colors": colors,
            "version": "1.0"
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出主题失败: {e}")
            return False
    
    def import_theme(self, file_path: str) -> bool:
        """导入主题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
            
            if "name" in theme_data and "colors" in theme_data:
                self.create_custom_theme(theme_data["name"], theme_data["colors"])
                return True
            else:
                print("主题文件格式无效")
                return False
        except Exception as e:
            print(f"导入主题失败: {e}")
            return False
