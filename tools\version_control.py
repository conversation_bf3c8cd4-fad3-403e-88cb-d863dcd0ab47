# 版本控制系统集成

import os
import subprocess
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

class VCSType(Enum):
    GIT = "git"
    SVN = "svn"
    MERCURIAL = "hg"

@dataclass
class CommitInfo:
    """提交信息"""
    hash: str
    author: str
    date: datetime
    message: str
    files_changed: List[str]
    insertions: int = 0
    deletions: int = 0

@dataclass
class BranchInfo:
    """分支信息"""
    name: str
    is_current: bool
    last_commit: str
    last_commit_date: datetime

@dataclass
class FileStatus:
    """文件状态"""
    path: str
    status: str  # modified, added, deleted, untracked, etc.
    staged: bool = False

class VersionControlManager:
    """版本控制管理器"""
    
    def __init__(self, project_path: str = None):
        self.project_path = project_path or os.getcwd()
        self.vcs_type = self._detect_vcs_type()
        self.config = self._load_config()
    
    def _detect_vcs_type(self) -> Optional[VCSType]:
        """检测版本控制系统类型"""
        if os.path.exists(os.path.join(self.project_path, '.git')):
            return VCSType.GIT
        elif os.path.exists(os.path.join(self.project_path, '.svn')):
            return VCSType.SVN
        elif os.path.exists(os.path.join(self.project_path, '.hg')):
            return VCSType.MERCURIAL
        return None
    
    def _load_config(self) -> Dict:
        """加载配置"""
        config_file = os.path.join(self.project_path, '.vcs_config.json')
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            'auto_commit': False,
            'commit_template': '',
            'ignore_patterns': ['*.pyc', '__pycache__/', '.DS_Store'],
            'hooks': {
                'pre_commit': [],
                'post_commit': [],
                'pre_push': []
            }
        }
    
    def _run_command(self, command: List[str], cwd: str = None) -> Tuple[bool, str, str]:
        """运行命令"""
        try:
            result = subprocess.run(
                command,
                cwd=cwd or self.project_path,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def is_repository(self) -> bool:
        """检查是否为版本控制仓库"""
        return self.vcs_type is not None
    
    def init_repository(self, vcs_type: VCSType = VCSType.GIT) -> Tuple[bool, str]:
        """初始化仓库"""
        if self.is_repository():
            return False, "已经是版本控制仓库"
        
        if vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'init'])
            if success:
                self.vcs_type = VCSType.GIT
                # 创建初始的 .gitignore
                self._create_gitignore()
                return True, "Git仓库初始化成功"
            return False, f"初始化失败: {stderr}"
        
        return False, f"不支持的版本控制系统: {vcs_type}"
    
    def _create_gitignore(self):
        """创建 .gitignore 文件"""
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
temp/
logs/
*.log
"""
        gitignore_path = os.path.join(self.project_path, '.gitignore')
        if not os.path.exists(gitignore_path):
            with open(gitignore_path, 'w', encoding='utf-8') as f:
                f.write(gitignore_content)
    
    def get_status(self) -> List[FileStatus]:
        """获取文件状态"""
        if not self.is_repository():
            return []
        
        if self.vcs_type == VCSType.GIT:
            return self._get_git_status()
        
        return []
    
    def _get_git_status(self) -> List[FileStatus]:
        """获取Git状态"""
        success, stdout, stderr = self._run_command(['git', 'status', '--porcelain'])
        if not success:
            return []
        
        files = []
        for line in stdout.strip().split('\\n'):
            if not line:
                continue
            
            status_code = line[:2]
            file_path = line[3:]
            
            # 解析状态码
            staged = status_code[0] != ' ' and status_code[0] != '?'
            
            status_map = {
                'M': 'modified',
                'A': 'added',
                'D': 'deleted',
                'R': 'renamed',
                'C': 'copied',
                '?': 'untracked',
                '!': 'ignored'
            }
            
            status = status_map.get(status_code[1] if status_code[1] != ' ' else status_code[0], 'unknown')
            
            files.append(FileStatus(
                path=file_path,
                status=status,
                staged=staged
            ))
        
        return files
    
    def add_files(self, files: List[str]) -> Tuple[bool, str]:
        """添加文件到暂存区"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'add'] + files)
            return success, stderr if not success else "文件已添加到暂存区"
        
        return False, "不支持的版本控制系统"
    
    def commit(self, message: str, files: List[str] = None) -> Tuple[bool, str]:
        """提交更改"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        # 运行预提交钩子
        if not self._run_pre_commit_hooks():
            return False, "预提交钩子执行失败"
        
        if self.vcs_type == VCSType.GIT:
            # 如果指定了文件，先添加到暂存区
            if files:
                add_success, add_msg = self.add_files(files)
                if not add_success:
                    return False, f"添加文件失败: {add_msg}"
            
            # 提交
            success, stdout, stderr = self._run_command(['git', 'commit', '-m', message])
            
            if success:
                # 运行后提交钩子
                self._run_post_commit_hooks()
                return True, "提交成功"
            
            return False, f"提交失败: {stderr}"
        
        return False, "不支持的版本控制系统"
    
    def push(self, remote: str = 'origin', branch: str = None) -> Tuple[bool, str]:
        """推送到远程仓库"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        # 运行预推送钩子
        if not self._run_pre_push_hooks():
            return False, "预推送钩子执行失败"
        
        if self.vcs_type == VCSType.GIT:
            cmd = ['git', 'push', remote]
            if branch:
                cmd.append(branch)
            
            success, stdout, stderr = self._run_command(cmd)
            return success, stderr if not success else "推送成功"
        
        return False, "不支持的版本控制系统"
    
    def pull(self, remote: str = 'origin', branch: str = None) -> Tuple[bool, str]:
        """从远程仓库拉取"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            cmd = ['git', 'pull', remote]
            if branch:
                cmd.append(branch)
            
            success, stdout, stderr = self._run_command(cmd)
            return success, stderr if not success else "拉取成功"
        
        return False, "不支持的版本控制系统"
    
    def get_branches(self) -> List[BranchInfo]:
        """获取分支列表"""
        if not self.is_repository():
            return []
        
        if self.vcs_type == VCSType.GIT:
            return self._get_git_branches()
        
        return []
    
    def _get_git_branches(self) -> List[BranchInfo]:
        """获取Git分支"""
        success, stdout, stderr = self._run_command(['git', 'branch', '-v'])
        if not success:
            return []
        
        branches = []
        for line in stdout.strip().split('\\n'):
            if not line:
                continue
            
            is_current = line.startswith('*')
            line = line[2:] if is_current else line[2:]
            
            parts = line.split()
            if len(parts) >= 2:
                name = parts[0]
                commit_hash = parts[1]
                
                # 获取最后提交日期
                date_success, date_stdout, _ = self._run_command([
                    'git', 'log', '-1', '--format=%ci', commit_hash
                ])
                
                commit_date = datetime.now()
                if date_success and date_stdout.strip():
                    try:
                        commit_date = datetime.fromisoformat(date_stdout.strip().replace(' ', 'T', 1))
                    except:
                        pass
                
                branches.append(BranchInfo(
                    name=name,
                    is_current=is_current,
                    last_commit=commit_hash,
                    last_commit_date=commit_date
                ))
        
        return branches
    
    def create_branch(self, branch_name: str, checkout: bool = True) -> Tuple[bool, str]:
        """创建分支"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            if checkout:
                success, stdout, stderr = self._run_command(['git', 'checkout', '-b', branch_name])
            else:
                success, stdout, stderr = self._run_command(['git', 'branch', branch_name])
            
            return success, stderr if not success else f"分支 {branch_name} 创建成功"
        
        return False, "不支持的版本控制系统"
    
    def switch_branch(self, branch_name: str) -> Tuple[bool, str]:
        """切换分支"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'checkout', branch_name])
            return success, stderr if not success else f"已切换到分支 {branch_name}"
        
        return False, "不支持的版本控制系统"
    
    def get_commit_history(self, limit: int = 20) -> List[CommitInfo]:
        """获取提交历史"""
        if not self.is_repository():
            return []
        
        if self.vcs_type == VCSType.GIT:
            return self._get_git_commit_history(limit)
        
        return []
    
    def _get_git_commit_history(self, limit: int) -> List[CommitInfo]:
        """获取Git提交历史"""
        success, stdout, stderr = self._run_command([
            'git', 'log', f'--max-count={limit}',
            '--pretty=format:%H|%an|%ci|%s', '--name-only'
        ])
        
        if not success:
            return []
        
        commits = []
        lines = stdout.strip().split('\\n')
        i = 0
        
        while i < len(lines):
            if '|' in lines[i]:
                parts = lines[i].split('|')
                if len(parts) >= 4:
                    commit_hash = parts[0]
                    author = parts[1]
                    date_str = parts[2]
                    message = parts[3]
                    
                    # 解析日期
                    try:
                        commit_date = datetime.fromisoformat(date_str.replace(' ', 'T', 1))
                    except:
                        commit_date = datetime.now()
                    
                    # 获取修改的文件
                    files_changed = []
                    i += 1
                    while i < len(lines) and lines[i] and '|' not in lines[i]:
                        if lines[i].strip():
                            files_changed.append(lines[i].strip())
                        i += 1
                    
                    commits.append(CommitInfo(
                        hash=commit_hash,
                        author=author,
                        date=commit_date,
                        message=message,
                        files_changed=files_changed
                    ))
                    continue
            i += 1
        
        return commits
    
    def get_diff(self, file_path: str = None, staged: bool = False) -> str:
        """获取差异"""
        if not self.is_repository():
            return ""
        
        if self.vcs_type == VCSType.GIT:
            cmd = ['git', 'diff']
            if staged:
                cmd.append('--staged')
            if file_path:
                cmd.append(file_path)
            
            success, stdout, stderr = self._run_command(cmd)
            return stdout if success else ""
        
        return ""
    
    def stash_changes(self, message: str = None) -> Tuple[bool, str]:
        """暂存更改"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            cmd = ['git', 'stash']
            if message:
                cmd.extend(['push', '-m', message])
            
            success, stdout, stderr = self._run_command(cmd)
            return success, stderr if not success else "更改已暂存"
        
        return False, "不支持的版本控制系统"
    
    def apply_stash(self, stash_index: int = 0) -> Tuple[bool, str]:
        """应用暂存"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'stash', 'apply', f'stash@{{{stash_index}}}'])
            return success, stderr if not success else "暂存已应用"
        
        return False, "不支持的版本控制系统"
    
    def _run_pre_commit_hooks(self) -> bool:
        """运行预提交钩子"""
        hooks = self.config.get('hooks', {}).get('pre_commit', [])
        for hook in hooks:
            success, _, _ = self._run_command(hook.split())
            if not success:
                return False
        return True
    
    def _run_post_commit_hooks(self) -> bool:
        """运行后提交钩子"""
        hooks = self.config.get('hooks', {}).get('post_commit', [])
        for hook in hooks:
            self._run_command(hook.split())
        return True
    
    def _run_pre_push_hooks(self) -> bool:
        """运行预推送钩子"""
        hooks = self.config.get('hooks', {}).get('pre_push', [])
        for hook in hooks:
            success, _, _ = self._run_command(hook.split())
            if not success:
                return False
        return True
    
    def get_remote_info(self) -> Dict[str, str]:
        """获取远程仓库信息"""
        if not self.is_repository():
            return {}
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'remote', '-v'])
            if success:
                remotes = {}
                for line in stdout.strip().split('\\n'):
                    if line:
                        parts = line.split()
                        if len(parts) >= 2:
                            name = parts[0]
                            url = parts[1]
                            remotes[name] = url
                return remotes
        
        return {}
    
    def add_remote(self, name: str, url: str) -> Tuple[bool, str]:
        """添加远程仓库"""
        if not self.is_repository():
            return False, "不是版本控制仓库"
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'remote', 'add', name, url])
            return success, stderr if not success else f"远程仓库 {name} 添加成功"
        
        return False, "不支持的版本控制系统"
    
    def clone_repository(self, url: str, target_dir: str = None) -> Tuple[bool, str]:
        """克隆仓库"""
        cmd = ['git', 'clone', url]
        if target_dir:
            cmd.append(target_dir)
        
        success, stdout, stderr = self._run_command(cmd, cwd=os.path.dirname(self.project_path))
        return success, stderr if not success else "仓库克隆成功"
    
    def get_file_history(self, file_path: str, limit: int = 10) -> List[CommitInfo]:
        """获取文件历史"""
        if not self.is_repository():
            return []
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command([
                'git', 'log', f'--max-count={limit}',
                '--pretty=format:%H|%an|%ci|%s', '--', file_path
            ])
            
            if not success:
                return []
            
            commits = []
            for line in stdout.strip().split('\\n'):
                if '|' in line:
                    parts = line.split('|')
                    if len(parts) >= 4:
                        try:
                            commit_date = datetime.fromisoformat(parts[2].replace(' ', 'T', 1))
                        except:
                            commit_date = datetime.now()
                        
                        commits.append(CommitInfo(
                            hash=parts[0],
                            author=parts[1],
                            date=commit_date,
                            message=parts[3],
                            files_changed=[file_path]
                        ))
            
            return commits
        
        return []
    
    def blame_file(self, file_path: str) -> Dict[int, Dict[str, str]]:
        """获取文件责任信息"""
        if not self.is_repository():
            return {}
        
        if self.vcs_type == VCSType.GIT:
            success, stdout, stderr = self._run_command(['git', 'blame', '--line-porcelain', file_path])
            if not success:
                return {}
            
            blame_info = {}
            lines = stdout.strip().split('\\n')
            current_commit = None
            current_author = None
            current_date = None
            line_number = 1
            
            for line in lines:
                if line.startswith('author '):
                    current_author = line[7:]
                elif line.startswith('author-time '):
                    try:
                        timestamp = int(line[12:])
                        current_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                    except:
                        current_date = 'unknown'
                elif len(line) == 40 and all(c in '0123456789abcdef' for c in line):
                    current_commit = line[:8]  # 短哈希
                elif line.startswith('\\t'):
                    # 这是代码行
                    blame_info[line_number] = {
                        'commit': current_commit or 'unknown',
                        'author': current_author or 'unknown',
                        'date': current_date or 'unknown',
                        'content': line[1:]  # 移除制表符
                    }
                    line_number += 1
            
            return blame_info
        
        return {}
    
    def save_config(self):
        """保存配置"""
        config_file = os.path.join(self.project_path, '.vcs_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)