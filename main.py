#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI代码生成器 - 主程序入口

这是AI代码生成器的主入口文件，提供统一的启动方式。

运行方式:
    python main.py
    python main.py --modern    # 启动现代化界面
    python main.py --enhanced  # 启动增强版界面
    python main.py --demo      # 启动部署演示
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖"""
    missing_deps = []

    # 核心依赖检查
    core_deps = {
        "PyQt5": "PyQt5",
        "requests": "requests",
        "aiohttp": "aiohttp",
        "psutil": "psutil"
    }

    for import_name, package_name in core_deps.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_deps.append(package_name)

    # 可选依赖检查
    optional_deps = {
        "numpy": "numpy",
        "pandas": "pandas",
        "matplotlib": "matplotlib"
    }

    missing_optional = []
    for import_name, package_name in optional_deps.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_optional.append(package_name)

    if missing_deps:
        print("❌ 缺少以下核心依赖包:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements_enhanced.txt")
        return False

    if missing_optional:
        print("⚠️  缺少以下可选依赖包（不影响基本功能）:")
        for dep in missing_optional:
            print(f"   - {dep}")

    return True

def create_default_config():
    """创建默认配置文件"""
    try:
        from tools.config_manager import config_manager

        # 初始化配置管理器会自动创建默认配置
        config_manager.load_all_configs()

        # 检查是否有启用的API提供商
        enabled_providers = config_manager.get_enabled_api_providers()

        if not enabled_providers:
            print("💡 请配置API密钥以启用AI功能")
            print("   可以通过以下方式配置:")
            print("   1. 编辑 config/api_configs.json 文件")
            print("   2. 在应用程序中使用设置界面")
        else:
            print(f"✅ 已启用的API提供商: {', '.join(enabled_providers)}")

        print(f"✅ 配置系统初始化完成")

    except Exception as e:
        print(f"❌ 配置初始化失败: {e}")
        # 回退到原有方式
        config_file = "api_configs.json"
        example_file = "api_configs_example.json"

        if not os.path.exists(config_file) and os.path.exists(example_file):
            import shutil
            shutil.copy(example_file, config_file)
            print(f"✅ 已创建默认配置文件: {config_file}")
            print("💡 请编辑配置文件，添加您的API密钥")

def launch_modern_interface():
    """启动现代化界面"""
    try:
        # 启动性能监控
        from tools.performance_optimizer import performance_monitor
        performance_monitor.start_monitoring()
        print("📊 性能监控已启动")

        # 尝试启动简化的现代化界面
        try:
            from ui.simple_modern_interface import main as simple_modern_main
            print("🚀 启动简化现代化界面...")
            result = simple_modern_main()
        except Exception as e:
            print(f"⚠️ 简化界面启动失败，尝试原始界面: {e}")
            from ui.modern_interface import main as modern_main
            print("🚀 启动原始现代化界面...")
            result = modern_main()

        # 停止性能监控
        performance_monitor.stop_monitoring()
        print("📊 性能监控已停止")

        return result
    except ImportError as e:
        print(f"❌ 无法启动现代化界面: {e}")
        return False
    except Exception as e:
        print(f"❌ 现代化界面启动失败: {e}")
        return False

def launch_enhanced_interface():
    """启动增强版界面"""
    try:
        from ui.enhanced_main_window import main as enhanced_main
        print("🚀 启动增强版界面...")
        enhanced_main()
    except ImportError as e:
        print(f"❌ 无法启动增强版界面: {e}")
        return False
    except Exception as e:
        print(f"❌ 增强版界面启动失败: {e}")
        return False
    return True

def launch_deployment_demo():
    """启动部署演示"""
    try:
        from run_deployment_demo import main as demo_main
        print("🚀 启动部署演示...")
        demo_main()
    except ImportError as e:
        print(f"❌ 无法启动部署演示: {e}")
        return False
    except Exception as e:
        print(f"❌ 部署演示启动失败: {e}")
        return False
    return True

def create_executable():
    """创建可执行文件"""
    try:
        import PyInstaller.__main__
        import shutil
        from pathlib import Path
        
        print("📦 开始打包项目为可执行文件...")
        
        # 检查PyInstaller是否已安装
        try:
            import PyInstaller
        except ImportError:
            print("❌ PyInstaller未安装，请先运行: pip install pyinstaller")
            return False
        
        # 获取项目根目录
        project_root = Path(__file__).parent.absolute()
        
        # 定义打包参数
        pyinstaller_args = [
            str(project_root / 'main.py'),  # 使用绝对路径
            '--onefile',  # 打包为单个可执行文件
            '--windowed',  # 不显示控制台窗口（Windows）
            '--name=AI代码生成器',
            '--icon=NONE',  # 如果有图标文件可以指定路径
            f'--distpath={project_root}/dist',
            f'--workpath={project_root}/build',
            f'--specpath={project_root}',
            '--clean'
        ]
        
        # 添加数据文件（处理特殊字符）
        ui_path = project_root / "ui"
        if ui_path.exists():
            pyinstaller_args.append(f'--add-data={str(ui_path.resolve())}{os.pathsep}ui')
            
        # 添加配置文件
        config_files = ['api_configs.json', 'api_configs_example.json']
        for config_file in config_files:
            if (project_root / config_file).exists():
                pyinstaller_args.append(f'--add-data={str((project_root / config_file).resolve())}{os.pathsep}.')

        # 添加requirements文件
        req_files = ['requirements.txt', 'requirements_enhanced.txt']
        for req_file in req_files:
            if (project_root / req_file).exists():
                pyinstaller_args.append(f'--add-data={str((project_root / req_file).resolve())}{os.pathsep}.')
        
        print("🔧 打包参数:")
        for arg in pyinstaller_args:
            print(f"   {arg}")
        
        # 执行打包
        PyInstaller.__main__.run(pyinstaller_args)
        
        # 检查打包结果
        exe_path = project_root / "dist" / "AI代码生成器.exe"  # Windows
        if not exe_path.exists():
            exe_path = project_root / "dist" / "AI代码生成器"  # Linux/Mac
        
        if exe_path.exists():
            print(f"✅ 打包成功！可执行文件位置: {exe_path}")
            return True
        else:
            print("❌ 打包失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        return False

# 新增: 增强版打包功能
def create_executable_advanced():
    """创建增强版可执行文件"""
    try:
        import PyInstaller.__main__
        from pathlib import Path
        import json
        
        print("📦 开始高级打包项目为可执行文件...")
        
        # 检查PyInstaller是否已安装
        try:
            import PyInstaller
        except ImportError:
            print("❌ PyInstaller未安装，请先运行: pip install pyinstaller")
            return False
        
        # 获取项目根目录
        project_root = Path(__file__).parent.absolute()
        
        # 读取或创建打包配置
        pack_config_file = project_root / "pack_config.json"
        if pack_config_file.exists():
            with open(pack_config_file, 'r', encoding='utf-8') as f:
                pack_config = json.load(f)
        else:
            # 默认配置
            pack_config = {
                "name": "AI代码生成器",
                "onefile": True,
                "windowed": True,
                "console": False,
                "icon": "NONE",
                "add_data": [],
                "hidden_imports": [],
                "exclude_modules": [],
                "runtime_hooks": [],  # 新增运行时钩子
                "upx": False,         # 新增UPX压缩选项
                "key": "",            # 新增加密密钥
                "clean": True         # 新增清理选项
            }
            # 保存默认配置
            with open(pack_config_file, 'w', encoding='utf-8') as f:
                json.dump(pack_config, f, ensure_ascii=False, indent=2)
            print(f"💡 已创建打包配置文件: {pack_config_file}")
        
        # 定义打包参数
        pyinstaller_args = ['main.py']
        
        # 根据配置添加参数
        if pack_config.get("onefile", True):
            pyinstaller_args.append('--onefile')
        
        if pack_config.get("windowed", True):
            pyinstaller_args.append('--windowed')
        elif pack_config.get("console", False):
            pyinstaller_args.append('--console')
            
        if pack_config.get("name"):
            pyinstaller_args.extend(['--name', pack_config["name"]])
            
        if pack_config.get("icon") and pack_config["icon"] != "NONE":
            pyinstaller_args.extend(['--icon', pack_config["icon"]])
        else:
            pyinstaller_args.append('--icon=NONE')
            
        # 新增UPX支持
        if pack_config.get("upx", False):
            pyinstaller_args.append('--upx')
            
        # 新增加密支持
        if pack_config.get("key"):
            pyinstaller_args.extend(['--key', pack_config["key"]])
            
        # 新增clean参数
        if pack_config.get("clean", True):
            pyinstaller_args.append('--clean')
            
        pyinstaller_args.extend([
            f'--distpath={project_root}/dist',
            f'--workpath={project_root}/build',
            f'--specpath={project_root}',
        ])
        
        # 添加数据文件
        default_data = [
            ('ui', 'ui'),
            ('api_configs.json', '.'),
            ('api_configs_example.json', '.'),
            ('requirements.txt', '.'),
            ('requirements_enhanced.txt', '.')
        ]
        
        all_data = default_data + pack_config.get("add_data", [])
        for src, dest in all_data:
            src_path = project_root / src
            if src_path.exists():
                pyinstaller_args.append(f'--add-data={src_path}{os.pathsep}{dest}')
        
        # 添加隐藏导入
        for hidden_import in pack_config.get("hidden_imports", []):
            pyinstaller_args.extend(['--hidden-import', hidden_import])
        
        # 排除模块
        for exclude_module in pack_config.get("exclude_modules", []):
            pyinstaller_args.extend(['--exclude-module', exclude_module])
            
        # 添加运行时钩子
        for hook in pack_config.get("runtime_hooks", []):
            pyinstaller_args.extend(['--runtime-hook', hook])
        
        print("🔧 高级打包参数:")
        for arg in pyinstaller_args:
            print(f"   {arg}")
        
        # 执行打包
        PyInstaller.__main__.run(pyinstaller_args)
        
        # 检查打包结果
        exe_name = pack_config.get("name", "AI代码生成器")
        exe_path = project_root / "dist" / f"{exe_name}.exe"  # Windows
        if not exe_path.exists():
            exe_path = project_root / "dist" / exe_name  # Linux/Mac
        
        if exe_path.exists():
            print(f"✅ 高级打包成功！可执行文件位置: {exe_path}")
            return True
        else:
            print("❌ 高级打包失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"❌ 高级打包过程中出现错误: {e}")
        return False

# 新增: 调试器功能增强
def launch_debugger():
    """启动调试器"""
    try:
        print("🐛 启动AI代码生成器调试器...")
        import logging
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('debug.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("调试器已启动")
        
        # 增加模块搜索路径
        import sys
        sys.path.append(str(Path(__file__).parent / 'ui'))
        
        # 动态导入调试界面
        try:
            import importlib
            debug_interface = importlib.import_module('debug_interface')
            from PyQt5.QtWidgets import QApplication
            app = QApplication(sys.argv)
            debug_window = getattr(debug_interface, 'DebugInterface')()
            debug_window.show()
            logger.info("调试界面已显示")
            debug_window.init_debugger()
            return app.exec_()
        except AttributeError as e:
            print(f"❌ 调试界面模块错误: {e}")
            return 1
            
    except ImportError as e:
        print(f"❌ 无法启动调试器界面: {e}")
        # 启动命令行调试模式
        print("🔧 启动命令行调试模式...")
        import logging
        logging.basicConfig(level=logging.DEBUG)
        print("💡 调试模式已启用，将显示详细日志信息")
        return 0
    except Exception as e:
        print(f"❌ 调试器启动失败: {e}")
        return 1

# 新增: 部署功能增强
def deploy_application():
    """部署应用程序"""
    try:
        print("🌐 开始部署应用程序...")
        
        # 检查Docker是否安装
        import subprocess
        import json
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"🐳 {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  Docker未安装或未在PATH中，跳过Docker部署")
        
        # 加载部署配置
        deploy_config = {}
        deploy_config_path = Path("deploy_config.json")
        if deploy_config_path.exists():
            with open(deploy_config_path, 'r', encoding='utf-8') as f:
                deploy_config = json.load(f)
        
        # 检查是否有Dockerfile
        dockerfile_path = Path("Dockerfile")
        if not dockerfile_path.exists():
            print("📝 创建默认Dockerfile...")
            create_default_dockerfile()
        
        # 构建Docker镜像
        project_name = deploy_config.get("project_name", "ai-code-generator")
        try:
            print("🔨 构建Docker镜像...")
            subprocess.run(['docker', 'build', '-t', project_name, '.'], 
                         check=True)
            print(f"✅ Docker镜像 {project_name} 构建成功")
        except subprocess.CalledProcessError:
            print("❌ Docker镜像构建失败")
            return False
        except FileNotFoundError:
            print("⏭️  跳过Docker部署")
        
        # 检查是否有docker-compose.yml
        compose_file = Path("docker-compose.yml")
        if not compose_file.exists():
            print("📝 创建默认docker-compose.yml...")
            create_default_compose_file()
        
        # 新增云服务部署
        cloud_provider = deploy_config.get("cloud_provider")
        if cloud_provider:
            print(f"☁️  正在部署到 {cloud_provider}...")
            if cloud_provider == "aliyun":
                deploy_to_aliyun(deploy_config.get("aliyun_config", {}))
            elif cloud_provider == "aws":
                deploy_to_aws(deploy_config.get("aws_config", {}))
            elif cloud_provider == "azure":
                deploy_to_azure(deploy_config.get("azure_config", {}))
            print(f"✅ 已成功部署到 {cloud_provider}")
        
        print("✅ 应用部署完成")
        return True
        
    except Exception as e:
        print(f"❌ 部署过程中出现错误: {e}")
        return False

def deploy_to_aliyun(config):
    """部署到阿里云"""
    print("阿里云部署配置:", config)
    # 这里添加实际的阿里云部署逻辑

def deploy_to_aws(config):
    """部署到AWS"""
    print("AWS部署配置:", config)
    # 这里添加实际的AWS部署逻辑

def deploy_to_azure(config):
    """部署到Azure"""
    print("Azure部署配置:", config)
    # 这里添加实际的Azure部署逻辑

def create_default_dockerfile():
    """创建默认Dockerfile"""
    dockerfile_content = '''# AI代码生成器 Dockerfile
FROM python:3.8-slim

WORKDIR /app

# 复制依赖文件
COPY requirements.txt requirements_enhanced.txt ./

# 安装依赖
RUN pip install --no-cache-dir -r requirements_enhanced.txt

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["python", "main.py"]
'''
    
    with open("Dockerfile", "w", encoding="utf-8") as f:
        f.write(dockerfile_content)
    print("✅ 默认Dockerfile已创建")

def create_default_compose_file():
    """创建默认docker-compose.yml"""
    compose_content = '''version: '3.8'

services:
  ai-code-generator:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
    environment:
      - PYTHONUNBUFFERED=1
'''
    
    with open("docker-compose.yml", "w", encoding="utf-8") as f:
        f.write(compose_content)
    print("✅ 默认docker-compose.yml已创建")

# 新增: 设置功能增强
def launch_settings():
    """启动设置界面"""
    try:
        print("⚙️  启动设置界面...")
        # 修改导入方式，避免Pylance报错
        from ui import settings_interface
        from PyQt5.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        settings_window = settings_interface.SettingsInterface()
        settings_window.show()
        
        # 新增设置界面初始化
        settings_window.init_settings()
        
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ 无法启动设置界面: {e}")
        # 提供命令行设置选项
        handle_cli_settings()
        return 0
    except Exception as e:
        print(f"❌ 设置界面启动失败: {e}")
        return 1

def handle_cli_settings():
    """处理命令行设置"""
    print("🔧 命令行设置选项:")
    print("   1. 配置API密钥")
    print("   2. 配置打包选项")
    print("   3. 配置部署选项")
    print("   4. 配置云服务部署")
    print("   5. 界面设置")
    print("   6. 模型参数设置")
    print("   7. 重置所有配置")
    print("   8. 返回上级菜单")
    
    choice = input("请选择操作 (1-8): ").strip()
    
    if choice == "1":
        configure_api_keys()
    elif choice == "2":
        configure_pack_options()
    elif choice == "3":
        configure_deploy_options()
    elif choice == "4":
        configure_cloud_deploy()
    elif choice == "5":
        configure_ui_settings()
    elif choice == "6":
        configure_model_settings()
    elif choice == "7":
        reset_all_configurations()
    elif choice == "8":
        return
    else:
        print("❌ 无效选择")

def configure_api_keys():
    """配置API密钥"""
    import json
    
    config_file = "api_configs.json"
    if not os.path.exists(config_file):
        print("📝 创建新的API配置文件...")
        config = {
            "openai": {
                "api_key": "",
                "base_url": "https://api.openai.com/v1"
            },
            "anthropic": {
                "api_key": ""
            },
            "google": {
                "api_key": ""
            }
        }
    else:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    print("🔑 配置API密钥:")
    for provider in config:
        current_key = config[provider].get("api_key", "")
        if current_key:
            print(f"   {provider}: 已配置 (****{current_key[-4:]})")
        else:
            print(f"   {provider}: 未配置")
        
        new_key = input(f"   输入 {provider} API密钥 (留空跳过): ").strip()
        if new_key:
            config[provider]["api_key"] = new_key
            print(f"   ✅ {provider} API密钥已更新")
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ API密钥配置已保存")

def configure_pack_options():
    """配置打包选项"""
    import json
    
    pack_config_file = "pack_config.json"
    if os.path.exists(pack_config_file):
        with open(pack_config_file, 'r', encoding='utf-8') as f:
            pack_config = json.load(f)
    else:
        pack_config = {
            "name": "AI代码生成器",
            "onefile": True,
            "windowed": True,
            "console": False,
            "icon": "NONE",
            "runtime_hooks": [],
            "upx": False,
            "key": "",
            "clean": True
        }
    
    print("📦 配置打包选项:")
    print(f"   应用名称: {pack_config.get('name', 'AI代码生成器')}")
    print(f"   单文件打包: {'是' if pack_config.get('onefile', True) else '否'}")
    print(f"   无窗口模式: {'是' if pack_config.get('windowed', True) else '否'}")
    print(f"   控制台模式: {'是' if pack_config.get('console', False) else '否'}")
    print(f"   图标文件: {pack_config.get('icon', 'NONE')}")
    print(f"   UPX压缩: {'是' if pack_config.get('upx', False) else '否'}")
    print(f"   加密密钥: {'已设置' if pack_config.get('key') else '未设置'}")
    
    # 交互式配置
    name = input("   应用名称 (留空保持不变): ").strip()
    if name:
        pack_config["name"] = name
    
    onefile = input("   单文件打包? (y/n, 留空保持不变): ").strip().lower()
    if onefile in ['y', 'yes', '是']:
        pack_config["onefile"] = True
    elif onefile in ['n', 'no', '否']:
        pack_config["onefile"] = False
    
    windowed = input("   无窗口模式? (y/n, 留空保持不变): ").strip().lower()
    if windowed in ['y', 'yes', '是']:
        pack_config["windowed"] = True
        pack_config["console"] = False
    elif windowed in ['n', 'no', '否']:
        pack_config["windowed"] = False
    
    console = input("   控制台模式? (y/n, 留空保持不变): ").strip().lower()
    if console in ['y', 'yes', '是']:
        pack_config["console"] = True
        pack_config["windowed"] = False
    elif console in ['n', 'no', '否']:
        pack_config["console"] = False
    
    icon = input("   图标文件路径 (留空保持不变): ").strip()
    if icon:
        pack_config["icon"] = icon
    
    upx = input("   使用UPX压缩? (y/n, 留空保持不变): ").strip().lower()
    if upx in ['y', 'yes', '是']:
        pack_config["upx"] = True
    elif upx in ['n', 'no', '否']:
        pack_config["upx"] = False
        
    key = input("   加密密钥 (留空保持不变): ").strip()
    if key:
        pack_config["key"] = key
        
    clean = input("   清理构建文件? (y/n, 默认是): ").strip().lower()
    if clean in ['n', 'no', '否']:
        pack_config["clean"] = False
    else:
        pack_config["clean"] = True
    
    # 添加运行时钩子
    add_hooks = input("   添加运行时钩子文件 (多个用逗号分隔): ").strip()
    if add_hooks:
        hooks = [h.strip() for h in add_hooks.split(',')]
        # 验证钩子文件是否存在
        valid_hooks = []
        for hook in hooks:
            hook_path = Path(hook)
            if hook_path.exists():
                valid_hooks.append(hook)
            else:
                print(f"⚠️  钩子文件 {hook} 不存在，已跳过")
        pack_config["runtime_hooks"] = valid_hooks
    
    with open(pack_config_file, 'w', encoding='utf-8') as f:
        json.dump(pack_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 打包配置已保存")

def configure_deploy_options():
    """配置部署选项"""
    print("🌐 配置部署选项:")
    print("   1. Docker部署配置")
    print("   2. 云服务部署配置")
    print("   3. 返回上级菜单")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        configure_docker_deploy()
    elif choice == "2":
        configure_cloud_deploy()
    elif choice == "3":
        return
    else:
        print("❌ 无效选择")

def configure_docker_deploy():
    """配置Docker部署"""
    print("🐳 配置Docker部署:")
    print("   Docker部署配置已启用")
    print("   请确保已安装Docker")

def configure_cloud_deploy():
    """配置云服务部署"""
    print("☁️  配置云服务部署:")
    print("   1. 阿里云")
    print("   2. AWS")
    print("   3. Azure")
    print("   4. 返回上级菜单")
    
    choice = input("请选择云服务商 (1-4): ").strip()
    
    if choice == "4":
        return
    
    cloud_providers = {
        "1": "aliyun",
        "2": "aws",
        "3": "azure"
    }
    
    provider = cloud_providers.get(choice)
    if not provider:
        print("❌ 无效选择")
        return
    
    import json
    
    deploy_config_file = "deploy_config.json"
    if os.path.exists(deploy_config_file):
        with open(deploy_config_file, 'r', encoding='utf-8') as f:
            deploy_config = json.load(f)
    else:
        deploy_config = {}
    
    deploy_config["cloud_provider"] = provider
    provider_config = deploy_config.get(f"{provider}_config", {})
    
    print(f"\n🔗 配置 {provider} 参数:")
    
    if provider == "aliyun":
        print("阿里云配置说明: 需要RAM访问密钥和区域信息")
        provider_config["access_key_id"] = input("   Access Key ID (留空保持不变): ").strip() or provider_config.get("access_key_id")
        provider_config["access_key_secret"] = input("   Access Key Secret (留空保持不变): ").strip() or provider_config.get("access_key_secret")
        provider_config["region"] = input("   区域 (如: cn-hangzhou, 留空默认): ").strip() or provider_config.get("region", "cn-hangzhou")
        
    elif provider == "aws":
        print("AWS配置说明: 需要访问密钥和区域信息")
        provider_config["aws_access_key_id"] = input("   AWS Access Key ID (留空保持不变): ").strip() or provider_config.get("aws_access_key_id")
        provider_config["aws_secret_access_key"] = input("   AWS Secret Access Key (留空保持不变): ").strip() or provider_config.get("aws_secret_access_key")
        provider_config["region"] = input("   区域 (如: us-east-1, 留空默认): ").strip() or provider_config.get("region", "us-east-1")
        
    elif provider == "azure":
        print("Azure配置说明: 需要订阅信息和服务主体凭证")
        provider_config["subscription_id"] = input("   订阅ID (留空保持不变): ").strip() or provider_config.get("subscription_id")
        provider_config["tenant_id"] = input("   租户ID (留空保持不变): ").strip() or provider_config.get("tenant_id")
        provider_config["client_id"] = input("   客户端ID (留空保持不变): ").strip() or provider_config.get("client_id")
        provider_config["client_secret"] = input("   客户端密钥 (留空保持不变): ").strip() or provider_config.get("client_secret")
    
    deploy_config[f"{provider}_config"] = provider_config
    
    with open(deploy_config_file, 'w', encoding='utf-8') as f:
        json.dump(deploy_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ {provider} 配置已保存")

def configure_ui_settings():
    """配置界面设置"""
    print("🎨 配置界面设置:")
    print("   1. 主题设置")
    print("   2. 字体设置")
    print("   3. 快捷键设置")
    print("   4. 返回上级菜单")
    
    choice = input("请选择操作 (1-4): ").strip()
    
    if choice == "4":
        return
    
    import json
    
    ui_config_file = "ui_config.json"
    if os.path.exists(ui_config_file):
        with open(ui_config_file, 'r', encoding='utf-8') as f:
            ui_config = json.load(f)
    else:
        ui_config = {
            "theme": "default",
            "font_family": "Segoe UI",
            "font_size": 12,
            "shortcut_keys": {}
        }
    
    if choice == "1":
        print("   当前主题:", ui_config.get("theme", "default"))
        themes = ["default", "dark", "light", "blue", "green"]
        print("   可用主题:", ", ".join(themes))
        new_theme = input("   输入新主题名称: ").strip()
        if new_theme in themes:
            ui_config["theme"] = new_theme
            print("✅ 主题已更新")
        else:
            print("❌ 无效主题")
            
    elif choice == "2":
        print(f"   当前字体: {ui_config.get('font_family', 'Segoe UI')} {ui_config.get('font_size', 12)}")
        font_family = input("   输入新字体家族 (留空保持不变): ").strip()
        font_size = input("   输入新字体大小 (留空保持不变): ").strip()
        
        if font_family:
            ui_config["font_family"] = font_family
        if font_size.isdigit():
            ui_config["font_size"] = int(font_size)
        print("✅ 字体设置已更新")
            
    elif choice == "3":
        print("   当前快捷键配置:", ui_config.get("shortcut_keys", {}))
        print("   输入快捷键配置 (格式: action=key)")
        print("   例如: run_code=F5")
        print("   输入空行结束配置")
        
        shortcuts = {}
        while True:
            line = input("   ").strip()
            if not line:
                break
            if "=" in line:
                action, key = line.split("=", 1)
                shortcuts[action.strip()] = key.strip()
        
        if shortcuts:
            ui_config["shortcut_keys"] = shortcuts
            print("✅ 快捷键已更新")
    
    with open(ui_config_file, 'w', encoding='utf-8') as f:
        json.dump(ui_config, f, ensure_ascii=False, indent=2)
    print("✅ 界面设置已保存")

def configure_model_settings():
    """配置模型参数"""
    print("🧠 配置模型参数:")
    print("   1. 默认模型")
    print("   2. API速率限制")
    print("   3. 缓存设置")
    print("   4. 返回上级菜单")
    
    choice = input("请选择操作 (1-4): ").strip()
    
    if choice == "4":
        return
    
    import json
    
    model_config_file = "model_config.json"
    if os.path.exists(model_config_file):
        with open(model_config_file, 'r', encoding='utf-8') as f:
            model_config = json.load(f)
    else:
        model_config = {
            "default_model": "gpt-3.5-turbo",
            "max_tokens": 1024,
            "temperature": 0.7,
            "cache_size": 100,
            "rate_limit": {
                "requests_per_minute": 60,
                "tokens_per_minute": 10000
            }
        }
    
    if choice == "1":
        print("   当前默认模型:", model_config.get("default_model", "gpt-3.5-turbo"))
        new_model = input("   输入新默认模型名称: ").strip()
        if new_model:
            model_config["default_model"] = new_model
            print("✅ 默认模型已更新")
            
    elif choice == "2":
        print("   当前速率限制:", model_config.get("rate_limit", {}))
        rpm = input("   每分钟请求次数 (留空保持不变): ").strip()
        tpm = input("   每分钟token数 (留空保持不变): ").strip()
        
        if rpm.isdigit():
            model_config["rate_limit"]["requests_per_minute"] = int(rpm)
        if tpm.isdigit():
            model_config["rate_limit"]["tokens_per_minute"] = int(tpm)
        print("✅ 速率限制已更新")
            
    elif choice == "3":
        print("   当前缓存大小:", model_config.get("cache_size", 100))
        new_size = input("   输入新缓存大小 (留空保持不变): ").strip()
        if new_size.isdigit():
            model_config["cache_size"] = int(new_size)
            print("✅ 缓存大小已更新")
    
    with open(model_config_file, 'w', encoding='utf-8') as f:
        json.dump(model_config, f, ensure_ascii=False, indent=2)
    print("✅ 模型参数已保存")

def reset_all_configurations():
    """重置所有配置"""
    confirm = input("⚠️  确定要重置所有配置吗? (y/N): ").strip().lower()
    if confirm in ['y', 'yes', '是']:
        import os
        config_files = [
            "api_configs.json",
            "pack_config.json",
            "deploy_config.json"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    os.remove(config_file)
                    print(f"🗑️  已删除 {config_file}")
                except Exception as e:
                    print(f"❌ 删除 {config_file} 失败: {e}")
        
        print("✅ 所有配置已重置")
    else:
        print("↩️  取消重置操作")

def show_welcome():
    """显示欢迎信息"""
    print("=" * 60)
    print("🚀 AI代码生成器 v2.0")
    print("=" * 60)
    print("🎯 功能特性:")
    print("   • 🤖 多AI模型支持 (OpenAI, Anthropic, Google等)")
    print("   • 🎨 现代化用户界面")
    print("   • 🔧 智能代码生成")
    print("   • 🐛 AI辅助调试")
    print("   • 📦 多平台打包")
    print("   • 🚀 容器化部署")
    print("   • 📚 自动文档生成")
    print()

def show_usage():
    """显示使用说明"""
    print("📖 使用说明:")
    print("   python main.py              # 启动默认界面")
    print("   python main.py --modern     # 启动现代化界面")
    print("   python main.py --enhanced   # 启动增强版界面")
    print("   python main.py --demo       # 启动部署演示")
    print("   python main.py --package    # 打包为可执行程序")
    print("   python main.py --package-advanced # 高级打包")
    print("   python main.py --debug      # 启动调试模式")
    print("   python main.py --debugger   # 启动调试器")
    print("   python main.py --deploy     # 部署应用")
    print("   python main.py --settings   # 启动设置")
    print("   python main.py --help       # 显示帮助信息")
    print()

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="AI代码生成器 - 智能代码生成与打包工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python main.py                # 启动默认界面
  python main.py --modern       # 启动现代化界面
  python main.py --enhanced     # 启动增强版界面
  python main.py --demo         # 启动部署演示
  python main.py --package      # 打包为可执行程序
        """
    )
    
    parser.add_argument(
        "--modern", 
        action="store_true", 
        help="启动现代化界面 (推荐)"
    )
    
    parser.add_argument(
        "--enhanced", 
        action="store_true", 
        help="启动增强版界面"
    )
    
    parser.add_argument(
        "--demo", 
        action="store_true", 
        help="启动部署演示"
    )
    
    parser.add_argument(
        "--check-deps", 
        action="store_true", 
        help="检查依赖包"
    )
    
    # 添加打包参数
    parser.add_argument(
        "--package", 
        action="store_true", 
        help="打包项目为可执行程序"
    )
    
    # 添加高级打包参数
    parser.add_argument(
        "--package-advanced",
        action="store_true",
        help="高级打包项目为可执行程序"
    )
    
    # 添加调试参数
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    # 添加调试器参数
    parser.add_argument(
        "--debugger",
        action="store_true",
        help="启动调试器"
    )
    
    # 添加部署参数
    parser.add_argument(
        "--deploy",
        action="store_true",
        help="部署应用程序"
    )
    
    # 添加设置参数
    parser.add_argument(
        "--settings",
        action="store_true",
        help="启动设置界面"
    )
    
    args = parser.parse_args()
    
    # 显示欢迎信息
    show_welcome()
    
    # 检查依赖
    if args.check_deps:
        if check_dependencies():
            print("✅ 所有依赖包都已安装")
        return
    
    # 处理打包命令
    if args.package:
        if not check_dependencies():
            print("⚠️  依赖检查失败，但仍继续尝试打包...")
        result = create_executable()
        return 0 if result else 1
    
    # 处理高级打包命令
    if args.package_advanced:
        if not check_dependencies():
            print("⚠️  依赖检查失败，但仍继续尝试高级打包...")
        result = create_executable_advanced()
        return 0 if result else 1
    
    # 处理调试模式
    if args.debug:
        print("🐛 启用调试模式")
        import logging
        logging.basicConfig(level=logging.DEBUG)
        print("💡 调试模式已启用，将显示详细日志信息")
    
    # 处理调试器
    if args.debugger:
        result = launch_debugger()
        return result
    
    # 处理部署
    if args.deploy:
        result = deploy_application()
        return 0 if result else 1
    
    # 处理设置
    if args.settings:
        result = launch_settings()
        return result
    
    # 参数冲突检查
    if sum([args.modern, args.enhanced, args.demo]) > 1:
        print("❌ 错误: 不能同时指定多个界面模式")
        return 1
    
    # 依赖检查增强
    if not check_dependencies() and not any([args.package, args.package_advanced]):
        print("⚠️  警告: 依赖检查失败，但将继续执行非界面相关操作...")
    
    # 创建默认配置
    create_default_config()
    
    # 根据参数启动对应界面
    success = False
    
    if args.demo:
        success = launch_deployment_demo()
    elif args.enhanced:
        success = launch_enhanced_interface()
    elif args.modern:
        success = launch_modern_interface()
    else:
        # 默认启动现代化界面
        print("💡 默认启动现代化界面，使用 --help 查看更多选项")
        success = launch_modern_interface()
        
        # 如果现代化界面启动失败，尝试增强版界面
        if not success:
            print("🔄 尝试启动增强版界面...")
            success = launch_enhanced_interface()
    
    if not success:
        print("\n❌ 所有界面都无法启动")
        print("💡 请检查:")
        print("   1. 是否安装了所有依赖: pip install -r requirements_enhanced.txt")
        print("   2. 是否配置了API密钥: 编辑 api_configs.json")
        print("   3. 查看错误信息并修复相关问题")
        show_usage()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())