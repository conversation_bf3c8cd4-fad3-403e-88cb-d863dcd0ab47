# 用户使用指南

欢迎使用AI代码生成器！本指南将帮助您快速上手并充分利用所有功能。

## 📋 目录

- [快速开始](#快速开始)
- [界面介绍](#界面介绍)
- [功能详解](#功能详解)
- [配置管理](#配置管理)
- [常见问题](#常见问题)
- [技巧和窍门](#技巧和窍门)

## 🚀 快速开始

### 第一次启动

1. **安装依赖**
   ```bash
   pip install -r requirements_enhanced.txt
   ```

2. **启动应用**
   ```bash
   python main.py --modern
   ```

3. **配置API密钥**
   - 首次启动会自动创建配置文件
   - 点击设置按钮配置您的API密钥
   - 至少配置一个AI提供商才能使用代码生成功能

### 基本工作流程

1. **选择功能** → 2. **输入需求** → 3. **生成代码** → 4. **查看结果** → 5. **保存或优化**

## 🎨 界面介绍

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏                                                   │
├─────────────────────────────────────────────────────────┤
│ 工具栏                                                   │
├──────────┬──────────────────────────────────────────────┤
│          │                                              │
│ 侧边栏    │ 主内容区域                                    │
│          │                                              │
│ • 主页    │ [根据选择的功能显示不同内容]                    │
│ • AI助手  │                                              │
│ • 代码生成 │                                              │
│ • 调试器  │                                              │
│ • 打包器  │                                              │
│ • 部署    │                                              │
│ • 设置    │                                              │
│          │                                              │
├──────────┴──────────────────────────────────────────────┤
│ 状态栏                                                   │
└─────────────────────────────────────────────────────────┘
```

### 响应式设计

- **大屏幕** (>1920px): 显示所有功能，侧边栏展开
- **中等屏幕** (1366-1920px): 标准布局
- **小屏幕** (<1366px): 侧边栏可折叠，工具栏简化

## 🔧 功能详解

### 1. 主页 (Home)

**功能概览**
- 显示应用统计信息
- 快速访问常用功能
- 最近使用的项目和文件
- 系统状态监控

**使用技巧**
- 点击功能卡片快速跳转
- 查看性能指标了解系统状态
- 使用快捷方式提高效率

### 2. AI智能助手

**支持的AI提供商**
- OpenAI (GPT-3.5, GPT-4)
- Anthropic (Claude)
- Google (Gemini)
- Azure OpenAI
- 国内提供商 (智谱AI, 百度文心等)

**使用步骤**
1. 选择AI提供商和模型
2. 输入对话内容或代码需求
3. 设置生成参数 (温度、最大token等)
4. 点击发送获取AI回复

**最佳实践**
- 提供清晰、具体的需求描述
- 包含上下文信息提高生成质量
- 使用示例输入输出格式
- 分步骤描述复杂需求

### 3. 代码生成

**支持的编程语言**
- Python, JavaScript, TypeScript
- Java, C#, C++, C
- Go, Rust, PHP, Ruby
- Swift, Kotlin, Scala
- R, MATLAB, SQL
- HTML, CSS

**生成选项**
- **复杂度**: 简单/中等/复杂/专家级
- **风格**: 简洁/函数式/面向对象/过程式
- **包含内容**: 测试代码/文档/注释

**使用示例**
```
需求: 创建一个用户管理系统的API
语言: Python
复杂度: 中等
风格: 面向对象
要求: 
- 使用Flask框架
- 包含用户注册、登录、信息更新功能
- 支持JWT认证
- 包含数据验证
- 生成对应的测试代码
```

### 4. 智能调试器

**功能特点**
- 自动错误分析
- 智能修复建议
- 代码质量评估
- 性能优化建议

**使用方法**
1. 粘贴有问题的代码
2. 输入错误信息 (可选)
3. 选择编程语言
4. 点击"分析调试"
5. 查看分析结果和修复建议

**错误类型支持**
- 语法错误
- 逻辑错误
- 运行时错误
- 性能问题
- 安全漏洞

### 5. 项目打包

**支持的平台**
- Windows (x64, x86)
- Linux (x64, ARM64)
- macOS (x64, ARM64)
- Web平台
- 移动平台 (Android, iOS)

**打包格式**
- 可执行文件 (.exe, 二进制文件)
- 安装包 (.msi, .deb, .rpm)
- 便携版 (.zip, .tar.gz)
- 容器镜像 (Docker)
- 应用商店包

**配置选项**
```json
{
  "name": "MyApp",
  "version": "1.0.0",
  "platforms": ["windows-x64", "linux-x64"],
  "formats": ["executable", "portable"],
  "include_files": ["config/", "assets/"],
  "exclude_files": ["tests/", "*.pyc"]
}
```

### 6. 部署管理

**支持的部署方式**
- 本地部署
- 云服务器部署
- 容器化部署 (Docker, Kubernetes)
- 应用商店发布

**云服务商支持**
- 阿里云
- AWS
- Azure
- Google Cloud
- 腾讯云

## ⚙️ 配置管理

### 应用设置

**界面配置**
- 主题选择 (深色/浅色/自定义)
- 语言设置
- 字体和大小
- 窗口布局

**功能配置**
- 自动保存间隔
- 历史记录数量
- 性能监控开关
- 错误追踪设置

**API配置**
- 提供商选择
- 模型参数
- 超时设置
- 缓存配置

### 配置文件位置

```
config/
├── app_config.json      # 应用程序配置
├── api_configs.json     # API配置
└── user_config.json     # 用户自定义配置
```

### 配置备份和恢复

**备份配置**
```bash
python main.py --settings
# 选择 "备份配置" 选项
```

**恢复配置**
- 在设置界面选择 "恢复配置"
- 选择备份文件
- 确认恢复操作

## ❓ 常见问题

### Q1: 启动时提示缺少依赖怎么办？

**A:** 重新安装依赖包
```bash
pip install -r requirements_enhanced.txt --force-reinstall
```

### Q2: API调用失败怎么办？

**A:** 检查以下几点：
1. 网络连接是否正常
2. API密钥是否正确
3. API配额是否充足
4. 代理设置是否正确

### Q3: 生成的代码质量不理想？

**A:** 优化提示词：
- 提供更详细的需求描述
- 包含具体的输入输出示例
- 指定代码风格和最佳实践
- 分解复杂需求为多个简单任务

### Q4: 打包失败怎么办？

**A:** 常见解决方案：
1. 确保安装了PyInstaller: `pip install pyinstaller`
2. 检查项目路径是否包含中文或特殊字符
3. 查看详细错误日志
4. 尝试不同的打包工具

### Q5: 界面显示异常？

**A:** 尝试以下方法：
1. 更新显卡驱动
2. 切换不同主题
3. 调整系统DPI设置
4. 重置界面配置

### Q6: 性能问题？

**A:** 优化建议：
1. 启用性能监控查看瓶颈
2. 调整API并发数量
3. 清理历史记录和缓存
4. 关闭不必要的功能

## 💡 技巧和窍门

### 提高代码生成质量

1. **使用模板化提示**
   ```
   任务: [具体任务描述]
   输入: [输入格式和示例]
   输出: [期望的输出格式]
   约束: [技术约束和要求]
   示例: [具体的输入输出示例]
   ```

2. **分步骤生成**
   - 先生成基本框架
   - 再逐步添加功能
   - 最后优化和完善

3. **利用上下文**
   - 提供相关的代码片段
   - 说明项目背景和架构
   - 指定编码规范和风格

### 高效使用快捷键

- `Ctrl+N`: 新建项目
- `Ctrl+O`: 打开项目
- `Ctrl+S`: 保存当前内容
- `F5`: 运行/生成代码
- `F9`: 调试代码
- `F11`: 全屏模式
- `Ctrl+,`: 打开设置

### 自定义工作流程

1. **创建项目模板**
   - 保存常用的项目结构
   - 预设API配置
   - 定义代码规范

2. **批量处理**
   - 使用脚本模式批量生成
   - 设置自动化流程
   - 定时任务执行

3. **团队协作**
   - 导出配置文件共享
   - 统一代码风格设置
   - 共享项目模板

### 性能优化

1. **启用缓存**
   - API响应缓存
   - 代码生成结果缓存
   - 模板缓存

2. **并发控制**
   - 合理设置并发数
   - 使用队列管理请求
   - 避免频繁API调用

3. **资源管理**
   - 定期清理临时文件
   - 监控内存使用
   - 优化大文件处理

---

希望这份指南能帮助您更好地使用AI代码生成器！如有其他问题，请查看API文档或联系技术支持。
