# 增强版UI界面依赖

# 基础GUI框架
PyQt5==5.15.9

# HTTP请求库
requests==2.31.0
aiohttp==3.8.6

# 异步支持
asyncio-mqtt==0.13.0

# JSON处理
jsonschema==4.19.2

# JWT支持
PyJWT==2.8.0

# 数据类支持
dataclasses-json==0.6.1

# 类型提示
typing-extensions==4.8.0

# 文件处理
pathlib2==2.3.7

# 时间处理
python-dateutil==2.8.2

# 加密支持
cryptography==41.0.7

# 配置文件处理
pyyaml==6.0.1
toml==0.10.2

# 日志处理
loguru==0.7.2

# 进度条
tqdm==4.66.1

# 代码格式化
black==23.9.1
autopep8==2.0.4

# 代码检查
pylint==3.0.2
flake8==6.1.0

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1

# 文档生成
sphinx==7.2.6
mkdocs==1.5.3

# 开发工具
ipython==8.17.2
jupyter==1.0.0

# API客户端库
openai==1.3.5
anthropic==0.7.7
google-generativeai==0.3.1

# 数据库支持
sqlite3

# 网络工具
websockets==12.0

# 图像处理
Pillow==10.1.0

# 系统信息
psutil==5.9.6

# 环境变量
python-dotenv==1.0.0