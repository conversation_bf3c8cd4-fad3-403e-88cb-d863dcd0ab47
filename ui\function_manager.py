#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能管理器
连接界面和后端功能逻辑
"""

import asyncio
import json
import os
import sys
import threading
import time
from typing import Dict, Any, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt5.QtWidgets import QMessageBox, QProgressBar, QTextEdit

class WorkerThread(QThread):
    """工作线程"""
    
    finished = pyqtSignal(object)  # 完成信号
    error = pyqtSignal(str)        # 错误信号
    progress = pyqtSignal(int)     # 进度信号
    message = pyqtSignal(str)      # 消息信号
    
    def __init__(self, func, *args, **kwargs):
        super().__init__()
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.result = None
    
    def run(self):
        """运行工作函数"""
        try:
            if asyncio.iscoroutinefunction(self.func):
                # 异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                self.result = loop.run_until_complete(self.func(*self.args, **self.kwargs))
                loop.close()
            else:
                # 同步函数
                self.result = self.func(*self.args, **self.kwargs)
            
            self.finished.emit(self.result)
        except Exception as e:
            self.error.emit(str(e))

class FunctionManager(QObject):
    """功能管理器"""
    
    # 信号定义
    ai_response = pyqtSignal(str)           # AI回复信号
    code_generated = pyqtSignal(str)        # 代码生成信号
    debug_result = pyqtSignal(str)          # 调试结果信号
    package_progress = pyqtSignal(int, str) # 打包进度信号
    deploy_status = pyqtSignal(str)         # 部署状态信号
    settings_saved = pyqtSignal(bool)       # 设置保存信号
    
    def __init__(self):
        super().__init__()
        self.ai_engine = None
        self.code_generator = None
        self.debugger = None
        self.packager = None
        self.deployer = None
        self.config_manager = None
        
        # 初始化组件
        self.init_components()
    
    def init_components(self):
        """初始化功能组件"""
        try:
            # 导入AI引擎
            from ai.enhanced_ai_engine import EnhancedAIEngine
            self.ai_engine = EnhancedAIEngine()
        except Exception as e:
            print(f"AI引擎初始化失败: {e}")
        
        try:
            # 导入代码生成器
            from ai.enhanced_code_generator import EnhancedCodeGenerator, EnhancedDebugger
            self.code_generator = EnhancedCodeGenerator()
            self.debugger = EnhancedDebugger(self.code_generator)
        except Exception as e:
            print(f"代码生成器初始化失败: {e}")
        
        try:
            # 导入打包器
            from tools.enhanced_packager import EnhancedPackager
            self.packager = EnhancedPackager()
        except Exception as e:
            print(f"打包器初始化失败: {e}")
        
        try:
            # 导入配置管理器
            from tools.config_manager import config_manager
            self.config_manager = config_manager
        except Exception as e:
            print(f"配置管理器初始化失败: {e}")
    
    def send_ai_message(self, message: str, provider: str = "auto") -> None:
        """发送AI消息"""
        if not self.ai_engine:
            self.ai_response.emit("❌ AI引擎未初始化")
            return
        
        def ai_chat():
            try:
                response = self.ai_engine.chat(message, provider)
                return response
            except Exception as e:
                raise Exception(f"AI对话失败: {str(e)}")
        
        # 在工作线程中执行
        worker = WorkerThread(ai_chat)
        worker.finished.connect(lambda result: self.ai_response.emit(result))
        worker.error.connect(lambda error: self.ai_response.emit(f"❌ {error}"))
        worker.start()
    
    def generate_code(self, prompt: str, language: str, complexity: str, 
                     include_tests: bool = True, include_docs: bool = True) -> None:
        """生成代码"""
        if not self.code_generator:
            self.code_generated.emit("❌ 代码生成器未初始化")
            return
        
        async def generate():
            try:
                from ai.enhanced_code_generator import CodeGenerationRequest, CodeLanguage, CodeComplexity
                
                # 转换语言
                lang_map = {
                    "Python": CodeLanguage.PYTHON,
                    "JavaScript": CodeLanguage.JAVASCRIPT,
                    "Java": CodeLanguage.JAVA,
                    "C++": CodeLanguage.CPP,
                    "Go": CodeLanguage.GO,
                    "Rust": CodeLanguage.RUST
                }
                
                # 转换复杂度
                complexity_map = {
                    "简单": CodeComplexity.SIMPLE,
                    "中等": CodeComplexity.MEDIUM,
                    "复杂": CodeComplexity.COMPLEX,
                    "专家级": CodeComplexity.EXPERT
                }
                
                request = CodeGenerationRequest(
                    prompt=prompt,
                    language=lang_map.get(language, CodeLanguage.PYTHON),
                    complexity=complexity_map.get(complexity, CodeComplexity.MEDIUM),
                    include_tests=include_tests,
                    include_docs=include_docs
                )
                
                response = await self.code_generator.generate_code(request)
                
                # 格式化输出
                result = f"# 生成的代码\n\n```{language.lower()}\n{response.code}\n```\n\n"
                
                if response.tests and include_tests:
                    result += f"# 测试代码\n\n```{language.lower()}\n{response.tests}\n```\n\n"
                
                if response.documentation and include_docs:
                    result += f"# 文档\n\n{response.documentation}\n\n"
                
                if response.suggestions:
                    result += "# 建议\n\n"
                    for suggestion in response.suggestions:
                        result += f"- {suggestion}\n"
                
                return result
                
            except Exception as e:
                raise Exception(f"代码生成失败: {str(e)}")
        
        # 在工作线程中执行
        worker = WorkerThread(generate)
        worker.finished.connect(lambda result: self.code_generated.emit(result))
        worker.error.connect(lambda error: self.code_generated.emit(f"❌ {error}"))
        worker.start()
    
    def debug_code(self, code: str, error_message: str = "", language: str = "Python") -> None:
        """调试代码"""
        if not self.debugger:
            self.debug_result.emit("❌ 调试器未初始化")
            return
        
        async def debug():
            try:
                from ai.enhanced_code_generator import CodeLanguage
                
                lang_map = {
                    "Python": CodeLanguage.PYTHON,
                    "JavaScript": CodeLanguage.JAVASCRIPT,
                    "Java": CodeLanguage.JAVA,
                    "C++": CodeLanguage.CPP,
                    "Go": CodeLanguage.GO,
                    "Rust": CodeLanguage.RUST
                }
                
                result = await self.debugger.debug_code(
                    code=code,
                    error_message=error_message or "未指定错误信息",
                    language=lang_map.get(language, CodeLanguage.PYTHON)
                )
                
                # 格式化调试结果
                output = f"# 调试分析结果\n\n"
                output += f"**错误类型**: {result['error_analysis']['error_type']}\n"
                output += f"**严重程度**: {result['error_analysis']['severity']}\n\n"
                
                if result['error_analysis']['likely_causes']:
                    output += "## 可能原因\n"
                    for cause in result['error_analysis']['likely_causes']:
                        output += f"- {cause}\n"
                    output += "\n"
                
                if result['fix_suggestions']:
                    output += "## 修复建议\n"
                    for suggestion in result['fix_suggestions']:
                        output += f"- {suggestion}\n"
                    output += "\n"
                
                if result['fixed_code']:
                    output += f"## 修复后的代码\n\n```{language.lower()}\n{result['fixed_code']}\n```\n\n"
                
                output += f"**修复信心度**: {result['confidence_score']:.1%}\n"
                
                return output
                
            except Exception as e:
                raise Exception(f"代码调试失败: {str(e)}")
        
        # 在工作线程中执行
        worker = WorkerThread(debug)
        worker.finished.connect(lambda result: self.debug_result.emit(result))
        worker.error.connect(lambda error: self.debug_result.emit(f"❌ {error}"))
        worker.start()
    
    def package_project(self, project_name: str, version: str, main_file: str, 
                       platforms: list, project_path: str = ".") -> None:
        """打包项目"""
        if not self.packager:
            self.package_progress.emit(0, "❌ 打包器未初始化")
            return
        
        async def package():
            try:
                from tools.enhanced_packager import PackageConfig, Platform, PackageFormat
                
                # 转换平台
                platform_map = {
                    "Windows x64": Platform.WINDOWS_X64,
                    "Linux x64": Platform.LINUX_X64,
                    "macOS x64": Platform.MACOS_X64,
                    "Web应用": Platform.WEB
                }
                
                selected_platforms = [platform_map[p] for p in platforms if p in platform_map]
                
                config = PackageConfig(
                    name=project_name,
                    version=version,
                    description=f"{project_name} - AI生成的应用程序",
                    author="AI Code Generator",
                    main_file=main_file,
                    platforms=selected_platforms,
                    formats=[PackageFormat.EXECUTABLE, PackageFormat.PORTABLE]
                )
                
                self.packager.config = config
                
                def progress_callback(progress, message):
                    self.package_progress.emit(progress, message)
                
                result = await self.packager.package_project(project_path, progress_callback)
                
                if result['success']:
                    message = "✅ 打包完成！\n\n"
                    for platform_format, details in result['results'].items():
                        if details.get('success'):
                            message += f"✅ {platform_format}: {details.get('output_path', '成功')}\n"
                        else:
                            message += f"❌ {platform_format}: {details.get('error', '失败')}\n"
                else:
                    message = f"❌ 打包失败: {result.get('error', '未知错误')}"
                
                return message
                
            except Exception as e:
                raise Exception(f"项目打包失败: {str(e)}")
        
        # 在工作线程中执行
        worker = WorkerThread(package)
        worker.finished.connect(lambda result: self.package_progress.emit(100, result))
        worker.error.connect(lambda error: self.package_progress.emit(0, f"❌ {error}"))
        worker.start()
    
    def deploy_project(self, deploy_type: str, server_config: dict) -> None:
        """部署项目"""
        def deploy():
            try:
                if deploy_type == "本地服务器":
                    return self._deploy_local(server_config)
                elif deploy_type == "云服务器":
                    return self._deploy_cloud(server_config)
                elif deploy_type == "Docker容器":
                    return self._deploy_docker(server_config)
                else:
                    return f"✅ {deploy_type} 部署模拟完成"
            except Exception as e:
                raise Exception(f"部署失败: {str(e)}")
        
        # 在工作线程中执行
        worker = WorkerThread(deploy)
        worker.finished.connect(lambda result: self.deploy_status.emit(result))
        worker.error.connect(lambda error: self.deploy_status.emit(f"❌ {error}"))
        worker.start()
    
    def _deploy_local(self, config: dict) -> str:
        """本地部署"""
        return "✅ 本地服务器部署完成\n服务已启动在 http://localhost:8000"
    
    def _deploy_cloud(self, config: dict) -> str:
        """云端部署"""
        server_ip = config.get('server_ip', '未知')
        return f"✅ 云服务器部署完成\n服务已部署到 {server_ip}"
    
    def _deploy_docker(self, config: dict) -> str:
        """Docker部署"""
        return "✅ Docker容器部署完成\n容器已启动并运行"
    
    def save_settings(self, settings: dict) -> None:
        """保存设置"""
        try:
            if not self.config_manager:
                self.settings_saved.emit(False)
                return
            
            # 保存API配置
            if 'api' in settings:
                api_config = settings['api']
                provider = api_config.get('provider', '').lower()
                if provider and api_config.get('api_key'):
                    config = {
                        'api_key': api_config['api_key'],
                        'model': api_config.get('model', ''),
                        'enabled': True
                    }
                    self.config_manager.set_api_config(provider, config)
            
            # 保存界面设置
            if 'ui' in settings:
                ui_config = settings['ui']
                for key, value in ui_config.items():
                    self.config_manager.set_config(f'ui.{key}', value)
            
            # 保存性能设置
            if 'performance' in settings:
                perf_config = settings['performance']
                for key, value in perf_config.items():
                    self.config_manager.set_config(f'performance.{key}', value)
            
            self.settings_saved.emit(True)
            
        except Exception as e:
            print(f"保存设置失败: {e}")
            self.settings_saved.emit(False)
    
    def load_settings(self) -> dict:
        """加载设置"""
        try:
            if not self.config_manager:
                return {}
            
            settings = {
                'api': {},
                'ui': {},
                'performance': {}
            }
            
            # 加载API配置
            for provider in ['openai', 'anthropic', 'google', 'zhipu']:
                config = self.config_manager.get_api_config(provider)
                if config and config.get('enabled'):
                    settings['api'] = {
                        'provider': provider,
                        'api_key': config.get('api_key', ''),
                        'model': config.get('model', '')
                    }
                    break
            
            # 加载界面设置
            settings['ui'] = {
                'theme': self.config_manager.get_config('ui.theme', '浅色主题'),
                'language': self.config_manager.get_config('ui.language', '中文'),
                'auto_save': self.config_manager.get_config('ui.auto_save', True)
            }
            
            # 加载性能设置
            settings['performance'] = {
                'monitor': self.config_manager.get_config('performance.monitor', True),
                'cache': self.config_manager.get_config('performance.cache', True)
            }
            
            return settings
            
        except Exception as e:
            print(f"加载设置失败: {e}")
            return {}

# 全局功能管理器实例
function_manager = FunctionManager()
