#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面 - 用于诊断界面问题
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI代码生成器 - 测试界面")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置界面
        self.setup_ui()
        
        # 应用样式
        self.apply_style()
    
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.create_sidebar(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_sidebar(self, main_layout):
        """创建侧边栏"""
        sidebar = QWidget()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QWidget {
                background-color: #2D3748;
                color: white;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(10, 10, 10, 10)
        sidebar_layout.setSpacing(5)
        
        # 标题
        title = QLabel("功能菜单")
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        sidebar_layout.addWidget(title)
        
        # 菜单项
        menu_items = [
            ("🏠", "主页"),
            ("🤖", "AI助手"),
            ("💻", "代码生成"),
            ("🐛", "调试器"),
            ("📦", "打包器"),
            ("🚀", "部署"),
            ("⚙️", "设置")
        ]
        
        self.menu_buttons = []
        for icon, text in menu_items:
            btn = QPushButton(f"{icon} {text}")
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px;
                    border: none;
                    border-radius: 5px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #4A5568;
                }
                QPushButton:pressed {
                    background-color: #3182CE;
                }
            """)
            btn.clicked.connect(lambda checked, t=text: self.on_menu_clicked(t))
            sidebar_layout.addWidget(btn)
            self.menu_buttons.append(btn)
        
        sidebar_layout.addStretch()
        main_layout.addWidget(sidebar)
    
    def create_main_content(self, main_layout):
        """创建主内容区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-left: 1px solid #e0e0e0;
            }
        """)
        
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # 欢迎标题
        welcome_label = QLabel("🚀 欢迎使用 AI代码生成器")
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2D3748;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        content_layout.addWidget(welcome_label)
        
        # 功能卡片容器
        cards_widget = QWidget()
        cards_layout = QGridLayout(cards_widget)
        cards_layout.setSpacing(20)
        
        # 功能卡片
        features = [
            ("🤖 AI智能助手", "与AI对话，获取编程帮助", "#3182CE"),
            ("💻 代码生成", "智能生成高质量代码", "#38A169"),
            ("🐛 智能调试", "AI辅助代码调试", "#E53E3E"),
            ("📦 项目打包", "一键打包多平台应用", "#D69E2E"),
            ("🚀 快速部署", "自动化部署到云端", "#9F7AEA"),
            ("📚 文档生成", "自动生成项目文档", "#00B5D8")
        ]
        
        for i, (title, desc, color) in enumerate(features):
            card = self.create_feature_card(title, desc, color)
            row = i // 3
            col = i % 3
            cards_layout.addWidget(card, row, col)
        
        content_layout.addWidget(cards_widget)
        
        # 状态信息
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        
        # API状态
        api_status = QLabel("🟢 API状态: 正常")
        api_status.setStyleSheet("color: #38A169; font-weight: bold;")
        status_layout.addWidget(api_status)
        
        status_layout.addStretch()
        
        # 版本信息
        version_label = QLabel("版本: v2.0.0")
        version_label.setStyleSheet("color: #718096;")
        status_layout.addWidget(version_label)
        
        content_layout.addWidget(status_widget)
        content_layout.addStretch()
        
        main_layout.addWidget(content_widget)
    
    def create_feature_card(self, title, description, color):
        """创建功能卡片"""
        card = QWidget()
        card.setFixedSize(200, 120)
        card.setStyleSheet(f"""
            QWidget {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 10px;
            }}
            QWidget:hover {{
                background-color: #f8f9fa;
                transform: translateY(-2px);
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {color};
            }}
        """)
        layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #718096;
                line-height: 1.4;
            }
        """)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        layout.addStretch()
        
        # 添加点击事件
        card.mousePressEvent = lambda event: self.on_card_clicked(title)
        
        return card
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        file_menu.addAction('新建项目', self.new_project)
        file_menu.addAction('打开项目', self.open_project)
        file_menu.addSeparator()
        file_menu.addAction('退出', self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')
        edit_menu.addAction('复制', lambda: print("复制"))
        edit_menu.addAction('粘贴', lambda: print("粘贴"))
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        tools_menu.addAction('设置', self.open_settings)
        tools_menu.addAction('性能监控', self.show_performance)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        help_menu.addAction('关于', self.show_about)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # 新建
        new_action = QAction('新建', self)
        new_action.setIcon(self.style().standardIcon(QStyle.SP_FileIcon))
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)
        
        # 打开
        open_action = QAction('打开', self)
        open_action.setIcon(self.style().standardIcon(QStyle.SP_DirOpenIcon))
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # 生成代码
        generate_action = QAction('生成代码', self)
        generate_action.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        generate_action.triggered.connect(lambda: self.on_menu_clicked("代码生成"))
        toolbar.addAction(generate_action)
        
        # 调试
        debug_action = QAction('调试', self)
        debug_action.setIcon(self.style().standardIcon(QStyle.SP_MessageBoxCritical))
        debug_action.triggered.connect(lambda: self.on_menu_clicked("调试器"))
        toolbar.addAction(debug_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()
        status_bar.showMessage("就绪")
        
        # 添加永久组件
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.progress_bar)
        
        # API状态指示器
        api_label = QLabel("API: 已连接")
        api_label.setStyleSheet("color: green;")
        status_bar.addPermanentWidget(api_label)
    
    def apply_style(self):
        """应用全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QMenuBar {
                background-color: #ffffff;
                border-bottom: 1px solid #e9ecef;
                padding: 5px;
            }
            QMenuBar::item {
                padding: 5px 10px;
                border-radius: 3px;
            }
            QMenuBar::item:selected {
                background-color: #e9ecef;
            }
            QToolBar {
                background-color: #ffffff;
                border-bottom: 1px solid #e9ecef;
                spacing: 5px;
                padding: 5px;
            }
            QStatusBar {
                background-color: #ffffff;
                border-top: 1px solid #e9ecef;
            }
        """)
    
    def on_menu_clicked(self, menu_name):
        """菜单点击事件"""
        self.statusBar().showMessage(f"切换到: {menu_name}")
        print(f"点击了菜单: {menu_name}")
    
    def on_card_clicked(self, card_title):
        """功能卡片点击事件"""
        self.statusBar().showMessage(f"启动功能: {card_title}")
        print(f"点击了功能卡片: {card_title}")
    
    def new_project(self):
        """新建项目"""
        QMessageBox.information(self, "新建项目", "新建项目功能")
    
    def open_project(self):
        """打开项目"""
        QMessageBox.information(self, "打开项目", "打开项目功能")
    
    def open_settings(self):
        """打开设置"""
        QMessageBox.information(self, "设置", "设置功能")
    
    def show_performance(self):
        """显示性能监控"""
        QMessageBox.information(self, "性能监控", "性能监控功能")
    
    def show_about(self):
        """显示关于"""
        QMessageBox.about(self, "关于", 
                         "AI代码生成器 v2.0\n\n"
                         "一个功能强大的AI驱动代码生成工具\n"
                         "支持多种编程语言和AI模型")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("AI Code Generator")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("AI Code Team")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    # 启动应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
