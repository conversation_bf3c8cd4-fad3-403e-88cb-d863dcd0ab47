# 高级调试器
import sys
import os
import ast
import dis
import inspect
import traceback
import threading
import time
import json
import subprocess
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import linecache
import pdb
import bdb
from contextlib import contextmanager

class BreakpointType(Enum):
    LINE = "line"
    FUNCTION = "function"
    EXCEPTION = "exception"
    CONDITIONAL = "conditional"
    WATCHPOINT = "watchpoint"

class DebugState(Enum):
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    STEPPING = "stepping"
    FINISHED = "finished"
    ERROR = "error"

@dataclass
class Breakpoint:
    """断点信息"""
    id: int
    type: BreakpointType
    file_path: str
    line_number: int
    condition: str = ""
    hit_count: int = 0
    enabled: bool = True
    temporary: bool = False

@dataclass
class StackFrame:
    """栈帧信息"""
    frame_id: int
    function_name: str
    file_path: str
    line_number: int
    locals: Dict[str, Any]
    globals: Dict[str, Any]
    source_line: str

@dataclass
class Variable:
    """变量信息"""
    name: str
    value: Any
    type_name: str
    size: int
    is_mutable: bool
    memory_address: str

class AdvancedDebugger(bdb.Bdb):
    """高级调试器"""
    
    def __init__(self):
        super().__init__()
        self.breakpoints: Dict[int, Breakpoint] = {}
        self.watchpoints: Dict[str, Any] = {}
        self.call_stack: List[StackFrame] = []
        self.current_frame = None
        self.state = DebugState.STOPPED
        self.step_mode = False
        self.step_into = False
        self.step_over = False
        self.step_out = False
        self.breakpoint_counter = 0
        self.execution_history = []
        self.performance_data = {}
        self.memory_tracker = {}
        self.exception_handler = None
        self.output_capture = []
        self.input_simulation = []
        
        # AI辅助调试
        self.ai_suggestions = []
        self.code_analysis = {}
        
        # 回调函数
        self.on_breakpoint_hit = None
        self.on_step = None
        self.on_exception = None
        self.on_state_change = None
    
    def set_breakpoint(self, file_path: str, line_number: int, 
                      condition: str = "", temporary: bool = False) -> int:
        """设置断点"""
        self.breakpoint_counter += 1
        breakpoint_id = self.breakpoint_counter
        
        breakpoint = Breakpoint(
            id=breakpoint_id,
            type=BreakpointType.LINE,
            file_path=os.path.abspath(file_path),
            line_number=line_number,
            condition=condition,
            temporary=temporary
        )
        
        self.breakpoints[breakpoint_id] = breakpoint
        
        # 设置bdb断点
        self.set_break(file_path, line_number, temporary, condition)
        
        return breakpoint_id
    
    def remove_breakpoint(self, breakpoint_id: int) -> bool:
        """移除断点"""
        if breakpoint_id in self.breakpoints:
            breakpoint = self.breakpoints[breakpoint_id]
            self.clear_break(breakpoint.file_path, breakpoint.line_number)
            del self.breakpoints[breakpoint_id]
            return True
        return False
    
    def set_watchpoint(self, variable_name: str, condition: str = ""):
        """设置监视点"""
        self.watchpoints[variable_name] = {
            'condition': condition,
            'last_value': None,
            'hit_count': 0
        }
    
    def remove_watchpoint(self, variable_name: str):
        """移除监视点"""
        if variable_name in self.watchpoints:
            del self.watchpoints[variable_name]
    
    def start_debugging(self, code: str, filename: str = "<debug>"):
        """开始调试"""
        self.state = DebugState.RUNNING
        self._notify_state_change()
        
        try:
            # 编译代码
            compiled_code = compile(code, filename, 'exec')
            
            # 创建执行环境
            debug_globals = {
                '__name__': '__main__',
                '__file__': filename,
                '__builtins__': __builtins__
            }
            
            # 开始调试执行
            self.run(compiled_code, debug_globals)
            
        except Exception as e:
            self.state = DebugState.ERROR
            self._handle_exception(e)
        finally:
            self.state = DebugState.FINISHED
            self._notify_state_change()
    
    def step_into(self):
        """步入"""
        self.step_into = True
        self.step_mode = True
        self.set_step()
        self.state = DebugState.STEPPING
        self._notify_state_change()
    
    def step_over(self):
        """步过"""
        self.step_over = True
        self.step_mode = True
        self.set_next(self.current_frame)
        self.state = DebugState.STEPPING
        self._notify_state_change()
    
    def step_out(self):
        """步出"""
        self.step_out = True
        self.step_mode = True
        self.set_return(self.current_frame)
        self.state = DebugState.STEPPING
        self._notify_state_change()
    
    def continue_execution(self):
        """继续执行"""
        self.step_mode = False
        self.set_continue()
        self.state = DebugState.RUNNING
        self._notify_state_change()
    
    def pause_execution(self):
        """暂停执行"""
        self.set_quit()
        self.state = DebugState.PAUSED
        self._notify_state_change()
    
    def stop_debugging(self):
        """停止调试"""
        self.set_quit()
        self.state = DebugState.STOPPED
        self._notify_state_change()
    
    def user_line(self, frame):
        """用户代码行事件"""
        self.current_frame = frame
        
        # 更新调用栈
        self._update_call_stack(frame)
        
        # 检查监视点
        self._check_watchpoints(frame)
        
        # 记录执行历史
        self._record_execution(frame)
        
        # 性能分析
        self._collect_performance_data(frame)
        
        # 如果是步进模式，暂停执行
        if self.step_mode:
            self.state = DebugState.PAUSED
            self._notify_step(frame)
            self.interaction(frame, None)
    
    def user_call(self, frame, args):
        """函数调用事件"""
        if self.step_into:
            self.step_into = False
            self.user_line(frame)
    
    def user_return(self, frame, retval):
        """函数返回事件"""
        if self.step_out:
            self.step_out = False
            self.user_line(frame)
    
    def user_exception(self, frame, exc_info):
        """异常事件"""
        self.state = DebugState.PAUSED
        self._handle_exception(exc_info[1], frame)
        self.interaction(frame, exc_info)
    
    def do_break(self, frame, arg):
        """处理断点命中"""
        file_path = frame.f_code.co_filename
        line_number = frame.f_lineno
        
        # 查找匹配的断点
        for bp in self.breakpoints.values():
            if (bp.file_path == file_path and 
                bp.line_number == line_number and 
                bp.enabled):
                
                # 检查条件
                if bp.condition:
                    try:
                        if not eval(bp.condition, frame.f_globals, frame.f_locals):
                            continue
                    except:
                        continue
                
                # 增加命中次数
                bp.hit_count += 1
                
                # 如果是临时断点，删除它
                if bp.temporary:
                    self.remove_breakpoint(bp.id)
                
                # 暂停执行
                self.state = DebugState.PAUSED
                self._notify_breakpoint_hit(bp, frame)
                return True
        
        return False
    
    def _update_call_stack(self, frame):
        """更新调用栈"""
        self.call_stack = []
        current_frame = frame
        frame_id = 0
        
        while current_frame:
            stack_frame = StackFrame(
                frame_id=frame_id,
                function_name=current_frame.f_code.co_name,
                file_path=current_frame.f_code.co_filename,
                line_number=current_frame.f_lineno,
                locals=dict(current_frame.f_locals),
                globals=dict(current_frame.f_globals),
                source_line=linecache.getline(
                    current_frame.f_code.co_filename, 
                    current_frame.f_lineno
                ).strip()
            )
            
            self.call_stack.append(stack_frame)
            current_frame = current_frame.f_back
            frame_id += 1
    
    def _check_watchpoints(self, frame):
        """检查监视点"""
        for var_name, watch_info in self.watchpoints.items():
            try:
                # 获取变量值
                if var_name in frame.f_locals:
                    current_value = frame.f_locals[var_name]
                elif var_name in frame.f_globals:
                    current_value = frame.f_globals[var_name]
                else:
                    continue
                
                # 检查值是否改变
                if watch_info['last_value'] != current_value:
                    # 检查条件
                    if watch_info['condition']:
                        try:
                            if not eval(watch_info['condition'], 
                                      {'old': watch_info['last_value'], 
                                       'new': current_value}):
                                continue
                        except:
                            continue
                    
                    # 触发监视点
                    watch_info['hit_count'] += 1
                    watch_info['last_value'] = current_value
                    
                    self.state = DebugState.PAUSED
                    self._notify_watchpoint_hit(var_name, current_value, frame)
                    
            except Exception as e:
                continue
    
    def _record_execution(self, frame):
        """记录执行历史"""
        execution_record = {
            'timestamp': time.time(),
            'file_path': frame.f_code.co_filename,
            'line_number': frame.f_lineno,
            'function_name': frame.f_code.co_name,
            'locals': {k: str(v) for k, v in frame.f_locals.items()},
            'source_line': linecache.getline(
                frame.f_code.co_filename, 
                frame.f_lineno
            ).strip()
        }
        
        self.execution_history.append(execution_record)
        
        # 限制历史记录数量
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]
    
    def _collect_performance_data(self, frame):
        """收集性能数据"""
        function_name = frame.f_code.co_name
        
        if function_name not in self.performance_data:
            self.performance_data[function_name] = {
                'call_count': 0,
                'total_time': 0,
                'start_time': None,
                'memory_usage': []
            }
        
        perf_data = self.performance_data[function_name]
        
        # 记录函数调用
        if perf_data['start_time'] is None:
            perf_data['start_time'] = time.time()
            perf_data['call_count'] += 1
        
        # 记录内存使用
        try:
            import psutil
            process = psutil.Process()
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            perf_data['memory_usage'].append(memory_usage)
        except:
            pass
    
    def _handle_exception(self, exception, frame=None):
        """处理异常"""
        exception_info = {
            'type': type(exception).__name__,
            'message': str(exception),
            'traceback': traceback.format_exc(),
            'frame': frame
        }
        
        if self.on_exception:
            self.on_exception(exception_info)
        
        # AI辅助异常分析
        self._analyze_exception_with_ai(exception_info)
    
    def _analyze_exception_with_ai(self, exception_info):
        """AI辅助异常分析"""
        # 这里可以集成AI来分析异常并提供修复建议
        analysis = {
            'exception_type': exception_info['type'],
            'possible_causes': [],
            'fix_suggestions': [],
            'related_code': []
        }
        
        # 基于异常类型提供建议
        if exception_info['type'] == 'NameError':
            analysis['possible_causes'].append('变量未定义或拼写错误')
            analysis['fix_suggestions'].append('检查变量名拼写，确保变量已定义')
        elif exception_info['type'] == 'IndexError':
            analysis['possible_causes'].append('列表索引超出范围')
            analysis['fix_suggestions'].append('检查列表长度，使用适当的索引范围')
        elif exception_info['type'] == 'KeyError':
            analysis['possible_causes'].append('字典键不存在')
            analysis['fix_suggestions'].append('使用dict.get()方法或检查键是否存在')
        
        self.ai_suggestions.append(analysis)
    
    def get_variables(self, frame_id: int = 0) -> List[Variable]:
        """获取变量信息"""
        if frame_id >= len(self.call_stack):
            return []
        
        frame_info = self.call_stack[frame_id]
        variables = []
        
        # 本地变量
        for name, value in frame_info.locals.items():
            if not name.startswith('__'):
                var = Variable(
                    name=name,
                    value=value,
                    type_name=type(value).__name__,
                    size=sys.getsizeof(value),
                    is_mutable=self._is_mutable(value),
                    memory_address=hex(id(value))
                )
                variables.append(var)
        
        return variables
    
    def _is_mutable(self, obj) -> bool:
        """检查对象是否可变"""
        immutable_types = (int, float, str, tuple, frozenset, bytes)
        return not isinstance(obj, immutable_types)
    
    def evaluate_expression(self, expression: str, frame_id: int = 0) -> Any:
        """在指定栈帧中评估表达式"""
        if frame_id >= len(self.call_stack):
            raise ValueError("无效的栈帧ID")
        
        frame_info = self.call_stack[frame_id]
        
        try:
            # 在栈帧的上下文中评估表达式
            result = eval(expression, frame_info.globals, frame_info.locals)
            return result
        except Exception as e:
            raise ValueError(f"表达式评估失败: {str(e)}")
    
    def modify_variable(self, variable_name: str, new_value: Any, frame_id: int = 0):
        """修改变量值"""
        if frame_id >= len(self.call_stack):
            raise ValueError("无效的栈帧ID")
        
        frame_info = self.call_stack[frame_id]
        
        if variable_name in frame_info.locals:
            frame_info.locals[variable_name] = new_value
        elif variable_name in frame_info.globals:
            frame_info.globals[variable_name] = new_value
        else:
            raise ValueError(f"变量 '{variable_name}' 不存在")
    
    def get_source_code(self, file_path: str, start_line: int = 1, 
                       end_line: int = None) -> List[str]:
        """获取源代码"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if end_line is None:
                end_line = len(lines)
            
            return lines[start_line-1:end_line]
        except Exception as e:
            return [f"无法读取文件: {str(e)}"]
    
    def analyze_code_complexity(self, code: str) -> Dict:
        """分析代码复杂度"""
        try:
            tree = ast.parse(code)
            analyzer = CodeComplexityAnalyzer()
            analyzer.visit(tree)
            
            return {
                'cyclomatic_complexity': analyzer.complexity,
                'function_count': analyzer.function_count,
                'class_count': analyzer.class_count,
                'line_count': len(code.split('\n')),
                'suggestions': analyzer.suggestions
            }
        except Exception as e:
            return {'error': str(e)}
    
    def generate_test_cases(self, function_code: str) -> List[str]:
        """生成测试用例"""
        # 这里可以使用AI来生成测试用例
        test_cases = []
        
        try:
            # 解析函数
            tree = ast.parse(function_code)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    function_name = node.name
                    args = [arg.arg for arg in node.args.args]
                    
                    # 生成基本测试用例
                    test_cases.append(f"def test_{function_name}_basic():")
                    test_cases.append(f"    result = {function_name}({', '.join(['None'] * len(args))})")
                    test_cases.append(f"    assert result is not None")
                    test_cases.append("")
                    
                    # 生成边界测试用例
                    test_cases.append(f"def test_{function_name}_edge_cases():")
                    test_cases.append(f"    # TODO: 添加边界测试用例")
                    test_cases.append("")
        
        except Exception as e:
            test_cases.append(f"# 生成测试用例失败: {str(e)}")
        
        return test_cases
    
    def profile_performance(self, code: str, iterations: int = 100) -> Dict:
        """性能分析"""
        import cProfile
        import pstats
        import io
        
        # 创建性能分析器
        profiler = cProfile.Profile()
        
        try:
            # 编译代码
            compiled_code = compile(code, '<profile>', 'exec')
            
            # 运行性能分析
            profiler.enable()
            for _ in range(iterations):
                exec(compiled_code)
            profiler.disable()
            
            # 获取统计信息
            stats_stream = io.StringIO()
            stats = pstats.Stats(profiler, stream=stats_stream)
            stats.sort_stats('cumulative')
            stats.print_stats(20)  # 显示前20个函数
            
            return {
                'iterations': iterations,
                'profile_output': stats_stream.getvalue(),
                'total_calls': stats.total_calls,
                'total_time': stats.total_tt
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def memory_analysis(self, code: str) -> Dict:
        """内存分析"""
        try:
            import tracemalloc
            
            # 开始内存跟踪
            tracemalloc.start()
            
            # 执行代码
            exec(code)
            
            # 获取内存快照
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            # 分析结果
            memory_info = {
                'total_memory': sum(stat.size for stat in top_stats),
                'top_allocations': []
            }
            
            for stat in top_stats[:10]:
                memory_info['top_allocations'].append({
                    'file': stat.traceback.format()[0],
                    'size': stat.size,
                    'count': stat.count
                })
            
            tracemalloc.stop()
            return memory_info
            
        except Exception as e:
            return {'error': str(e)}
    
    def export_debug_session(self, file_path: str):
        """导出调试会话"""
        session_data = {
            'timestamp': time.time(),
            'breakpoints': [asdict(bp) for bp in self.breakpoints.values()],
            'execution_history': self.execution_history,
            'performance_data': self.performance_data,
            'ai_suggestions': self.ai_suggestions,
            'call_stack': [asdict(frame) for frame in self.call_stack]
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False, default=str)
    
    def import_debug_session(self, file_path: str):
        """导入调试会话"""
        with open(file_path, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        # 恢复断点
        for bp_data in session_data.get('breakpoints', []):
            bp = Breakpoint(**bp_data)
            self.breakpoints[bp.id] = bp
        
        # 恢复其他数据
        self.execution_history = session_data.get('execution_history', [])
        self.performance_data = session_data.get('performance_data', {})
        self.ai_suggestions = session_data.get('ai_suggestions', [])
    
    def _notify_state_change(self):
        """通知状态改变"""
        if self.on_state_change:
            self.on_state_change(self.state)
    
    def _notify_breakpoint_hit(self, breakpoint: Breakpoint, frame):
        """通知断点命中"""
        if self.on_breakpoint_hit:
            self.on_breakpoint_hit(breakpoint, frame)
    
    def _notify_step(self, frame):
        """通知步进"""
        if self.on_step:
            self.on_step(frame)
    
    def _notify_watchpoint_hit(self, variable_name: str, value: Any, frame):
        """通知监视点命中"""
        print(f"监视点命中: {variable_name} = {value}")


class CodeComplexityAnalyzer(ast.NodeVisitor):
    """代码复杂度分析器"""
    
    def __init__(self):
        self.complexity = 1  # 基础复杂度
        self.function_count = 0
        self.class_count = 0
        self.suggestions = []
    
    def visit_If(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_While(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_For(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_ExceptHandler(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_With(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node):
        self.function_count += 1
        
        # 检查函数复杂度
        old_complexity = self.complexity
        self.complexity = 1
        self.generic_visit(node)
        
        function_complexity = self.complexity
        if function_complexity > 10:
            self.suggestions.append(
                f"函数 '{node.name}' 复杂度过高 ({function_complexity})，建议重构"
            )
        
        self.complexity = old_complexity + function_complexity
    
    def visit_ClassDef(self, node):
        self.class_count += 1
        self.generic_visit(node)


class DebuggerUI:
    """调试器用户界面"""
    
    def __init__(self, debugger: AdvancedDebugger):
        self.debugger = debugger
        self.setup_callbacks()
    
    def setup_callbacks(self):
        """设置回调函数"""
        self.debugger.on_breakpoint_hit = self.on_breakpoint_hit
        self.debugger.on_step = self.on_step
        self.debugger.on_exception = self.on_exception
        self.debugger.on_state_change = self.on_state_change
    
    def on_breakpoint_hit(self, breakpoint: Breakpoint, frame):
        """断点命中处理"""
        print(f"\n🔴 断点命中: {breakpoint.file_path}:{breakpoint.line_number}")
        print(f"函数: {frame.f_code.co_name}")
        print(f"代码行: {linecache.getline(breakpoint.file_path, breakpoint.line_number).strip()}")
        
        # 显示局部变量
        print("\n📊 局部变量:")
        for name, value in frame.f_locals.items():
            if not name.startswith('__'):
                print(f"  {name} = {value} ({type(value).__name__})")
    
    def on_step(self, frame):
        """步进处理"""
        print(f"\n👣 步进: {frame.f_code.co_filename}:{frame.f_lineno}")
        print(f"函数: {frame.f_code.co_name}")
        print(f"代码行: {linecache.getline(frame.f_code.co_filename, frame.f_lineno).strip()}")
    
    def on_exception(self, exception_info):
        """异常处理"""
        print(f"\n❌ 异常: {exception_info['type']}")
        print(f"消息: {exception_info['message']}")
        print(f"追踪:\n{exception_info['traceback']}")
    
    def on_state_change(self, state: DebugState):
        """状态改变处理"""
        print(f"\n🔄 调试状态: {state.value}")


# 使用示例
def main():
    """使用示例"""
    # 创建调试器
    debugger = AdvancedDebugger()
    ui = DebuggerUI(debugger)
    
    # 要调试的代码
    test_code = """
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    result = fibonacci(10)
    print(f"斐波那契数列第10项: {result}")
    
    # 测试异常
    try:
        x = 1 / 0
    except ZeroDivisionError as e:
        print(f"捕获异常: {e}")

if __name__ == "__main__":
    main()
"""
    
    # 设置断点
    debugger.set_breakpoint("<debug>", 2)  # fibonacci函数第一行
    debugger.set_breakpoint("<debug>", 8)  # main函数第一行
    
    # 设置监视点
    debugger.set_watchpoint("n")
    debugger.set_watchpoint("result")
    
    print("🚀 开始调试...")
    
    # 开始调试
    debugger.start_debugging(test_code, "<debug>")
    
    print("\n📊 调试完成，性能数据:")
    for func_name, perf_data in debugger.performance_data.items():
        print(f"  {func_name}: 调用 {perf_data['call_count']} 次")
    
    print(f"\n🧠 AI建议数量: {len(debugger.ai_suggestions)}")
    for suggestion in debugger.ai_suggestions:
        print(f"  - {suggestion}")
    
    # 导出调试会话
    debugger.export_debug_session("debug_session.json")
    print("\n💾 调试会话已导出到 debug_session.json")


if __name__ == "__main__":
    main()