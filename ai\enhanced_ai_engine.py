# 增强AI引擎
import os
import json
import asyncio
import aiohttp
import openai
import anthropic
from typing import Dict, List, Optional, Any, AsyncGenerator, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import tiktoken

class AIProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"

class TaskType(Enum):
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    CODE_REFACTOR = "code_refactor"
    BUG_FIX = "bug_fix"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    OPTIMIZATION = "optimization"
    TRANSLATION = "translation"

@dataclass
class AIRequest:
    """AI请求"""
    task_type: TaskType
    prompt: str
    context: Dict[str, Any]
    language: str = "python"
    framework: str = ""
    max_tokens: int = 2000
    temperature: float = 0.7
    stream: bool = False
    metadata: Dict[str, Any] = None

@dataclass
class AIResponse:
    """AI响应"""
    content: str
    provider: AIProvider
    model: str
    tokens_used: int
    cost: float
    latency: float
    confidence: float
    metadata: Dict[str, Any] = None

class EnhancedAIEngine:
    """增强AI引擎"""
    
    def __init__(self):
        self.providers = {}
        self.models = {}
        self.cost_tracker = {}
        self.performance_metrics = {}
        self.context_cache = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化提供商
        self._initialize_providers()
        
        # 初始化模型映射
        self._initialize_models()
        
        # 启动性能监控
        self._start_performance_monitoring()
    
    def _load_config(self) -> Dict:
        """加载AI配置"""
        config_file = "ai_config.json"
        default_config = {
            "providers": {
                "openai": {
                    "enabled": True,
                    "api_key": "",
                    "base_url": "https://api.openai.com/v1",
                    "models": {
                        "gpt-4": {
                            "max_tokens": 8192,
                            "cost_per_1k_tokens": {"input": 0.03, "output": 0.06}
                        },
                        "gpt-3.5-turbo": {
                            "max_tokens": 4096,
                            "cost_per_1k_tokens": {"input": 0.001, "output": 0.002}
                        }
                    }
                },
                "anthropic": {
                    "enabled": True,
                    "api_key": "",
                    "models": {
                        "claude-3-opus": {
                            "max_tokens": 4096,
                            "cost_per_1k_tokens": {"input": 0.015, "output": 0.075}
                        },
                        "claude-3-sonnet": {
                            "max_tokens": 4096,
                            "cost_per_1k_tokens": {"input": 0.003, "output": 0.015}
                        }
                    }
                },
                "google": {
                    "enabled": False,
                    "api_key": "",
                    "models": {
                        "gemini-pro": {
                            "max_tokens": 2048,
                            "cost_per_1k_tokens": {"input": 0.0005, "output": 0.0015}
                        }
                    }
                }
            },
            "routing": {
                "default_provider": "openai",
                "default_model": "gpt-3.5-turbo",
                "task_routing": {
                    "code_generation": {"provider": "openai", "model": "gpt-4"},
                    "code_review": {"provider": "anthropic", "model": "claude-3-sonnet"},
                    "bug_fix": {"provider": "openai", "model": "gpt-4"},
                    "documentation": {"provider": "anthropic", "model": "claude-3-sonnet"}
                }
            },
            "optimization": {
                "enable_caching": True,
                "cache_ttl": 3600,
                "enable_batching": True,
                "batch_size": 5,
                "enable_streaming": True,
                "parallel_requests": True
            },
            "safety": {
                "content_filter": True,
                "rate_limiting": True,
                "max_requests_per_minute": 60,
                "timeout": 30
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self._merge_config(default_config, user_config)
        
        return default_config
    
    def _merge_config(self, default: Dict, user: Dict):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def _initialize_providers(self):
        """初始化AI提供商"""
        # OpenAI
        if self.config["providers"]["openai"]["enabled"]:
            api_key = self.config["providers"]["openai"]["api_key"]
            if api_key:
                openai.api_key = api_key
                self.providers[AIProvider.OPENAI] = openai
        
        # Anthropic
        if self.config["providers"]["anthropic"]["enabled"]:
            api_key = self.config["providers"]["anthropic"]["api_key"]
            if api_key:
                self.providers[AIProvider.ANTHROPIC] = anthropic.Anthropic(api_key=api_key)
    
    def _initialize_models(self):
        """初始化模型映射"""
        for provider_name, provider_config in self.config["providers"].items():
            if provider_config["enabled"]:
                provider = AIProvider(provider_name)
                self.models[provider] = list(provider_config["models"].keys())
    
    def _start_performance_monitoring(self):
        """启动性能监控"""
        def monitor():
            while True:
                self._collect_metrics()
                time.sleep(60)  # 每分钟收集一次指标
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _collect_metrics(self):
        """收集性能指标"""
        # 收集各种性能指标
        metrics = {
            "timestamp": time.time(),
            "total_requests": sum(self.performance_metrics.get(p.value, {}).get("requests", 0) 
                                for p in AIProvider),
            "total_cost": sum(self.cost_tracker.values()),
            "average_latency": self._calculate_average_latency(),
            "cache_hit_rate": self._calculate_cache_hit_rate()
        }
        
        # 保存指标
        self._save_metrics(metrics)
    
    def _calculate_average_latency(self) -> float:
        """计算平均延迟"""
        total_latency = 0
        total_requests = 0
        
        for provider_metrics in self.performance_metrics.values():
            if "latencies" in provider_metrics:
                latencies = provider_metrics["latencies"]
                total_latency += sum(latencies)
                total_requests += len(latencies)
        
        return total_latency / total_requests if total_requests > 0 else 0
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        total_hits = sum(metrics.get("cache_hits", 0) 
                        for metrics in self.performance_metrics.values())
        total_requests = sum(metrics.get("requests", 0) 
                           for metrics in self.performance_metrics.values())
        
        return total_hits / total_requests if total_requests > 0 else 0
    
    def _save_metrics(self, metrics: Dict):
        """保存性能指标"""
        metrics_file = "ai_metrics.json"
        
        # 读取现有指标
        existing_metrics = []
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r', encoding='utf-8') as f:
                existing_metrics = json.load(f)
        
        # 添加新指标
        existing_metrics.append(metrics)
        
        # 只保留最近1000条记录
        existing_metrics = existing_metrics[-1000:]
        
        # 保存指标
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(existing_metrics, f, indent=2)
    
    async def generate_code(self, request: AIRequest) -> AIResponse:
        """生成代码"""
        # 构建专门的代码生成提示
        system_prompt = self._build_code_generation_prompt(request)
        
        # 选择最佳提供商和模型
        provider, model = self._select_provider_and_model(request.task_type)
        
        # 执行请求
        return await self._execute_request(provider, model, system_prompt, request)
    
    async def review_code(self, request: AIRequest) -> AIResponse:
        """代码审查"""
        system_prompt = self._build_code_review_prompt(request)
        provider, model = self._select_provider_and_model(TaskType.CODE_REVIEW)
        return await self._execute_request(provider, model, system_prompt, request)
    
    async def refactor_code(self, request: AIRequest) -> AIResponse:
        """代码重构"""
        system_prompt = self._build_code_refactor_prompt(request)
        provider, model = self._select_provider_and_model(TaskType.CODE_REFACTOR)
        return await self._execute_request(provider, model, system_prompt, request)
    
    async def fix_bug(self, request: AIRequest) -> AIResponse:
        """修复Bug"""
        system_prompt = self._build_bug_fix_prompt(request)
        provider, model = self._select_provider_and_model(TaskType.BUG_FIX)
        return await self._execute_request(provider, model, system_prompt, request)
    
    async def generate_documentation(self, request: AIRequest) -> AIResponse:
        """生成文档"""
        system_prompt = self._build_documentation_prompt(request)
        provider, model = self._select_provider_and_model(TaskType.DOCUMENTATION)
        return await self._execute_request(provider, model, system_prompt, request)
    
    async def generate_tests(self, request: AIRequest) -> AIResponse:
        """生成测试"""
        system_prompt = self._build_testing_prompt(request)
        provider, model = self._select_provider_and_model(TaskType.TESTING)
        return await self._execute_request(provider, model, system_prompt, request)
    
    async def optimize_code(self, request: AIRequest) -> AIResponse:
        """优化代码"""
        system_prompt = self._build_optimization_prompt(request)
        provider, model = self._select_provider_and_model(TaskType.OPTIMIZATION)
        return await self._execute_request(provider, model, system_prompt, request)
    
    def _build_code_generation_prompt(self, request: AIRequest) -> str:
        """构建代码生成提示"""
        return f"""你是一个专业的{request.language}开发专家。请根据以下要求生成高质量的代码：

编程语言: {request.language}
框架: {request.framework or '无特定框架'}
需求描述: {request.prompt}

请遵循以下原则：
1. 代码应该清晰、可读、可维护
2. 遵循{request.language}的最佳实践和编码规范
3. 包含适当的注释和文档字符串
4. 考虑错误处理和边界情况
5. 如果适用，包含类型提示
6. 代码应该是完整且可运行的

上下文信息：
{json.dumps(request.context, indent=2, ensure_ascii=False)}

请直接返回代码，不需要额外的解释。"""
    
    def _build_code_review_prompt(self, request: AIRequest) -> str:
        """构建代码审查提示"""
        return f"""你是一个资深的代码审查专家。请对以下{request.language}代码进行全面审查：

代码：
{request.prompt}

请从以下方面进行审查：
1. 代码质量和可读性
2. 性能优化建议
3. 安全性问题
4. 最佳实践遵循情况
5. 潜在的Bug和错误
6. 代码结构和设计模式
7. 测试覆盖率建议

请提供具体的改进建议和修改后的代码示例。"""
    
    def _build_code_refactor_prompt(self, request: AIRequest) -> str:
        """构建代码重构提示"""
        return f"""你是一个代码重构专家。请对以下{request.language}代码进行重构：

原始代码：
{request.prompt}

重构目标：
{request.context.get('refactor_goals', '提高代码质量和可维护性')}

请遵循以下重构原则：
1. 保持功能不变
2. 提高代码可读性
3. 减少代码重复
4. 改善代码结构
5. 优化性能
6. 增强可测试性

请提供重构后的代码和重构说明。"""
    
    def _build_bug_fix_prompt(self, request: AIRequest) -> str:
        """构建Bug修复提示"""
        return f"""你是一个Bug修复专家。请分析并修复以下{request.language}代码中的问题：

有问题的代码：
{request.prompt}

错误信息：
{request.context.get('error_message', '未提供错误信息')}

预期行为：
{request.context.get('expected_behavior', '未指定预期行为')}

请：
1. 分析问题的根本原因
2. 提供修复后的代码
3. 解释修复的原理
4. 建议如何避免类似问题"""
    
    def _build_documentation_prompt(self, request: AIRequest) -> str:
        """构建文档生成提示"""
        return f"""你是一个技术文档专家。请为以下{request.language}代码生成完整的文档：

代码：
{request.prompt}

请生成：
1. 函数/类的详细说明
2. 参数和返回值描述
3. 使用示例
4. 注意事项和限制
5. 相关的类型信息

文档格式应该符合{request.language}的标准文档规范。"""
    
    def _build_testing_prompt(self, request: AIRequest) -> str:
        """构建测试生成提示"""
        return f"""你是一个测试专家。请为以下{request.language}代码生成全面的测试用例：

待测试代码：
{request.prompt}

请生成：
1. 单元测试用例
2. 边界条件测试
3. 异常情况测试
4. 性能测试（如适用）
5. 集成测试（如适用）

测试框架：{request.context.get('test_framework', 'pytest' if request.language == 'python' else '标准测试框架')}

请确保测试覆盖率达到90%以上。"""
    
    def _build_optimization_prompt(self, request: AIRequest) -> str:
        """构建优化提示"""
        return f"""你是一个性能优化专家。请优化以下{request.language}代码：

原始代码：
{request.prompt}

优化目标：
{request.context.get('optimization_goals', '提高执行效率和减少资源消耗')}

请：
1. 分析性能瓶颈
2. 提供优化后的代码
3. 解释优化策略
4. 估算性能提升
5. 考虑内存使用优化"""
    
    def _select_provider_and_model(self, task_type: TaskType) -> tuple[AIProvider, str]:
        """选择最佳提供商和模型"""
        # 检查任务特定路由
        task_routing = self.config["routing"]["task_routing"]
        if task_type.value in task_routing:
            route = task_routing[task_type.value]
            provider = AIProvider(route["provider"])
            model = route["model"]
            
            # 验证提供商是否可用
            if provider in self.providers:
                return provider, model
        
        # 使用默认路由
        default_provider = AIProvider(self.config["routing"]["default_provider"])
        default_model = self.config["routing"]["default_model"]
        
        return default_provider, default_model
    
    async def _execute_request(self, provider: AIProvider, model: str, 
                             system_prompt: str, request: AIRequest) -> AIResponse:
        """执行AI请求"""
        start_time = time.time()
        
        try:
            # 检查缓存
            if self.config["optimization"]["enable_caching"]:
                cache_key = self._generate_cache_key(provider, model, system_prompt, request)
                cached_response = self._get_cached_response(cache_key)
                if cached_response:
                    return cached_response
            
            # 执行请求
            if provider == AIProvider.OPENAI:
                response = await self._execute_openai_request(model, system_prompt, request)
            elif provider == AIProvider.ANTHROPIC:
                response = await self._execute_anthropic_request(model, system_prompt, request)
            else:
                raise ValueError(f"不支持的提供商: {provider}")
            
            # 计算延迟
            latency = time.time() - start_time
            response.latency = latency
            
            # 更新性能指标
            self._update_performance_metrics(provider, latency, response.tokens_used)
            
            # 更新成本跟踪
            self._update_cost_tracking(provider, model, response.tokens_used)
            
            # 缓存响应
            if self.config["optimization"]["enable_caching"]:
                self._cache_response(cache_key, response)
            
            return response
            
        except Exception as e:
            # 记录错误
            self._log_error(provider, model, str(e))
            raise
    
    async def _execute_openai_request(self, model: str, system_prompt: str, 
                                    request: AIRequest) -> AIResponse:
        """执行OpenAI请求"""
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": request.prompt}
        ]
        
        # 添加上下文消息
        if request.context.get("conversation_history"):
            for msg in request.context["conversation_history"]:
                messages.insert(-1, msg)
        
        try:
            if request.stream:
                # 流式响应
                response = await openai.ChatCompletion.acreate(
                    model=model,
                    messages=messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    stream=True
                )
                
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.get("content"):
                        content += chunk.choices[0].delta["content"]
                
                tokens_used = self._estimate_tokens(content)
                
            else:
                # 普通响应
                response = await openai.ChatCompletion.acreate(
                    model=model,
                    messages=messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
                
                content = response.choices[0].message.content
                tokens_used = response.usage.total_tokens
            
            return AIResponse(
                content=content,
                provider=AIProvider.OPENAI,
                model=model,
                tokens_used=tokens_used,
                cost=self._calculate_cost(AIProvider.OPENAI, model, tokens_used),
                latency=0,  # 将在上层设置
                confidence=0.9,  # 默认置信度
                metadata={"finish_reason": response.choices[0].finish_reason if not request.stream else "stop"}
            )
            
        except Exception as e:
            raise Exception(f"OpenAI请求失败: {str(e)}")
    
    async def _execute_anthropic_request(self, model: str, system_prompt: str, 
                                       request: AIRequest) -> AIResponse:
        """执行Anthropic请求"""
        try:
            client = self.providers[AIProvider.ANTHROPIC]
            
            response = await client.messages.create(
                model=model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                system=system_prompt,
                messages=[{"role": "user", "content": request.prompt}]
            )
            
            content = response.content[0].text
            tokens_used = response.usage.input_tokens + response.usage.output_tokens
            
            return AIResponse(
                content=content,
                provider=AIProvider.ANTHROPIC,
                model=model,
                tokens_used=tokens_used,
                cost=self._calculate_cost(AIProvider.ANTHROPIC, model, tokens_used),
                latency=0,
                confidence=0.9,
                metadata={"stop_reason": response.stop_reason}
            )
            
        except Exception as e:
            raise Exception(f"Anthropic请求失败: {str(e)}")
    
    def _estimate_tokens(self, text: str, model: str = "gpt-3.5-turbo") -> int:
        """估算token数量"""
        try:
            encoding = tiktoken.encoding_for_model(model)
            return len(encoding.encode(text))
        except:
            # 简单估算：1个token约等于4个字符
            return len(text) // 4
    
    def _calculate_cost(self, provider: AIProvider, model: str, tokens_used: int) -> float:
        """计算成本"""
        provider_config = self.config["providers"][provider.value]
        model_config = provider_config["models"].get(model, {})
        cost_config = model_config.get("cost_per_1k_tokens", {"input": 0, "output": 0})
        
        # 简化计算，假设输入输出各占一半
        input_tokens = tokens_used // 2
        output_tokens = tokens_used - input_tokens
        
        input_cost = (input_tokens / 1000) * cost_config["input"]
        output_cost = (output_tokens / 1000) * cost_config["output"]
        
        return input_cost + output_cost
    
    def _generate_cache_key(self, provider: AIProvider, model: str, 
                          system_prompt: str, request: AIRequest) -> str:
        """生成缓存键"""
        import hashlib
        
        key_data = f"{provider.value}:{model}:{system_prompt}:{request.prompt}:{request.temperature}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str) -> Optional[AIResponse]:
        """获取缓存响应"""
        if cache_key in self.context_cache:
            cached_item = self.context_cache[cache_key]
            
            # 检查是否过期
            if time.time() - cached_item["timestamp"] < self.config["optimization"]["cache_ttl"]:
                return cached_item["response"]
            else:
                # 删除过期缓存
                del self.context_cache[cache_key]
        
        return None
    
    def _cache_response(self, cache_key: str, response: AIResponse):
        """缓存响应"""
        self.context_cache[cache_key] = {
            "response": response,
            "timestamp": time.time()
        }
        
        # 限制缓存大小
        if len(self.context_cache) > 1000:
            # 删除最旧的缓存项
            oldest_key = min(self.context_cache.keys(), 
                           key=lambda k: self.context_cache[k]["timestamp"])
            del self.context_cache[oldest_key]
    
    def _update_performance_metrics(self, provider: AIProvider, latency: float, tokens_used: int):
        """更新性能指标"""
        provider_key = provider.value
        
        if provider_key not in self.performance_metrics:
            self.performance_metrics[provider_key] = {
                "requests": 0,
                "latencies": [],
                "tokens": [],
                "cache_hits": 0
            }
        
        metrics = self.performance_metrics[provider_key]
        metrics["requests"] += 1
        metrics["latencies"].append(latency)
        metrics["tokens"].append(tokens_used)
        
        # 只保留最近100次请求的数据
        if len(metrics["latencies"]) > 100:
            metrics["latencies"] = metrics["latencies"][-100:]
            metrics["tokens"] = metrics["tokens"][-100:]
    
    def _update_cost_tracking(self, provider: AIProvider, model: str, tokens_used: int):
        """更新成本跟踪"""
        cost = self._calculate_cost(provider, model, tokens_used)
        
        provider_key = provider.value
        if provider_key not in self.cost_tracker:
            self.cost_tracker[provider_key] = 0
        
        self.cost_tracker[provider_key] += cost
    
    def _log_error(self, provider: AIProvider, model: str, error: str):
        """记录错误"""
        error_log = {
            "timestamp": time.time(),
            "provider": provider.value,
            "model": model,
            "error": error
        }
        
        # 保存到错误日志文件
        with open("ai_errors.log", "a", encoding="utf-8") as f:
            f.write(json.dumps(error_log, ensure_ascii=False) + "\n")
    
    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        return {
            "providers": self.performance_metrics,
            "total_cost": sum(self.cost_tracker.values()),
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "average_latency": self._calculate_average_latency()
        }
    
    def get_cost_breakdown(self) -> Dict:
        """获取成本分解"""
        return self.cost_tracker.copy()
    
    def clear_cache(self):
        """清除缓存"""
        self.context_cache.clear()
    
    def set_provider_config(self, provider: AIProvider, config: Dict):
        """设置提供商配置"""
        self.config["providers"][provider.value].update(config)
        self._initialize_providers()
    
    async def batch_process(self, requests: List[AIRequest]) -> List[AIResponse]:
        """批量处理请求"""
        if not self.config["optimization"]["enable_batching"]:
            # 串行处理
            responses = []
            for request in requests:
                response = await self._route_request(request)
                responses.append(response)
            return responses
        
        # 并行处理
        if self.config["optimization"]["parallel_requests"]:
            tasks = [self._route_request(request) for request in requests]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常
            processed_responses = []
            for response in responses:
                if isinstance(response, Exception):
                    # 创建错误响应
                    error_response = AIResponse(
                        content=f"处理失败: {str(response)}",
                        provider=AIProvider.OPENAI,
                        model="error",
                        tokens_used=0,
                        cost=0,
                        latency=0,
                        confidence=0
                    )
                    processed_responses.append(error_response)
                else:
                    processed_responses.append(response)
            
            return processed_responses
        else:
            # 批量但串行处理
            responses = []
            for request in requests:
                try:
                    response = await self._route_request(request)
                    responses.append(response)
                except Exception as e:
                    error_response = AIResponse(
                        content=f"处理失败: {str(e)}",
                        provider=AIProvider.OPENAI,
                        model="error",
                        tokens_used=0,
                        cost=0,
                        latency=0,
                        confidence=0
                    )
                    responses.append(error_response)
            
            return responses
    
    async def _route_request(self, request: AIRequest) -> AIResponse:
        """路由请求到相应的处理方法"""
        if request.task_type == TaskType.CODE_GENERATION:
            return await self.generate_code(request)
        elif request.task_type == TaskType.CODE_REVIEW:
            return await self.review_code(request)
        elif request.task_type == TaskType.CODE_REFACTOR:
            return await self.refactor_code(request)
        elif request.task_type == TaskType.BUG_FIX:
            return await self.fix_bug(request)
        elif request.task_type == TaskType.DOCUMENTATION:
            return await self.generate_documentation(request)
        elif request.task_type == TaskType.TESTING:
            return await self.generate_tests(request)
        elif request.task_type == TaskType.OPTIMIZATION:
            return await self.optimize_code(request)
        else:
            raise ValueError(f"不支持的任务类型: {request.task_type}")
    
    async def stream_response(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """流式响应"""
        request.stream = True
        
        # 选择提供商和模型
        provider, model = self._select_provider_and_model(request.task_type)
        
        if provider == AIProvider.OPENAI:
            async for chunk in self._stream_openai_response(model, request):
                yield chunk
        else:
            # 对于不支持流式的提供商，模拟流式响应
            response = await self._execute_request(provider, model, 
                                                 self._build_code_generation_prompt(request), 
                                                 request)
            
            # 分块发送响应
            content = response.content
            chunk_size = 50
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i+chunk_size]
                yield chunk
                await asyncio.sleep(0.1)  # 模拟延迟
    
    async def _stream_openai_response(self, model: str, request: AIRequest) -> AsyncGenerator[str, None]:
        """OpenAI流式响应"""
        system_prompt = self._build_code_generation_prompt(request)
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": request.prompt}
        ]
        
        response = await openai.ChatCompletion.acreate(
            model=model,
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            stream=True
        )
        
        async for chunk in response:
            if chunk.choices[0].delta.get("content"):
                yield chunk.choices[0].delta["content"]


# 使用示例
async def main():
    """使用示例"""
    engine = EnhancedAIEngine()
    
    # 代码生成请求
    request = AIRequest(
        task_type=TaskType.CODE_GENERATION,
        prompt="创建一个Python函数，用于计算斐波那契数列",
        context={"requirements": "使用递归实现，包含缓存优化"},
        language="python",
        max_tokens=1000,
        temperature=0.7
    )
    
    # 执行请求
    response = await engine.generate_code(request)
    print(f"生成的代码:\n{response.content}")
    print(f"使用的tokens: {response.tokens_used}")
    print(f"成本: ${response.cost:.4f}")
    print(f"延迟: {response.latency:.2f}秒")
    
    # 批量处理
    requests = [
        AIRequest(TaskType.CODE_GENERATION, "创建一个排序函数", {}, "python"),
        AIRequest(TaskType.CODE_REVIEW, "def sort(arr): return sorted(arr)", {}, "python"),
        AIRequest(TaskType.DOCUMENTATION, "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)", {}, "python")
    ]
    
    responses = await engine.batch_process(requests)
    for i, response in enumerate(responses):
        print(f"\n请求 {i+1} 响应:\n{response.content[:100]}...")
    
    # 获取性能指标
    metrics = engine.get_performance_metrics()
    print(f"\n性能指标: {metrics}")


if __name__ == "__main__":
    asyncio.run(main())