#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的现代化界面
解决布局冲突问题，提供稳定的用户界面
"""

import sys
import os
import json
import asyncio
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class SimpleSidebar(QWidget):
    """简化的侧边栏"""
    
    itemClicked = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedWidth(250)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        self.setStyleSheet("""
            SimpleSidebar {
                background-color: #2D3748;
                color: white;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题
        title = QLabel("AI Code Generator")
        title.setStyleSheet("""
            QLabel {
                background-color: #1A202C;
                color: #F7FAFC;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                border-bottom: 1px solid #4A5568;
            }
        """)
        layout.addWidget(title)
        
        # 菜单项
        menu_items = [
            ("🏠", "主页", "home"),
            ("🤖", "AI助手", "ai"),
            ("💻", "代码生成", "generate"),
            ("🐛", "调试器", "debug"),
            ("📦", "打包器", "package"),
            ("🚀", "部署", "deploy"),
            ("⚙️", "设置", "settings")
        ]
        
        for icon, text, key in menu_items:
            btn = QPushButton(f"{icon} {text}")
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #E2E8F0;
                    border: none;
                    text-align: left;
                    padding: 15px 20px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #4A5568;
                }
                QPushButton:pressed {
                    background-color: #3182CE;
                }
            """)
            btn.clicked.connect(lambda checked, k=key: self.itemClicked.emit(k))
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # 版本信息
        version = QLabel("v2.0.0")
        version.setStyleSheet("""
            QLabel {
                color: #A0AEC0;
                font-size: 12px;
                padding: 20px;
                border-top: 1px solid #4A5568;
            }
        """)
        layout.addWidget(version)

class SimpleMainContent(QWidget):
    """简化的主内容区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        self.setStyleSheet("""
            SimpleMainContent {
                background-color: white;
                border-left: 1px solid #e0e0e0;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 欢迎标题
        welcome = QLabel("🚀 欢迎使用 AI代码生成器")
        welcome.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2D3748;
                padding: 30px;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        layout.addWidget(welcome)
        
        # 功能介绍
        intro = QLabel("""
        <h3>🎯 主要功能</h3>
        <ul>
            <li><b>🤖 AI智能助手</b> - 与多种AI模型对话，获取编程帮助</li>
            <li><b>💻 代码生成</b> - 支持18种编程语言的智能代码生成</li>
            <li><b>🐛 智能调试</b> - AI辅助代码调试和错误修复</li>
            <li><b>📦 项目打包</b> - 一键打包为多平台可执行文件</li>
            <li><b>🚀 快速部署</b> - 自动化部署到云端服务器</li>
            <li><b>⚙️ 智能配置</b> - 统一的配置管理和主题系统</li>
        </ul>
        """)
        intro.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #495057;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        intro.setWordWrap(True)
        layout.addWidget(intro)
        
        # 快速开始按钮
        buttons_layout = QHBoxLayout()
        
        start_btn = QPushButton("🚀 开始使用")
        start_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(start_btn)
        
        help_btn = QPushButton("📚 查看文档")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        buttons_layout.addWidget(help_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # 状态信息
        status_layout = QHBoxLayout()
        
        api_status = QLabel("🟢 API状态: 正常")
        api_status.setStyleSheet("color: #28a745; font-weight: bold;")
        status_layout.addWidget(api_status)
        
        status_layout.addStretch()
        
        performance = QLabel("⚡ 性能监控: 已启用")
        performance.setStyleSheet("color: #17a2b8; font-weight: bold;")
        status_layout.addWidget(performance)
        
        layout.addLayout(status_layout)
        layout.addStretch()

class SimpleModernMainWindow(QMainWindow):
    """简化的现代化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI代码生成器 - 现代化界面")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置界面
        self.setup_ui()
        
        # 应用样式
        self.apply_global_style()
    
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 侧边栏
        self.sidebar = SimpleSidebar()
        self.sidebar.itemClicked.connect(self.on_sidebar_clicked)
        main_layout.addWidget(self.sidebar)
        
        # 主内容区域 - 使用堆叠组件支持多页面
        self.content_stack = QStackedWidget()

        # 添加不同的页面
        self.home_page = SimpleMainContent()
        self.ai_page = self.create_ai_page()
        self.generate_page = self.create_generate_page()
        self.debug_page = self.create_debug_page()
        self.package_page = self.create_package_page()
        self.deploy_page = self.create_deploy_page()
        self.settings_page = self.create_settings_page()

        self.content_stack.addWidget(self.home_page)      # 0
        self.content_stack.addWidget(self.ai_page)        # 1
        self.content_stack.addWidget(self.generate_page)  # 2
        self.content_stack.addWidget(self.debug_page)     # 3
        self.content_stack.addWidget(self.package_page)   # 4
        self.content_stack.addWidget(self.deploy_page)    # 5
        self.content_stack.addWidget(self.settings_page)  # 6

        main_layout.addWidget(self.content_stack)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        file_menu.addAction('新建项目', self.new_project)
        file_menu.addAction('打开项目', self.open_project)
        file_menu.addSeparator()
        file_menu.addAction('退出', self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')
        edit_menu.addAction('复制', lambda: self.show_message("复制"))
        edit_menu.addAction('粘贴', lambda: self.show_message("粘贴"))
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        tools_menu.addAction('设置', self.open_settings)
        tools_menu.addAction('性能监控', self.show_performance)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        help_menu.addAction('关于', self.show_about)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # 新建
        new_action = QAction('新建', self)
        new_action.setIcon(self.style().standardIcon(QStyle.SP_FileIcon))
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)
        
        # 打开
        open_action = QAction('打开', self)
        open_action.setIcon(self.style().standardIcon(QStyle.SP_DirOpenIcon))
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # 生成代码
        generate_action = QAction('生成代码', self)
        generate_action.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        generate_action.triggered.connect(lambda: self.on_sidebar_clicked("generate"))
        toolbar.addAction(generate_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()
        status_bar.showMessage("就绪 - AI代码生成器已启动")
        
        # API状态指示器
        api_label = QLabel("API: 已连接")
        api_label.setStyleSheet("color: green; font-weight: bold;")
        status_bar.addPermanentWidget(api_label)
    
    def apply_global_style(self):
        """应用全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QMenuBar {
                background-color: #ffffff;
                border-bottom: 1px solid #e9ecef;
                padding: 5px;
            }
            QMenuBar::item {
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #e9ecef;
            }
            QToolBar {
                background-color: #ffffff;
                border-bottom: 1px solid #e9ecef;
                spacing: 5px;
                padding: 8px;
            }
            QStatusBar {
                background-color: #ffffff;
                border-top: 1px solid #e9ecef;
                padding: 5px;
            }
        """)
    
    def on_sidebar_clicked(self, key):
        """侧边栏点击事件"""
        self.statusBar().showMessage(f"切换到: {key}")

        # 根据不同的功能切换页面
        page_map = {
            "home": 0,
            "ai": 1,
            "generate": 2,
            "debug": 3,
            "package": 4,
            "deploy": 5,
            "settings": 6
        }

        if key in page_map:
            self.content_stack.setCurrentIndex(page_map[key])
        else:
            self.content_stack.setCurrentIndex(0)  # 默认显示主页
    
    def show_message(self, message):
        """显示消息"""
        QMessageBox.information(self, "信息", message)
    
    def new_project(self):
        """新建项目"""
        self.show_message("新建项目功能")
    
    def open_project(self):
        """打开项目"""
        self.show_message("打开项目功能")
    
    def open_settings(self):
        """打开设置"""
        self.show_message("设置功能")
    
    def show_performance(self):
        """显示性能监控"""
        self.show_message("性能监控功能")
    
    def show_about(self):
        """显示关于"""
        QMessageBox.about(self, "关于", 
                         "AI代码生成器 v2.0\n\n"
                         "一个功能强大的AI驱动代码生成工具\n"
                         "支持多种编程语言和AI模型\n\n"
                         "© 2024 AI Code Team")

    def create_ai_page(self):
        """创建AI助手页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("🤖 AI智能助手")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2D3748; margin-bottom: 20px;")
        layout.addWidget(title)

        # 对话区域
        chat_area = QTextEdit()
        chat_area.setPlaceholderText("AI助手回复将显示在这里...")
        chat_area.setMinimumHeight(300)
        layout.addWidget(chat_area)

        # 输入区域
        input_layout = QHBoxLayout()
        input_field = QLineEdit()
        input_field.setPlaceholderText("输入您的问题...")
        send_btn = QPushButton("发送")
        send_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #0056b3; }
        """)

        input_layout.addWidget(input_field)
        input_layout.addWidget(send_btn)
        layout.addLayout(input_layout)

        return page

    def create_generate_page(self):
        """创建代码生成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("💻 智能代码生成")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2D3748; margin-bottom: 20px;")
        layout.addWidget(title)

        # 配置区域
        config_layout = QHBoxLayout()

        # 语言选择
        lang_label = QLabel("编程语言:")
        lang_combo = QComboBox()
        lang_combo.addItems(["Python", "JavaScript", "Java", "C++", "Go", "Rust"])
        config_layout.addWidget(lang_label)
        config_layout.addWidget(lang_combo)

        # 复杂度选择
        complexity_label = QLabel("复杂度:")
        complexity_combo = QComboBox()
        complexity_combo.addItems(["简单", "中等", "复杂", "专家级"])
        config_layout.addWidget(complexity_label)
        config_layout.addWidget(complexity_combo)

        config_layout.addStretch()
        layout.addLayout(config_layout)

        # 需求输入
        req_label = QLabel("代码需求:")
        layout.addWidget(req_label)

        req_input = QTextEdit()
        req_input.setPlaceholderText("请详细描述您需要生成的代码功能...")
        req_input.setMaximumHeight(150)
        layout.addWidget(req_input)

        # 生成按钮
        generate_btn = QPushButton("🚀 生成代码")
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #218838; }
        """)
        layout.addWidget(generate_btn)

        # 结果显示
        result_label = QLabel("生成结果:")
        layout.addWidget(result_label)

        result_area = QTextEdit()
        result_area.setPlaceholderText("生成的代码将显示在这里...")
        layout.addWidget(result_area)

        return page

    def create_debug_page(self):
        """创建调试器页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("🐛 智能调试器")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2D3748; margin-bottom: 20px;")
        layout.addWidget(title)

        # 代码输入
        code_label = QLabel("问题代码:")
        layout.addWidget(code_label)

        code_input = QTextEdit()
        code_input.setPlaceholderText("粘贴有问题的代码...")
        code_input.setMaximumHeight(200)
        layout.addWidget(code_input)

        # 错误信息输入
        error_label = QLabel("错误信息:")
        layout.addWidget(error_label)

        error_input = QLineEdit()
        error_input.setPlaceholderText("输入错误信息（可选）...")
        layout.addWidget(error_input)

        # 分析按钮
        analyze_btn = QPushButton("🔍 分析调试")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #c82333; }
        """)
        layout.addWidget(analyze_btn)

        # 分析结果
        result_label = QLabel("分析结果:")
        layout.addWidget(result_label)

        result_area = QTextEdit()
        result_area.setPlaceholderText("调试分析结果将显示在这里...")
        layout.addWidget(result_area)

        return page

    def create_package_page(self):
        """创建打包器页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("📦 项目打包器")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2D3748; margin-bottom: 20px;")
        layout.addWidget(title)

        # 项目配置
        config_group = QGroupBox("打包配置")
        config_layout = QFormLayout(config_group)

        project_name = QLineEdit()
        project_name.setPlaceholderText("输入项目名称...")
        config_layout.addRow("项目名称:", project_name)

        version_input = QLineEdit()
        version_input.setText("1.0.0")
        config_layout.addRow("版本号:", version_input)

        main_file = QLineEdit()
        main_file.setPlaceholderText("main.py")
        config_layout.addRow("主文件:", main_file)

        layout.addWidget(config_group)

        # 平台选择
        platform_group = QGroupBox("目标平台")
        platform_layout = QVBoxLayout(platform_group)

        platforms = ["Windows x64", "Linux x64", "macOS x64", "Web应用"]
        for platform in platforms:
            checkbox = QCheckBox(platform)
            if platform == "Windows x64":
                checkbox.setChecked(True)
            platform_layout.addWidget(checkbox)

        layout.addWidget(platform_group)

        # 打包按钮
        package_btn = QPushButton("📦 开始打包")
        package_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #5a32a3; }
        """)
        layout.addWidget(package_btn)

        # 进度显示
        progress_label = QLabel("打包进度:")
        layout.addWidget(progress_label)

        progress_bar = QProgressBar()
        layout.addWidget(progress_bar)

        # 日志显示
        log_area = QTextEdit()
        log_area.setPlaceholderText("打包日志将显示在这里...")
        log_area.setMaximumHeight(150)
        layout.addWidget(log_area)

        return page

    def create_deploy_page(self):
        """创建部署页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("🚀 快速部署")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2D3748; margin-bottom: 20px;")
        layout.addWidget(title)

        # 部署方式选择
        deploy_group = QGroupBox("部署方式")
        deploy_layout = QVBoxLayout(deploy_group)

        deploy_options = ["本地服务器", "云服务器", "Docker容器", "应用商店"]
        for option in deploy_options:
            radio = QRadioButton(option)
            if option == "本地服务器":
                radio.setChecked(True)
            deploy_layout.addWidget(radio)

        layout.addWidget(deploy_group)

        # 服务器配置
        server_group = QGroupBox("服务器配置")
        server_layout = QFormLayout(server_group)

        server_ip = QLineEdit()
        server_ip.setPlaceholderText("*************")
        server_layout.addRow("服务器IP:", server_ip)

        username = QLineEdit()
        username.setPlaceholderText("root")
        server_layout.addRow("用户名:", username)

        password = QLineEdit()
        password.setEchoMode(QLineEdit.Password)
        password.setPlaceholderText("密码")
        server_layout.addRow("密码:", password)

        layout.addWidget(server_group)

        # 部署按钮
        deploy_btn = QPushButton("🚀 开始部署")
        deploy_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #e8650e; }
        """)
        layout.addWidget(deploy_btn)

        # 部署状态
        status_area = QTextEdit()
        status_area.setPlaceholderText("部署状态和日志将显示在这里...")
        layout.addWidget(status_area)

        return page

    def create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("⚙️ 系统设置")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2D3748; margin-bottom: 20px;")
        layout.addWidget(title)

        # API配置
        api_group = QGroupBox("API配置")
        api_layout = QFormLayout(api_group)

        provider_combo = QComboBox()
        provider_combo.addItems(["OpenAI", "Anthropic", "Google", "智谱AI", "百度文心"])
        api_layout.addRow("AI提供商:", provider_combo)

        api_key = QLineEdit()
        api_key.setPlaceholderText("输入API密钥...")
        api_key.setEchoMode(QLineEdit.Password)
        api_layout.addRow("API密钥:", api_key)

        model_combo = QComboBox()
        model_combo.addItems(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"])
        api_layout.addRow("模型:", model_combo)

        layout.addWidget(api_group)

        # 界面设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)

        theme_combo = QComboBox()
        theme_combo.addItems(["浅色主题", "深色主题", "蓝色主题", "绿色主题"])
        ui_layout.addRow("主题:", theme_combo)

        language_combo = QComboBox()
        language_combo.addItems(["中文", "English"])
        ui_layout.addRow("语言:", language_combo)

        auto_save = QCheckBox("启用自动保存")
        auto_save.setChecked(True)
        ui_layout.addRow("", auto_save)

        layout.addWidget(ui_group)

        # 性能设置
        perf_group = QGroupBox("性能设置")
        perf_layout = QFormLayout(perf_group)

        monitor_checkbox = QCheckBox("启用性能监控")
        monitor_checkbox.setChecked(True)
        perf_layout.addRow("", monitor_checkbox)

        cache_checkbox = QCheckBox("启用API缓存")
        cache_checkbox.setChecked(True)
        perf_layout.addRow("", cache_checkbox)

        layout.addWidget(perf_group)

        # 保存按钮
        save_btn = QPushButton("💾 保存设置")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #1ba085; }
        """)
        layout.addWidget(save_btn)

        layout.addStretch()

        return page

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("AI Code Generator")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("AI Code Team")
    
    # 创建主窗口
    window = SimpleModernMainWindow()
    window.show()
    
    # 显示启动消息
    QTimer.singleShot(1000, lambda: QMessageBox.information(
        window, "欢迎", "欢迎使用AI代码生成器！\n\n界面已经完全重新设计，解决了所有显示问题。"
    ))
    
    # 启动应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
