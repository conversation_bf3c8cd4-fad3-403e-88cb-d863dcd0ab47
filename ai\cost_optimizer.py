# 成本优化建议系统

import json
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

@dataclass
class CostMetrics:
    """成本指标"""
    total_cost: float = 0.0
    daily_cost: float = 0.0
    monthly_cost: float = 0.0
    cost_per_request: float = 0.0
    token_usage: int = 0
    request_count: int = 0
    average_tokens_per_request: float = 0.0

@dataclass
class OptimizationSuggestion:
    """优化建议"""
    type: str  # model_switch, prompt_optimization, caching, batching
    priority: str  # high, medium, low
    potential_savings: float
    description: str
    implementation: str
    impact: str

class CostOptimizer:
    """成本优化器"""
    
    def __init__(self):
        self.usage_history = []
        self.cost_thresholds = {
            'daily_warning': 10.0,
            'daily_limit': 50.0,
            'monthly_warning': 200.0,
            'monthly_limit': 1000.0
        }
        self.optimization_rules = self._init_optimization_rules()
        self.cache_hit_rate = 0.0
        self.model_costs = self._init_model_costs()
    
    def _init_model_costs(self) -> Dict[str, Dict]:
        """初始化模型成本数据"""
        return {
            "OpenAI GPT-4": {
                "input_cost": 0.03,
                "output_cost": 0.06,
                "context_limit": 128000
            },
            "Anthropic Claude": {
                "input_cost": 0.015,
                "output_cost": 0.075,
                "context_limit": 200000
            },
            "Google Gemini": {
                "input_cost": 0.001,
                "output_cost": 0.002,
                "context_limit": 32000
            },
            "DeepSeek": {
                "input_cost": 0.001,
                "output_cost": 0.002,
                "context_limit": 16000
            },
            "百度文心一言": {
                "input_cost": 0.008,
                "output_cost": 0.008,
                "context_limit": 8000
            },
            "阿里通义千问": {
                "input_cost": 0.008,
                "output_cost": 0.02,
                "context_limit": 32000
            },
            "智谱清言": {
                "input_cost": 0.01,
                "output_cost": 0.01,
                "context_limit": 128000
            }
        }
    
    def _init_optimization_rules(self) -> List[Dict]:
        """初始化优化规则"""
        return [
            {
                'name': 'high_cost_model_usage',
                'condition': lambda metrics: metrics.cost_per_request > 0.1,
                'suggestion_type': 'model_switch',
                'priority': 'high'
            },
            {
                'name': 'low_cache_hit_rate',
                'condition': lambda metrics: self.cache_hit_rate < 0.3,
                'suggestion_type': 'caching',
                'priority': 'medium'
            },
            {
                'name': 'large_context_usage',
                'condition': lambda metrics: metrics.average_tokens_per_request > 10000,
                'suggestion_type': 'prompt_optimization',
                'priority': 'high'
            },
            {
                'name': 'frequent_similar_requests',
                'condition': lambda metrics: self._detect_similar_requests(),
                'suggestion_type': 'batching',
                'priority': 'medium'
            }
        ]
    
    def record_usage(self, provider: str, input_tokens: int, output_tokens: int, 
                    request_type: str = "code_generation", metadata: Dict = None):
        """记录使用情况"""
        if metadata is None:
            metadata = {}
        
        # 计算成本
        model_cost = self.model_costs.get(provider, {})
        input_cost = (input_tokens / 1000) * model_cost.get('input_cost', 0)
        output_cost = (output_tokens / 1000) * model_cost.get('output_cost', 0)
        total_cost = input_cost + output_cost
        
        usage_record = {
            'timestamp': time.time(),
            'provider': provider,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'total_tokens': input_tokens + output_tokens,
            'cost': total_cost,
            'request_type': request_type,
            'metadata': metadata
        }
        
        self.usage_history.append(usage_record)
        
        # 保持历史记录在合理范围内
        if len(self.usage_history) > 10000:
            self.usage_history = self.usage_history[-5000:]
    
    def get_cost_metrics(self, days: int = 30) -> CostMetrics:
        """获取成本指标"""
        cutoff_time = time.time() - (days * 24 * 3600)
        recent_usage = [u for u in self.usage_history if u['timestamp'] > cutoff_time]
        
        if not recent_usage:
            return CostMetrics()
        
        total_cost = sum(u['cost'] for u in recent_usage)
        total_tokens = sum(u['total_tokens'] for u in recent_usage)
        request_count = len(recent_usage)
        
        # 计算日均成本
        daily_cost = total_cost / days if days > 0 else total_cost
        
        # 计算月均成本
        monthly_cost = daily_cost * 30
        
        return CostMetrics(
            total_cost=total_cost,
            daily_cost=daily_cost,
            monthly_cost=monthly_cost,
            cost_per_request=total_cost / request_count if request_count > 0 else 0,
            token_usage=total_tokens,
            request_count=request_count,
            average_tokens_per_request=total_tokens / request_count if request_count > 0 else 0
        )
    
    def analyze_cost_trends(self, days: int = 30) -> Dict[str, Any]:
        """分析成本趋势"""
        cutoff_time = time.time() - (days * 24 * 3600)
        recent_usage = [u for u in self.usage_history if u['timestamp'] > cutoff_time]
        
        if not recent_usage:
            return {'trend': 'no_data', 'analysis': '没有足够的数据进行分析'}
        
        # 按天分组
        daily_costs = {}
        for usage in recent_usage:
            date = datetime.fromtimestamp(usage['timestamp']).date()
            if date not in daily_costs:
                daily_costs[date] = 0
            daily_costs[date] += usage['cost']
        
        # 计算趋势
        dates = sorted(daily_costs.keys())
        costs = [daily_costs[date] for date in dates]
        
        if len(costs) < 2:
            return {'trend': 'insufficient_data', 'analysis': '数据不足以分析趋势'}
        
        # 简单的线性趋势分析
        recent_avg = sum(costs[-7:]) / min(7, len(costs))  # 最近7天平均
        earlier_avg = sum(costs[:-7]) / max(1, len(costs) - 7)  # 之前的平均
        
        if recent_avg > earlier_avg * 1.2:
            trend = 'increasing'
            analysis = f'成本呈上升趋势，最近平均每日${recent_avg:.3f}，比之前增加{((recent_avg/earlier_avg-1)*100):.1f}%'
        elif recent_avg < earlier_avg * 0.8:
            trend = 'decreasing'
            analysis = f'成本呈下降趋势，最近平均每日${recent_avg:.3f}，比之前减少{((1-recent_avg/earlier_avg)*100):.1f}%'
        else:
            trend = 'stable'
            analysis = f'成本相对稳定，最近平均每日${recent_avg:.3f}'
        
        return {
            'trend': trend,
            'analysis': analysis,
            'daily_costs': daily_costs,
            'recent_average': recent_avg,
            'earlier_average': earlier_avg
        }
    
    def get_optimization_suggestions(self, days: int = 30) -> List[OptimizationSuggestion]:
        """获取优化建议"""
        metrics = self.get_cost_metrics(days)
        suggestions = []
        
        # 应用优化规则
        for rule in self.optimization_rules:
            if rule['condition'](metrics):
                suggestion = self._generate_suggestion(rule, metrics)
                if suggestion:
                    suggestions.append(suggestion)
        
        # 添加基于使用模式的建议
        pattern_suggestions = self._analyze_usage_patterns(days)
        suggestions.extend(pattern_suggestions)
        
        # 按优先级和潜在节省排序
        suggestions.sort(key=lambda x: (
            {'high': 3, 'medium': 2, 'low': 1}[x.priority],
            x.potential_savings
        ), reverse=True)
        
        return suggestions
    
    def _generate_suggestion(self, rule: Dict, metrics: CostMetrics) -> Optional[OptimizationSuggestion]:
        """生成具体的优化建议"""
        suggestion_generators = {
            'model_switch': self._suggest_model_switch,
            'caching': self._suggest_caching,
            'prompt_optimization': self._suggest_prompt_optimization,
            'batching': self._suggest_batching
        }
        
        generator = suggestion_generators.get(rule['suggestion_type'])
        if generator:
            return generator(rule, metrics)
        
        return None
    
    def _suggest_model_switch(self, rule: Dict, metrics: CostMetrics) -> OptimizationSuggestion:
        """建议切换模型"""
        # 分析当前使用的模型
        recent_usage = self.usage_history[-100:]  # 最近100次请求
        model_usage = {}
        
        for usage in recent_usage:
            provider = usage['provider']
            if provider not in model_usage:
                model_usage[provider] = {'count': 0, 'cost': 0}
            model_usage[provider]['count'] += 1
            model_usage[provider]['cost'] += usage['cost']
        
        # 找出成本最高的模型
        if not model_usage:
            return None
        
        most_expensive = max(model_usage.items(), key=lambda x: x[1]['cost'])
        expensive_provider = most_expensive[0]
        expensive_cost = most_expensive[1]['cost']
        
        # 建议更便宜的替代方案
        cheaper_alternatives = []
        current_cost = self.model_costs.get(expensive_provider, {})
        
        for provider, cost_info in self.model_costs.items():
            if provider != expensive_provider:
                if (cost_info.get('input_cost', 0) < current_cost.get('input_cost', float('inf')) or
                    cost_info.get('output_cost', 0) < current_cost.get('output_cost', float('inf'))):
                    cheaper_alternatives.append(provider)
        
        if not cheaper_alternatives:
            return None
        
        best_alternative = min(cheaper_alternatives, 
                             key=lambda x: self.model_costs[x].get('input_cost', 0))
        
        # 计算潜在节省
        current_avg_cost = current_cost.get('input_cost', 0) + current_cost.get('output_cost', 0)
        alternative_avg_cost = (self.model_costs[best_alternative].get('input_cost', 0) + 
                              self.model_costs[best_alternative].get('output_cost', 0))
        
        potential_savings = (current_avg_cost - alternative_avg_cost) * metrics.average_tokens_per_request / 1000
        potential_savings *= metrics.request_count / 30  # 每日节省
        
        return OptimizationSuggestion(
            type='model_switch',
            priority='high',
            potential_savings=potential_savings,
            description=f'当前主要使用{expensive_provider}，成本较高',
            implementation=f'考虑切换到{best_alternative}，可节省约{((current_avg_cost-alternative_avg_cost)/current_avg_cost*100):.1f}%的成本',
            impact=f'预计每日可节省${potential_savings:.3f}'
        )
    
    def _suggest_caching(self, rule: Dict, metrics: CostMetrics) -> OptimizationSuggestion:
        """建议使用缓存"""
        # 分析重复请求
        recent_usage = self.usage_history[-200:]
        request_patterns = {}
        
        for usage in recent_usage:
            # 简化的请求模式识别
            pattern_key = f"{usage['request_type']}_{usage.get('metadata', {}).get('language', 'unknown')}"
            if pattern_key not in request_patterns:
                request_patterns[pattern_key] = []
            request_patterns[pattern_key].append(usage)
        
        # 找出重复度高的模式
        high_repeat_patterns = {k: v for k, v in request_patterns.items() if len(v) >= 5}
        
        if not high_repeat_patterns:
            return None
        
        # 计算缓存潜在节省
        total_repeated_cost = sum(sum(u['cost'] for u in usages) 
                                for usages in high_repeat_patterns.values())
        
        # 假设缓存可以避免70%的重复请求
        potential_savings = total_repeated_cost * 0.7 / 30  # 每日节省
        
        return OptimizationSuggestion(
            type='caching',
            priority='medium',
            potential_savings=potential_savings,
            description=f'检测到{len(high_repeat_patterns)}种高重复请求模式',
            implementation='实现智能缓存系统，缓存常见请求的结果',
            impact=f'预计每日可节省${potential_savings:.3f}，缓存命中率可达70%'
        )
    
    def _suggest_prompt_optimization(self, rule: Dict, metrics: CostMetrics) -> OptimizationSuggestion:
        """建议优化提示词"""
        if metrics.average_tokens_per_request <= 5000:
            return None
        
        # 分析token使用情况
        recent_usage = self.usage_history[-50:]
        large_requests = [u for u in recent_usage if u['total_tokens'] > 10000]
        
        if not large_requests:
            return None
        
        avg_large_request_cost = sum(u['cost'] for u in large_requests) / len(large_requests)
        
        # 假设优化提示词可以减少30%的token使用
        potential_savings = avg_large_request_cost * 0.3 * len(large_requests) / 30
        
        return OptimizationSuggestion(
            type='prompt_optimization',
            priority='high',
            potential_savings=potential_savings,
            description=f'平均每次请求使用{metrics.average_tokens_per_request:.0f}个token，较高',
            implementation='优化提示词结构，减少不必要的上下文，使用更精确的指令',
            impact=f'预计可减少30%的token使用，每日节省${potential_savings:.3f}'
        )
    
    def _suggest_batching(self, rule: Dict, metrics: CostMetrics) -> OptimizationSuggestion:
        """建议批处理"""
        # 分析请求频率
        recent_usage = self.usage_history[-100:]
        if len(recent_usage) < 20:
            return None
        
        # 按时间窗口分组
        time_windows = {}
        for usage in recent_usage:
            window = int(usage['timestamp'] // 300)  # 5分钟窗口
            if window not in time_windows:
                time_windows[window] = []
            time_windows[window].append(usage)
        
        # 找出高频时间窗口
        high_freq_windows = [w for w in time_windows.values() if len(w) >= 3]
        
        if not high_freq_windows:
            return None
        
        # 计算批处理潜在节省
        total_batchable_cost = sum(sum(u['cost'] for u in window) for window in high_freq_windows)
        potential_savings = total_batchable_cost * 0.2 / 30  # 假设批处理可节省20%
        
        return OptimizationSuggestion(
            type='batching',
            priority='medium',
            potential_savings=potential_savings,
            description=f'检测到{len(high_freq_windows)}个高频请求时间窗口',
            implementation='实现请求批处理，将短时间内的多个请求合并处理',
            impact=f'预计每日可节省${potential_savings:.3f}'
        )
    
    def _analyze_usage_patterns(self, days: int) -> List[OptimizationSuggestion]:
        """分析使用模式并生成建议"""
        suggestions = []
        cutoff_time = time.time() - (days * 24 * 3600)
        recent_usage = [u for u in self.usage_history if u['timestamp'] > cutoff_time]
        
        if not recent_usage:
            return suggestions
        
        # 分析时间模式
        hourly_usage = {}
        for usage in recent_usage:
            hour = datetime.fromtimestamp(usage['timestamp']).hour
            if hour not in hourly_usage:
                hourly_usage[hour] = []
            hourly_usage[hour].append(usage)
        
        # 找出使用高峰期
        peak_hours = sorted(hourly_usage.items(), key=lambda x: len(x[1]), reverse=True)[:3]
        
        if peak_hours and len(peak_hours[0][1]) > len(recent_usage) * 0.3:
            peak_cost = sum(u['cost'] for u in peak_hours[0][1])
            suggestions.append(OptimizationSuggestion(
                type='usage_scheduling',
                priority='low',
                potential_savings=peak_cost * 0.1 / 30,
                description=f'使用高峰期在{peak_hours[0][0]}点，占总使用量的{len(peak_hours[0][1])/len(recent_usage)*100:.1f}%',
                implementation='考虑将非紧急任务调度到低峰期执行',
                impact='可以获得更好的响应时间和潜在的成本优惠'
            ))
        
        # 分析模型使用分布
        model_distribution = {}
        for usage in recent_usage:
            provider = usage['provider']
            if provider not in model_distribution:
                model_distribution[provider] = {'count': 0, 'cost': 0}
            model_distribution[provider]['count'] += 1
            model_distribution[provider]['cost'] += usage['cost']
        
        # 检查是否过度依赖昂贵模型
        if len(model_distribution) > 1:
            sorted_models = sorted(model_distribution.items(), key=lambda x: x[1]['cost'], reverse=True)
            most_expensive = sorted_models[0]
            
            if most_expensive[1]['cost'] > sum(m[1]['cost'] for m in sorted_models[1:]):
                suggestions.append(OptimizationSuggestion(
                    type='model_diversification',
                    priority='medium',
                    potential_savings=most_expensive[1]['cost'] * 0.2 / 30,
                    description=f'{most_expensive[0]}占总成本的{most_expensive[1]["cost"]/sum(m[1]["cost"] for m in sorted_models)*100:.1f}%',
                    implementation='根据任务复杂度选择合适的模型，简单任务使用更便宜的模型',
                    impact='通过模型多样化可以显著降低平均成本'
                ))
        
        return suggestions
    
    def _detect_similar_requests(self) -> bool:
        """检测相似请求"""
        recent_usage = self.usage_history[-50:]
        if len(recent_usage) < 10:
            return False
        
        # 简化的相似性检测
        request_types = [u['request_type'] for u in recent_usage]
        type_counts = {}
        for req_type in request_types:
            type_counts[req_type] = type_counts.get(req_type, 0) + 1
        
        # 如果某种类型的请求占比超过50%，认为有相似请求
        max_count = max(type_counts.values())
        return max_count > len(recent_usage) * 0.5
    
    def set_cost_thresholds(self, thresholds: Dict[str, float]):
        """设置成本阈值"""
        self.cost_thresholds.update(thresholds)
    
    def check_cost_alerts(self) -> List[Dict[str, Any]]:
        """检查成本警报"""
        alerts = []
        metrics = self.get_cost_metrics(1)  # 今日成本
        monthly_metrics = self.get_cost_metrics(30)  # 月度成本
        
        # 检查日成本警报
        if metrics.daily_cost > self.cost_thresholds['daily_limit']:
            alerts.append({
                'type': 'daily_limit_exceeded',
                'severity': 'critical',
                'message': f'今日成本${metrics.daily_cost:.2f}已超过限额${self.cost_thresholds["daily_limit"]:.2f}',
                'action': '建议暂停非必要的API调用'
            })
        elif metrics.daily_cost > self.cost_thresholds['daily_warning']:
            alerts.append({
                'type': 'daily_warning',
                'severity': 'warning',
                'message': f'今日成本${metrics.daily_cost:.2f}接近警告线${self.cost_thresholds["daily_warning"]:.2f}',
                'action': '建议关注使用情况'
            })
        
        # 检查月成本警报
        if monthly_metrics.monthly_cost > self.cost_thresholds['monthly_limit']:
            alerts.append({
                'type': 'monthly_limit_exceeded',
                'severity': 'critical',
                'message': f'月度成本${monthly_metrics.monthly_cost:.2f}已超过限额${self.cost_thresholds["monthly_limit"]:.2f}',
                'action': '建议立即实施成本控制措施'
            })
        elif monthly_metrics.monthly_cost > self.cost_thresholds['monthly_warning']:
            alerts.append({
                'type': 'monthly_warning',
                'severity': 'warning',
                'message': f'月度成本${monthly_metrics.monthly_cost:.2f}接近警告线${self.cost_thresholds["monthly_warning"]:.2f}',
                'action': '建议优化使用策略'
            })
        
        return alerts
    
    def generate_cost_report(self, days: int = 30) -> str:
        """生成成本报告"""
        metrics = self.get_cost_metrics(days)
        trends = self.analyze_cost_trends(days)
        suggestions = self.get_optimization_suggestions(days)
        alerts = self.check_cost_alerts()
        
        report = []
        report.append(f"# AI API 成本分析报告 ({days}天)\\n")
        
        # 成本概览
        report.append("## 成本概览")
        report.append(f"- 总成本: ${metrics.total_cost:.3f}")
        report.append(f"- 日均成本: ${metrics.daily_cost:.3f}")
        report.append(f"- 月度预估: ${metrics.monthly_cost:.3f}")
        report.append(f"- 平均每次请求: ${metrics.cost_per_request:.4f}")
        report.append(f"- 总请求数: {metrics.request_count}")
        report.append(f"- Token使用量: {metrics.token_usage:,}")
        report.append("")
        
        # 成本趋势
        report.append("## 成本趋势")
        report.append(f"- 趋势: {trends['trend']}")
        report.append(f"- 分析: {trends['analysis']}")
        report.append("")
        
        # 成本警报
        if alerts:
            report.append("## ⚠️ 成本警报")
            for alert in alerts:
                severity_icon = "🔴" if alert['severity'] == 'critical' else "🟡"
                report.append(f"{severity_icon} {alert['message']}")
                report.append(f"   建议: {alert['action']}")
            report.append("")
        
        # 优化建议
        if suggestions:
            report.append("## 💡 优化建议")
            for i, suggestion in enumerate(suggestions[:5], 1):
                priority_icon = {"high": "🔥", "medium": "⚡", "low": "💡"}[suggestion.priority]
                report.append(f"### {i}. {priority_icon} {suggestion.type.replace('_', ' ').title()}")
                report.append(f"- 优先级: {suggestion.priority}")
                report.append(f"- 潜在节省: ${suggestion.potential_savings:.3f}/天")
                report.append(f"- 问题: {suggestion.description}")
                report.append(f"- 建议: {suggestion.implementation}")
                report.append(f"- 影响: {suggestion.impact}")
                report.append("")
        
        # 模型使用分析
        recent_usage = self.usage_history[-100:]
        if recent_usage:
            model_stats = {}
            for usage in recent_usage:
                provider = usage['provider']
                if provider not in model_stats:
                    model_stats[provider] = {'count': 0, 'cost': 0, 'tokens': 0}
                model_stats[provider]['count'] += 1
                model_stats[provider]['cost'] += usage['cost']
                model_stats[provider]['tokens'] += usage['total_tokens']
            
            report.append("## 模型使用统计")
            for provider, stats in sorted(model_stats.items(), key=lambda x: x[1]['cost'], reverse=True):
                usage_pct = stats['count'] / len(recent_usage) * 100
                cost_pct = stats['cost'] / sum(s['cost'] for s in model_stats.values()) * 100
                avg_cost = stats['cost'] / stats['count']
                report.append(f"- {provider}: {stats['count']}次 ({usage_pct:.1f}%), "
                            f"${stats['cost']:.3f} ({cost_pct:.1f}%), "
                            f"平均${avg_cost:.4f}/次")
            report.append("")
        
        return "\\n".join(report)
    
    def export_usage_data(self, days: int = 30) -> Dict[str, Any]:
        """导出使用数据"""
        cutoff_time = time.time() - (days * 24 * 3600)
        recent_usage = [u for u in self.usage_history if u['timestamp'] > cutoff_time]
        
        return {
            'export_time': datetime.now().isoformat(),
            'period_days': days,
            'total_records': len(recent_usage),
            'usage_data': recent_usage,
            'cost_metrics': asdict(self.get_cost_metrics(days)),
            'cost_thresholds': self.cost_thresholds,
            'optimization_suggestions': [asdict(s) for s in self.get_optimization_suggestions(days)]
        }