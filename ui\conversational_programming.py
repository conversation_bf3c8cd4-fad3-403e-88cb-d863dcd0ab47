# 对话式编程界面

import json
import time
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
                            QScrollArea, QLabel, QFrame, QSplitter, QComboBox, QCheckBox,
                            QSlider, QSpinBox, QTabWidget, QListWidget, QListWidgetItem,
                            QProgressBar, QGroupBox, QFormLayout, QLineEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette, QTextCursor, QPixmap, QIcon
from datetime import datetime

class ConversationMessage:
    """对话消息"""
    def __init__(self, role: str, content: str, timestamp: float = None, 
                 message_type: str = "text", metadata: Dict = None):
        self.role = role  # user, assistant, system
        self.content = content
        self.timestamp = timestamp or time.time()
        self.message_type = message_type  # text, code, error, suggestion
        self.metadata = metadata or {}

class CodeSuggestion:
    """代码建议"""
    def __init__(self, code: str, description: str, confidence: float, 
                 language: str = "python"):
        self.code = code
        self.description = description
        self.confidence = confidence
        self.language = language
        self.accepted = False

class ConversationalProgrammingWidget(QWidget):
    """对话式编程界面"""
    
    code_generated = pyqtSignal(str)
    suggestion_accepted = pyqtSignal(str, str)  # code, description
    
    def __init__(self):
        super().__init__()
        self.conversation_history = []
        self.current_context = {}
        self.suggestions = []
        self.auto_suggestions_enabled = True
        self.init_ui()
        
        # 定时器用于自动建议
        self.suggestion_timer = QTimer()
        self.suggestion_timer.timeout.connect(self.generate_auto_suggestions)
        self.suggestion_timer.setSingleShot(True)
    
    def init_ui(self):
        # 创建主布局
        main_layout = QHBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：对话区域
        conversation_widget = self.create_conversation_widget()
        splitter.addWidget(conversation_widget)
        
        # 右侧：辅助面板
        assistant_widget = self.create_assistant_widget()
        splitter.addWidget(assistant_widget)
        
        # 设置分割器比例
        splitter.setSizes([700, 300])
        
        main_layout.addWidget(splitter)
    
    def create_conversation_widget(self):
        """创建对话区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel('💬 对话式编程助手')
        title_label.setFont(QFont('SimHei', 14, QFont.Bold))
        layout.addWidget(title_label)
        
        # 对话显示区域
        self.conversation_display = QScrollArea()
        self.conversation_display.setWidgetResizable(True)
        self.conversation_display.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.conversation_display.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 对话内容容器
        self.conversation_container = QWidget()
        self.conversation_layout = QVBoxLayout(self.conversation_container)
        self.conversation_layout.setAlignment(Qt.AlignTop)
        self.conversation_display.setWidget(self.conversation_container)
        
        layout.addWidget(self.conversation_display)
        
        # 输入区域
        input_layout = QVBoxLayout()
        
        # 快捷操作按钮
        quick_actions_layout = QHBoxLayout()
        
        self.explain_btn = QPushButton('🔍 解释代码')
        self.explain_btn.clicked.connect(self.explain_code)
        
        self.optimize_btn = QPushButton('⚡ 优化代码')
        self.optimize_btn.clicked.connect(self.optimize_code)
        
        self.debug_btn = QPushButton('🐛 调试帮助')
        self.debug_btn.clicked.connect(self.debug_help)
        
        self.refactor_btn = QPushButton('🔧 重构建议')
        self.refactor_btn.clicked.connect(self.refactor_suggestions)
        
        quick_actions_layout.addWidget(self.explain_btn)
        quick_actions_layout.addWidget(self.optimize_btn)
        quick_actions_layout.addWidget(self.debug_btn)
        quick_actions_layout.addWidget(self.refactor_btn)
        quick_actions_layout.addStretch()
        
        input_layout.addLayout(quick_actions_layout)
        
        # 用户输入框
        self.user_input = QTextEdit()
        self.user_input.setMaximumHeight(100)
        self.user_input.setPlaceholderText('输入你的问题或需求...')
        self.user_input.textChanged.connect(self.on_input_changed)
        input_layout.addWidget(self.user_input)
        
        # 发送按钮和选项
        send_layout = QHBoxLayout()
        
        self.send_btn = QPushButton('发送 (Ctrl+Enter)')
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setDefault(True)
        
        self.clear_btn = QPushButton('清空对话')
        self.clear_btn.clicked.connect(self.clear_conversation)
        
        # 设置选项
        self.auto_suggest_cb = QCheckBox('自动建议')
        self.auto_suggest_cb.setChecked(True)
        self.auto_suggest_cb.toggled.connect(self.toggle_auto_suggestions)
        
        self.context_aware_cb = QCheckBox('上下文感知')
        self.context_aware_cb.setChecked(True)
        
        send_layout.addWidget(self.send_btn)
        send_layout.addWidget(self.clear_btn)
        send_layout.addStretch()
        send_layout.addWidget(self.auto_suggest_cb)
        send_layout.addWidget(self.context_aware_cb)
        
        input_layout.addLayout(send_layout)
        layout.addLayout(input_layout)
        
        return widget
    
    def create_assistant_widget(self):
        """创建辅助面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建选项卡
        tabs = QTabWidget()
        
        # 建议标签页
        suggestions_tab = self.create_suggestions_tab()
        tabs.addTab(suggestions_tab, '💡 建议')
        
        # 上下文标签页
        context_tab = self.create_context_tab()
        tabs.addTab(context_tab, '📋 上下文')
        
        # 历史标签页
        history_tab = self.create_history_tab()
        tabs.addTab(history_tab, '📚 历史')
        
        # 设置标签页
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, '⚙️ 设置')
        
        layout.addWidget(tabs)
        
        return widget
    
    def create_suggestions_tab(self):
        """创建建议标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 建议列表
        suggestions_label = QLabel('智能建议:')
        layout.addWidget(suggestions_label)
        
        self.suggestions_list = QListWidget()
        self.suggestions_list.itemDoubleClicked.connect(self.apply_suggestion)
        layout.addWidget(self.suggestions_list)
        
        # 建议控制按钮
        suggestion_controls = QHBoxLayout()
        
        self.refresh_suggestions_btn = QPushButton('刷新建议')
        self.refresh_suggestions_btn.clicked.connect(self.generate_auto_suggestions)
        
        self.apply_suggestion_btn = QPushButton('应用选中')
        self.apply_suggestion_btn.clicked.connect(self.apply_selected_suggestion)
        
        suggestion_controls.addWidget(self.refresh_suggestions_btn)
        suggestion_controls.addWidget(self.apply_suggestion_btn)
        
        layout.addLayout(suggestion_controls)
        
        # 代码片段库
        snippets_label = QLabel('代码片段:')
        layout.addWidget(snippets_label)
        
        self.snippets_list = QListWidget()
        self.load_code_snippets()
        self.snippets_list.itemDoubleClicked.connect(self.insert_snippet)
        layout.addWidget(self.snippets_list)
        
        return widget
    
    def create_context_tab(self):
        """创建上下文标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 当前上下文显示
        context_label = QLabel('当前上下文:')
        layout.addWidget(context_label)
        
        self.context_display = QTextEdit()
        self.context_display.setReadOnly(True)
        self.context_display.setMaximumHeight(150)
        layout.addWidget(self.context_display)
        
        # 上下文控制
        context_controls = QHBoxLayout()
        
        self.update_context_btn = QPushButton('更新上下文')
        self.update_context_btn.clicked.connect(self.update_context)
        
        self.clear_context_btn = QPushButton('清空上下文')
        self.clear_context_btn.clicked.connect(self.clear_context)
        
        context_controls.addWidget(self.update_context_btn)
        context_controls.addWidget(self.clear_context_btn)
        
        layout.addLayout(context_controls)
        
        # 相关文件
        files_label = QLabel('相关文件:')
        layout.addWidget(files_label)
        
        self.related_files_list = QListWidget()
        layout.addWidget(self.related_files_list)
        
        return widget
    
    def create_history_tab(self):
        """创建历史标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 对话历史搜索
        search_layout = QHBoxLayout()
        
        self.history_search = QLineEdit()
        self.history_search.setPlaceholderText('搜索历史对话...')
        self.history_search.textChanged.connect(self.search_history)
        
        search_layout.addWidget(self.history_search)
        
        layout.addLayout(search_layout)
        
        # 历史对话列表
        self.history_list = QListWidget()
        self.history_list.itemClicked.connect(self.load_history_item)
        layout.addWidget(self.history_list)
        
        # 历史控制按钮
        history_controls = QHBoxLayout()
        
        self.export_history_btn = QPushButton('导出历史')
        self.export_history_btn.clicked.connect(self.export_history)
        
        self.clear_history_btn = QPushButton('清空历史')
        self.clear_history_btn.clicked.connect(self.clear_history)
        
        history_controls.addWidget(self.export_history_btn)
        history_controls.addWidget(self.clear_history_btn)
        
        layout.addLayout(history_controls)
        
        return widget
    
    def create_settings_tab(self):
        """创建设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # AI模型设置
        model_group = QGroupBox('AI模型设置')
        model_layout = QFormLayout(model_group)
        
        self.model_selector = QComboBox()
        self.model_selector.addItems([
            'OpenAI GPT-4', 'Anthropic Claude', 'Google Gemini',
            'DeepSeek', '百度文心一言', '阿里通义千问'
        ])
        model_layout.addRow('首选模型:', self.model_selector)
        
        self.temperature_slider = QSlider(Qt.Horizontal)
        self.temperature_slider.setRange(0, 100)
        self.temperature_slider.setValue(70)
        self.temperature_label = QLabel('0.7')
        self.temperature_slider.valueChanged.connect(
            lambda v: self.temperature_label.setText(f'{v/100:.1f}')
        )
        temp_layout = QHBoxLayout()
        temp_layout.addWidget(self.temperature_slider)
        temp_layout.addWidget(self.temperature_label)
        model_layout.addRow('创造性:', temp_layout)
        
        layout.addWidget(model_group)
        
        # 对话设置
        conversation_group = QGroupBox('对话设置')
        conversation_layout = QFormLayout(conversation_group)
        
        self.max_history_spin = QSpinBox()
        self.max_history_spin.setRange(10, 1000)
        self.max_history_spin.setValue(100)
        conversation_layout.addRow('最大历史记录:', self.max_history_spin)
        
        self.auto_save_cb = QCheckBox('自动保存对话')
        self.auto_save_cb.setChecked(True)
        conversation_layout.addRow('', self.auto_save_cb)
        
        self.show_timestamps_cb = QCheckBox('显示时间戳')
        self.show_timestamps_cb.setChecked(False)
        conversation_layout.addRow('', self.show_timestamps_cb)
        
        layout.addWidget(conversation_group)
        
        # 代码设置
        code_group = QGroupBox('代码设置')
        code_layout = QFormLayout(code_group)
        
        self.default_language = QComboBox()
        self.default_language.addItems(['python', 'javascript', 'java', 'cpp', 'go'])
        code_layout.addRow('默认语言:', self.default_language)
        
        self.auto_format_cb = QCheckBox('自动格式化代码')
        self.auto_format_cb.setChecked(True)
        code_layout.addRow('', self.auto_format_cb)
        
        self.syntax_highlight_cb = QCheckBox('语法高亮')
        self.syntax_highlight_cb.setChecked(True)
        code_layout.addRow('', self.syntax_highlight_cb)
        
        layout.addWidget(code_group)
        
        layout.addStretch()
        
        return widget
    
    def add_message(self, message: ConversationMessage):
        """添加消息到对话"""
        self.conversation_history.append(message)
        
        # 创建消息widget
        message_widget = self.create_message_widget(message)
        self.conversation_layout.addWidget(message_widget)
        
        # 滚动到底部
        QTimer.singleShot(100, self.scroll_to_bottom)
        
        # 更新历史列表
        self.update_history_list()
    
    def create_message_widget(self, message: ConversationMessage) -> QWidget:
        """创建消息widget"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # 设置样式
        if message.role == 'user':
            widget.setStyleSheet("""
                QFrame {
                    background-color: #e3f2fd;
                    border: 1px solid #bbdefb;
                    border-radius: 8px;
                    margin: 5px;
                    padding: 5px;
                }
            """)
        else:
            widget.setStyleSheet("""
                QFrame {
                    background-color: #f5f5f5;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    margin: 5px;
                    padding: 5px;
                }
            """)
        
        # 消息头部
        header_layout = QHBoxLayout()
        
        role_label = QLabel(f"{'👤 用户' if message.role == 'user' else '🤖 助手'}")
        role_label.setFont(QFont('SimHei', 10, QFont.Bold))
        header_layout.addWidget(role_label)
        
        if self.show_timestamps_cb.isChecked():
            time_label = QLabel(datetime.fromtimestamp(message.timestamp).strftime('%H:%M:%S'))
            time_label.setStyleSheet('color: #666;')
            header_layout.addWidget(time_label)
        
        header_layout.addStretch()
        
        # 消息类型标识
        if message.message_type != 'text':
            type_label = QLabel(f"[{message.message_type.upper()}]")
            type_label.setStyleSheet('color: #ff9800; font-weight: bold;')
            header_layout.addWidget(type_label)
        
        layout.addLayout(header_layout)
        
        # 消息内容
        content_widget = QTextEdit()
        content_widget.setPlainText(message.content)
        content_widget.setReadOnly(True)
        content_widget.setMaximumHeight(200)
        
        # 根据消息类型设置样式
        if message.message_type == 'code':
            content_widget.setFont(QFont('Consolas', 10))
            content_widget.setStyleSheet('background-color: #263238; color: #ffffff;')
        
        layout.addWidget(content_widget)
        
        # 如果是代码消息，添加操作按钮
        if message.message_type == 'code' and message.role == 'assistant':
            button_layout = QHBoxLayout()
            
            copy_btn = QPushButton('📋 复制')
            copy_btn.clicked.connect(lambda: self.copy_code(message.content))
            
            apply_btn = QPushButton('✅ 应用')
            apply_btn.clicked.connect(lambda: self.apply_code(message.content))
            
            explain_btn = QPushButton('❓ 解释')
            explain_btn.clicked.connect(lambda: self.explain_generated_code(message.content))
            
            button_layout.addWidget(copy_btn)
            button_layout.addWidget(apply_btn)
            button_layout.addWidget(explain_btn)
            button_layout.addStretch()
            
            layout.addLayout(button_layout)
        
        return widget
    
    def send_message(self):
        """发送消息"""
        user_text = self.user_input.toPlainText().strip()
        if not user_text:
            return
        
        # 添加用户消息
        user_message = ConversationMessage('user', user_text)
        self.add_message(user_message)
        
        # 清空输入框
        self.user_input.clear()
        
        # 处理用户请求
        self.process_user_request(user_text)
    
    def process_user_request(self, request: str):
        """处理用户请求"""
        # 分析请求类型
        request_type = self.analyze_request_type(request)
        
        # 根据请求类型生成响应
        if request_type == 'code_generation':
            self.generate_code_response(request)
        elif request_type == 'explanation':
            self.generate_explanation_response(request)
        elif request_type == 'debugging':
            self.generate_debugging_response(request)
        elif request_type == 'optimization':
            self.generate_optimization_response(request)
        else:
            self.generate_general_response(request)
    
    def analyze_request_type(self, request: str) -> str:
        """分析请求类型"""
        request_lower = request.lower()
        
        code_keywords = ['写', '生成', '创建', '实现', '编写', 'write', 'generate', 'create', 'implement']
        explain_keywords = ['解释', '说明', '什么', '如何', 'explain', 'what', 'how', 'why']
        debug_keywords = ['调试', '错误', '问题', '修复', 'debug', 'error', 'fix', 'bug']
        optimize_keywords = ['优化', '改进', '性能', '效率', 'optimize', 'improve', 'performance']
        
        if any(keyword in request_lower for keyword in code_keywords):
            return 'code_generation'
        elif any(keyword in request_lower for keyword in explain_keywords):
            return 'explanation'
        elif any(keyword in request_lower for keyword in debug_keywords):
            return 'debugging'
        elif any(keyword in request_lower for keyword in optimize_keywords):
            return 'optimization'
        else:
            return 'general'
    
    def generate_code_response(self, request: str):
        """生成代码响应"""
        # 这里应该调用AI API生成代码
        # 暂时使用模拟响应
        
        # 添加思考消息
        thinking_message = ConversationMessage('assistant', '正在分析您的需求并生成代码...', message_type='system')
        self.add_message(thinking_message)
        
        # 模拟代码生成
        QTimer.singleShot(2000, lambda: self.show_generated_code(request))
    
    def show_generated_code(self, request: str):
        """显示生成的代码"""
        # 模拟生成的代码
        sample_code = f'''# 根据您的需求生成的代码
def example_function():
    """
    这是根据请求 "{request[:50]}..." 生成的示例函数
    """
    print("Hello, World!")
    return True

# 使用示例
if __name__ == "__main__":
    result = example_function()
    print(f"执行结果: {result}")
'''
        
        code_message = ConversationMessage('assistant', sample_code, message_type='code')
        self.add_message(code_message)
        
        # 添加解释消息
        explanation = f"我为您生成了一个示例函数来满足您的需求。这个代码包含了基本的结构和注释。您可以点击上方的按钮来复制、应用或获取更详细的解释。"
        explanation_message = ConversationMessage('assistant', explanation)
        self.add_message(explanation_message)
        
        # 发出代码生成信号
        self.code_generated.emit(sample_code)
    
    def generate_explanation_response(self, request: str):
        """生成解释响应"""
        explanation = f"关于您的问题 \"{request}\"，我来为您详细解释：\n\n这是一个很好的问题。根据上下文，我建议您考虑以下几个方面：\n\n1. 首先理解问题的核心\n2. 分析可能的解决方案\n3. 选择最适合的方法\n4. 实施并测试\n\n如果您需要更具体的代码示例，请告诉我！"
        
        response_message = ConversationMessage('assistant', explanation)
        self.add_message(response_message)
    
    def generate_debugging_response(self, request: str):
        """生成调试响应"""
        debug_help = f"关于您遇到的问题，让我帮您分析一下：\n\n🔍 问题分析：\n{request}\n\n💡 可能的原因：\n1. 语法错误\n2. 逻辑错误\n3. 环境配置问题\n\n🛠️ 建议的解决步骤：\n1. 检查错误信息\n2. 逐步调试\n3. 查看相关文档\n\n如果您能提供具体的错误信息或代码，我可以给出更精确的帮助！"
        
        response_message = ConversationMessage('assistant', debug_help, message_type='debugging')
        self.add_message(response_message)
    
    def generate_optimization_response(self, request: str):
        """生成优化响应"""
        optimization = f"关于代码优化，我为您提供以下建议：\n\n⚡ 性能优化方向：\n1. 算法复杂度优化\n2. 内存使用优化\n3. I/O操作优化\n\n📊 具体建议：\n- 使用更高效的数据结构\n- 减少不必要的循环\n- 缓存重复计算的结果\n- 使用生成器代替列表（适当时）\n\n如果您能分享具体的代码，我可以提供更针对性的优化建议！"
        
        response_message = ConversationMessage('assistant', optimization, message_type='optimization')
        self.add_message(response_message)
    
    def generate_general_response(self, request: str):
        """生成通用响应"""
        response = f"我理解您的问题。让我来帮助您：\n\n{request}\n\n这是一个很有趣的话题。根据我的理解，我建议您可以从以下几个角度来思考：\n\n1. 明确目标和需求\n2. 分析现有资源和限制\n3. 制定实施计划\n4. 逐步执行和调整\n\n如果您需要更具体的帮助，请告诉我更多细节！"
        
        response_message = ConversationMessage('assistant', response)
        self.add_message(response_message)
    
    def explain_code(self):
        """解释代码功能"""
        self.user_input.setPlainText("请解释一下当前代码的功能和工作原理")
        self.send_message()
    
    def optimize_code(self):
        """优化代码建议"""
        self.user_input.setPlainText("请帮我优化当前代码的性能和结构")
        self.send_message()
    
    def debug_help(self):
        """调试帮助"""
        self.user_input.setPlainText("我的代码有问题，请帮我分析和调试")
        self.send_message()
    
    def refactor_suggestions(self):
        """重构建议"""
        self.user_input.setPlainText("请给出代码重构的建议")
        self.send_message()
    
    def on_input_changed(self):
        """输入变化时的处理"""
        if self.auto_suggestions_enabled:
            # 延迟生成建议，避免频繁触发
            self.suggestion_timer.stop()
            self.suggestion_timer.start(1000)  # 1秒后生成建议
    
    def generate_auto_suggestions(self):
        """生成自动建议"""
        current_input = self.user_input.toPlainText().strip()
        if not current_input:
            return
        
        # 清空现有建议
        self.suggestions_list.clear()
        
        # 生成建议（这里是模拟的建议）
        suggestions = [
            "生成一个Python函数来处理这个需求",
            "创建一个类来封装相关功能",
            "添加错误处理和异常捕获",
            "优化算法复杂度",
            "添加单元测试"
        ]
        
        for suggestion in suggestions:
            item = QListWidgetItem(f"💡 {suggestion}")
            self.suggestions_list.addItem(item)
    
    def apply_suggestion(self, item):
        """应用建议"""
        suggestion_text = item.text().replace("💡 ", "")
        self.user_input.setPlainText(suggestion_text)
    
    def apply_selected_suggestion(self):
        """应用选中的建议"""
        current_item = self.suggestions_list.currentItem()
        if current_item:
            self.apply_suggestion(current_item)
    
    def load_code_snippets(self):
        """加载代码片段"""
        snippets = [
            ("Python函数模板", "def function_name():\\n    pass"),
            ("Python类模板", "class ClassName:\\n    def __init__(self):\\n        pass"),
            ("错误处理", "try:\\n    # code here\\n    pass\\nexcept Exception as e:\\n    print(f'Error: {e}')"),
            ("文件读取", "with open('filename.txt', 'r') as f:\\n    content = f.read()"),
            ("列表推导式", "[item for item in iterable if condition]")
        ]
        
        for name, code in snippets:
            item = QListWidgetItem(f"📝 {name}")
            item.setData(Qt.UserRole, code)
            self.snippets_list.addItem(item)
    
    def insert_snippet(self, item):
        """插入代码片段"""
        code = item.data(Qt.UserRole)
        if code:
            current_text = self.user_input.toPlainText()
            if current_text:
                new_text = f"{current_text}\\n\\n{code}"
            else:
                new_text = code
            self.user_input.setPlainText(new_text)
    
    def copy_code(self, code: str):
        """复制代码到剪贴板"""
        from PyQt5.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(code)
        
        # 显示提示
        self.add_message(ConversationMessage('system', '代码已复制到剪贴板', message_type='system'))
    
    def apply_code(self, code: str):
        """应用代码"""
        self.code_generated.emit(code)
        self.suggestion_accepted.emit(code, "用户应用了生成的代码")
    
    def explain_generated_code(self, code: str):
        """解释生成的代码"""
        self.user_input.setPlainText(f"请详细解释这段代码的功能：\\n\\n{code}")
        self.send_message()
    
    def update_context(self):
        """更新上下文"""
        # 这里应该从主应用获取当前上下文
        context_info = {
            'current_file': 'main.py',
            'language': 'python',
            'project_type': 'desktop_app',
            'recent_changes': ['添加了新函数', '修复了bug']
        }
        
        self.current_context = context_info
        self.display_context()
    
    def display_context(self):
        """显示上下文信息"""
        if not self.current_context:
            self.context_display.setText("暂无上下文信息")
            return
        
        context_text = []
        for key, value in self.current_context.items():
            if isinstance(value, list):
                value_str = ', '.join(value)
            else:
                value_str = str(value)
            context_text.append(f"{key}: {value_str}")
        
        self.context_display.setText('\\n'.join(context_text))
    
    def clear_context(self):
        """清空上下文"""
        self.current_context = {}
        self.context_display.clear()
    
    def update_history_list(self):
        """更新历史列表"""
        self.history_list.clear()
        
        # 按时间分组显示最近的对话
        recent_conversations = self.conversation_history[-20:]  # 最近20条
        
        for i, message in enumerate(recent_conversations):
            if message.role == 'user':
                preview = message.content[:50] + "..." if len(message.content) > 50 else message.content
                time_str = datetime.fromtimestamp(message.timestamp).strftime('%H:%M')
                item_text = f"[{time_str}] {preview}"
                
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, i)
                self.history_list.addItem(item)
    
    def search_history(self, query: str):
        """搜索历史对话"""
        if not query:
            self.update_history_list()
            return
        
        self.history_list.clear()
        
        for i, message in enumerate(self.conversation_history):
            if query.lower() in message.content.lower():
                preview = message.content[:50] + "..." if len(message.content) > 50 else message.content
                time_str = datetime.fromtimestamp(message.timestamp).strftime('%m-%d %H:%M')
                item_text = f"[{time_str}] {preview}"
                
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, i)
                self.history_list.addItem(item)
    
    def load_history_item(self, item):
        """加载历史项目"""
        index = item.data(Qt.UserRole)
        if index is not None and 0 <= index < len(self.conversation_history):
            message = self.conversation_history[index]
            self.user_input.setPlainText(message.content)
    
    def export_history(self):
        """导出对话历史"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, '导出对话历史', 'conversation_history.json', 'JSON文件 (*.json)'
        )
        
        if file_path:
            try:
                history_data = []
                for message in self.conversation_history:
                    history_data.append({
                        'role': message.role,
                        'content': message.content,
                        'timestamp': message.timestamp,
                        'message_type': message.message_type,
                        'metadata': message.metadata
                    })
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(history_data, f, ensure_ascii=False, indent=2)
                
                self.add_message(ConversationMessage('system', f'对话历史已导出到: {file_path}', message_type='system'))
                
            except Exception as e:
                self.add_message(ConversationMessage('system', f'导出失败: {str(e)}', message_type='error'))
    
    def clear_history(self):
        """清空历史"""
        from PyQt5.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self, '确认', '确定要清空所有对话历史吗？',
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.conversation_history.clear()
            self.history_list.clear()
            self.add_message(ConversationMessage('system', '对话历史已清空', message_type='system'))
    
    def clear_conversation(self):
        """清空当前对话"""
        # 清空对话显示
        for i in reversed(range(self.conversation_layout.count())):
            child = self.conversation_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 清空对话历史
        self.conversation_history.clear()
        
        # 添加欢迎消息
        welcome_message = ConversationMessage(
            'assistant', 
            '👋 您好！我是您的AI编程助手。我可以帮您：\\n\\n• 生成和解释代码\\n• 调试和优化程序\\n• 回答编程问题\\n• 提供最佳实践建议\\n\\n请告诉我您需要什么帮助！'
        )
        self.add_message(welcome_message)
    
    def toggle_auto_suggestions(self, enabled: bool):
        """切换自动建议"""
        self.auto_suggestions_enabled = enabled
        if not enabled:
            self.suggestions_list.clear()
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.conversation_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
            self.send_message()
        else:
            super().keyPressEvent(event)