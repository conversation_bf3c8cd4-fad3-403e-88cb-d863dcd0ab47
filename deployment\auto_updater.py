# 自动更新机制
import os
import json
import requests
import hashlib
import subprocess
import tempfile
import shutil
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import zipfile
import tarfile

class UpdateChannel(Enum):
    STABLE = "stable"
    BETA = "beta"
    ALPHA = "alpha"
    NIGHTLY = "nightly"

class UpdateStatus(Enum):
    CHECKING = "checking"
    AVAILABLE = "available"
    DOWNLOADING = "downloading"
    INSTALLING = "installing"
    COMPLETED = "completed"
    FAILED = "failed"
    UP_TO_DATE = "up_to_date"

@dataclass
class UpdateInfo:
    """更新信息"""
    version: str
    channel: UpdateChannel
    release_date: str
    download_url: str
    file_size: int
    checksum: str
    changelog: str
    critical: bool = False
    auto_restart: bool = True

@dataclass
class UpdateProgress:
    """更新进度"""
    status: UpdateStatus
    progress: float  # 0-100
    message: str
    error: str = ""

class AutoUpdater:
    """自动更新管理器"""
    
    def __init__(self, app_name: str = "AI Code Generator", current_version: str = "1.0.0"):
        self.app_name = app_name
        self.current_version = current_version
        self.config = self._load_config()
        self.update_callbacks: List[Callable[[UpdateProgress], None]] = []
        self.is_checking = False
        self.is_updating = False
        
    def _load_config(self) -> Dict:
        """加载更新配置"""
        config_file = os.path.join(os.getcwd(), 'update_config.json')
        default_config = {
            'update_server': {
                'base_url': 'https://api.github.com/repos/ai-code-generator/releases',
                'api_key': '',  # GitHub API token (可选)
                'timeout': 30
            },
            'update_settings': {
                'channel': 'stable',
                'auto_check': True,
                'check_interval': 3600,  # 1小时
                'auto_download': True,
                'auto_install': False,
                'backup_before_update': True,
                'rollback_enabled': True
            },
            'download_settings': {
                'chunk_size': 8192,
                'max_retries': 3,
                'retry_delay': 5,
                'verify_checksum': True
            },
            'backup_settings': {
                'max_backups': 5,
                'backup_directory': 'backups',
                'compress_backups': True
            },
            'notification_settings': {
                'show_notifications': True,
                'sound_enabled': True,
                'email_notifications': False,
                'webhook_url': ''
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self._merge_config(default_config, user_config)
        
        return default_config
    
    def _merge_config(self, default: Dict, user: Dict):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def add_update_callback(self, callback: Callable[[UpdateProgress], None]):
        """添加更新进度回调"""
        self.update_callbacks.append(callback)
    
    def remove_update_callback(self, callback: Callable[[UpdateProgress], None]):
        """移除更新进度回调"""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
    
    def _notify_progress(self, progress: UpdateProgress):
        """通知更新进度"""
        for callback in self.update_callbacks:
            try:
                callback(progress)
            except Exception as e:
                print(f"更新回调执行失败: {e}")
    
    def check_for_updates(self, force: bool = False) -> Optional[UpdateInfo]:
        """检查更新"""
        if self.is_checking and not force:
            return None
        
        self.is_checking = True
        self._notify_progress(UpdateProgress(
            status=UpdateStatus.CHECKING,
            progress=0,
            message="正在检查更新..."
        ))
        
        try:
            # 获取最新版本信息
            update_info = self._fetch_latest_version()
            
            if update_info and self._is_newer_version(update_info.version):
                self._notify_progress(UpdateProgress(
                    status=UpdateStatus.AVAILABLE,
                    progress=100,
                    message=f"发现新版本: {update_info.version}"
                ))
                
                # 如果启用自动下载
                if self.config['update_settings']['auto_download']:
                    threading.Thread(target=self._download_update, args=(update_info,)).start()
                
                return update_info
            else:
                self._notify_progress(UpdateProgress(
                    status=UpdateStatus.UP_TO_DATE,
                    progress=100,
                    message="已是最新版本"
                ))
                return None
                
        except Exception as e:
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.FAILED,
                progress=0,
                message="检查更新失败",
                error=str(e)
            ))
            return None
        finally:
            self.is_checking = False
    
    def _fetch_latest_version(self) -> Optional[UpdateInfo]:
        """获取最新版本信息"""
        server_config = self.config['update_server']
        headers = {'User-Agent': f'{self.app_name}/{self.current_version}'}
        
        if server_config.get('api_key'):
            headers['Authorization'] = f"token {server_config['api_key']}"
        
        try:
            response = requests.get(
                f"{server_config['base_url']}/latest",
                headers=headers,
                timeout=server_config['timeout']
            )
            response.raise_for_status()
            
            release_data = response.json()
            
            # 解析GitHub Release数据
            return UpdateInfo(
                version=release_data['tag_name'].lstrip('v'),
                channel=UpdateChannel.STABLE,
                release_date=release_data['published_at'],
                download_url=release_data['assets'][0]['browser_download_url'] if release_data['assets'] else '',
                file_size=release_data['assets'][0]['size'] if release_data['assets'] else 0,
                checksum='',  # GitHub不提供，需要从release notes解析
                changelog=release_data['body'],
                critical=self._is_critical_update(release_data['body'])
            )
            
        except Exception as e:
            print(f"获取版本信息失败: {e}")
            return None
    
    def _is_newer_version(self, version: str) -> bool:
        """检查是否为更新版本"""
        def version_tuple(v):
            return tuple(map(int, (v.split("."))))
        
        try:
            return version_tuple(version) > version_tuple(self.current_version)
        except:
            return False
    
    def _is_critical_update(self, changelog: str) -> bool:
        """检查是否为关键更新"""
        critical_keywords = ['security', 'critical', 'urgent', 'hotfix', '安全', '紧急', '关键']
        changelog_lower = changelog.lower()
        return any(keyword in changelog_lower for keyword in critical_keywords)
    
    def _download_update(self, update_info: UpdateInfo):
        """下载更新"""
        if self.is_updating:
            return
        
        self.is_updating = True
        download_config = self.config['download_settings']
        
        try:
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.DOWNLOADING,
                progress=0,
                message=f"正在下载 {update_info.version}..."
            ))
            
            # 创建临时下载目录
            temp_dir = tempfile.mkdtemp(prefix='update_')
            download_path = os.path.join(temp_dir, f"update_{update_info.version}.zip")
            
            # 下载文件
            response = requests.get(update_info.download_url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(download_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=download_config['chunk_size']):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            self._notify_progress(UpdateProgress(
                                status=UpdateStatus.DOWNLOADING,
                                progress=progress,
                                message=f"下载中... {progress:.1f}%"
                            ))
            
            # 验证校验和
            if download_config['verify_checksum'] and update_info.checksum:
                if not self._verify_checksum(download_path, update_info.checksum):
                    raise Exception("文件校验失败")
            
            # 如果启用自动安装
            if self.config['update_settings']['auto_install']:
                self._install_update(download_path, update_info)
            else:
                self._notify_progress(UpdateProgress(
                    status=UpdateStatus.COMPLETED,
                    progress=100,
                    message="下载完成，等待安装"
                ))
                
        except Exception as e:
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.FAILED,
                progress=0,
                message="下载失败",
                error=str(e)
            ))
        finally:
            self.is_updating = False
    
    def _verify_checksum(self, file_path: str, expected_checksum: str) -> bool:
        """验证文件校验和"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            return sha256_hash.hexdigest() == expected_checksum
        except:
            return False
    
    def _install_update(self, update_file: str, update_info: UpdateInfo):
        """安装更新"""
        try:
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.INSTALLING,
                progress=0,
                message="正在安装更新..."
            ))
            
            # 创建备份
            if self.config['update_settings']['backup_before_update']:
                self._create_backup()
            
            # 解压更新文件
            extract_dir = tempfile.mkdtemp(prefix='extract_')
            
            if update_file.endswith('.zip'):
                with zipfile.ZipFile(update_file, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif update_file.endswith('.tar.gz'):
                with tarfile.open(update_file, 'r:gz') as tar_ref:
                    tar_ref.extractall(extract_dir)
            
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.INSTALLING,
                progress=50,
                message="正在替换文件..."
            ))
            
            # 替换应用文件
            self._replace_application_files(extract_dir)
            
            # 更新版本信息
            self._update_version_info(update_info.version)
            
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.COMPLETED,
                progress=100,
                message=f"更新完成: {update_info.version}"
            ))
            
            # 发送通知
            self._send_notification(f"应用已更新到版本 {update_info.version}")
            
            # 如果需要自动重启
            if update_info.auto_restart:
                self._schedule_restart()
                
        except Exception as e:
            self._notify_progress(UpdateProgress(
                status=UpdateStatus.FAILED,
                progress=0,
                message="安装失败",
                error=str(e)
            ))
            
            # 尝试回滚
            if self.config['update_settings']['rollback_enabled']:
                self._rollback_update()
    
    def _create_backup(self):
        """创建备份"""
        backup_config = self.config['backup_settings']
        backup_dir = backup_config['backup_directory']
        os.makedirs(backup_dir, exist_ok=True)
        
        # 清理旧备份
        self._cleanup_old_backups()
        
        # 创建新备份
        backup_name = f"backup_{self.current_version}_{int(time.time())}"
        backup_path = os.path.join(backup_dir, backup_name)
        
        if backup_config['compress_backups']:
            backup_path += '.tar.gz'
            with tarfile.open(backup_path, 'w:gz') as tar:
                tar.add('.', arcname=backup_name, exclude=lambda x: x.startswith('./backups'))
        else:
            shutil.copytree('.', backup_path, ignore=shutil.ignore_patterns('backups'))
        
        print(f"备份创建完成: {backup_path}")
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        backup_config = self.config['backup_settings']
        backup_dir = backup_config['backup_directory']
        max_backups = backup_config['max_backups']
        
        if not os.path.exists(backup_dir):
            return
        
        # 获取所有备份文件
        backups = []
        for item in os.listdir(backup_dir):
            item_path = os.path.join(backup_dir, item)
            if os.path.isfile(item_path) or os.path.isdir(item_path):
                backups.append((item_path, os.path.getctime(item_path)))
        
        # 按创建时间排序
        backups.sort(key=lambda x: x[1], reverse=True)
        
        # 删除多余的备份
        for backup_path, _ in backups[max_backups:]:
            try:
                if os.path.isfile(backup_path):
                    os.remove(backup_path)
                else:
                    shutil.rmtree(backup_path)
                print(f"删除旧备份: {backup_path}")
            except Exception as e:
                print(f"删除备份失败: {e}")
    
    def _replace_application_files(self, extract_dir: str):
        """替换应用文件"""
        # 获取提取目录中的文件
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                src_path = os.path.join(root, file)
                rel_path = os.path.relpath(src_path, extract_dir)
                dst_path = os.path.join('.', rel_path)
                
                # 创建目标目录
                os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                
                # 替换文件
                shutil.copy2(src_path, dst_path)
    
    def _update_version_info(self, new_version: str):
        """更新版本信息"""
        version_file = 'version.json'
        version_info = {
            'version': new_version,
            'updated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
            'previous_version': self.current_version
        }
        
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        
        self.current_version = new_version
    
    def _rollback_update(self):
        """回滚更新"""
        try:
            backup_dir = self.config['backup_settings']['backup_directory']
            if not os.path.exists(backup_dir):
                return
            
            # 找到最新的备份
            backups = []
            for item in os.listdir(backup_dir):
                item_path = os.path.join(backup_dir, item)
                if os.path.exists(item_path):
                    backups.append((item_path, os.path.getctime(item_path)))
            
            if not backups:
                return
            
            # 选择最新备份
            latest_backup = max(backups, key=lambda x: x[1])[0]
            
            # 恢复备份
            if latest_backup.endswith('.tar.gz'):
                with tarfile.open(latest_backup, 'r:gz') as tar:
                    tar.extractall('.')
            else:
                # 清空当前目录（除了备份）
                for item in os.listdir('.'):
                    if item != 'backups':
                        if os.path.isfile(item):
                            os.remove(item)
                        else:
                            shutil.rmtree(item)
                
                # 复制备份文件
                for item in os.listdir(latest_backup):
                    src = os.path.join(latest_backup, item)
                    dst = os.path.join('.', item)
                    if os.path.isfile(src):
                        shutil.copy2(src, dst)
                    else:
                        shutil.copytree(src, dst)
            
            print("回滚完成")
            
        except Exception as e:
            print(f"回滚失败: {e}")
    
    def _send_notification(self, message: str):
        """发送通知"""
        notification_config = self.config['notification_settings']
        
        if not notification_config['show_notifications']:
            return
        
        try:
            # 系统通知
            if os.name == 'nt':  # Windows
                import win32gui
                import win32con
                win32gui.MessageBox(0, message, self.app_name, win32con.MB_OK)
            elif os.name == 'posix':  # Linux/macOS
                subprocess.run(['notify-send', self.app_name, message])
            
            # Webhook通知
            webhook_url = notification_config.get('webhook_url')
            if webhook_url:
                requests.post(webhook_url, json={'text': message})
                
        except Exception as e:
            print(f"发送通知失败: {e}")
    
    def _schedule_restart(self):
        """计划重启"""
        print("应用将在5秒后重启...")
        time.sleep(5)
        
        # 重启应用
        if os.name == 'nt':  # Windows
            subprocess.Popen([sys.executable] + sys.argv)
        else:  # Linux/macOS
            os.execv(sys.executable, [sys.executable] + sys.argv)
    
    def start_auto_check(self):
        """启动自动检查"""
        if not self.config['update_settings']['auto_check']:
            return
        
        def check_loop():
            while True:
                try:
                    self.check_for_updates()
                    time.sleep(self.config['update_settings']['check_interval'])
                except Exception as e:
                    print(f"自动检查更新失败: {e}")
                    time.sleep(300)  # 出错后5分钟重试
        
        thread = threading.Thread(target=check_loop, daemon=True)
        thread.start()
    
    def manual_update(self, update_file: str) -> bool:
        """手动更新"""
        try:
            # 验证更新文件
            if not os.path.exists(update_file):
                raise Exception("更新文件不存在")
            
            # 创建更新信息
            update_info = UpdateInfo(
                version="manual",
                channel=UpdateChannel.STABLE,
                release_date=time.strftime('%Y-%m-%d'),
                download_url="",
                file_size=os.path.getsize(update_file),
                checksum="",
                changelog="手动更新",
                auto_restart=False
            )
            
            # 安装更新
            self._install_update(update_file, update_info)
            return True
            
        except Exception as e:
            print(f"手动更新失败: {e}")
            return False
    
    def get_update_history(self) -> List[Dict]:
        """获取更新历史"""
        history_file = 'update_history.json'
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    
    def save_update_history(self, update_info: UpdateInfo):
        """保存更新历史"""
        history = self.get_update_history()
        history.append({
            'version': update_info.version,
            'channel': update_info.channel.value,
            'updated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
            'changelog': update_info.changelog
        })
        
        # 只保留最近50条记录
        history = history[-50:]
        
        with open('update_history.json', 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2, ensure_ascii=False)