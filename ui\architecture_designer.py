# 架构设计器模块

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QTextEdit, QComboBox, QListWidget, QListWidgetItem, 
                            QInputDialog, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

class ArchitectureDesignerWidget(QWidget):
    architecture_changed = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.architecture = {
            'name': '默认架构',
            'modules': [],
            'dependencies': []
        }
        self.init_ui()

    def init_ui(self):
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题
        title_label = QLabel('架构设计器')
        title_label.setFont(QFont('SimHei', 14, QFont.Bold))
        main_layout.addWidget(title_label)

        # 创建架构名称输入
        name_layout = QHBoxLayout()
        name_label = QLabel('架构名称:')
        self.name_edit = QTextEdit()
        self.name_edit.setMaximumHeight(40)
        self.name_edit.setText('默认架构')
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)
        main_layout.addLayout(name_layout)

        # 创建模块列表
        module_layout = QHBoxLayout()
        module_label = QLabel('模块:')
        self.module_list = QListWidget()
        module_layout.addWidget(module_label)
        module_layout.addWidget(self.module_list)

        # 模块操作按钮
        module_btn_layout = QVBoxLayout()
        add_module_btn = QPushButton('添加模块')
        add_module_btn.clicked.connect(self.add_module)
        remove_module_btn = QPushButton('删除模块')
        remove_module_btn.clicked.connect(self.remove_module)
        module_btn_layout.addWidget(add_module_btn)
        module_btn_layout.addWidget(remove_module_btn)
        module_btn_layout.addStretch()
        module_layout.addLayout(module_btn_layout)
        main_layout.addLayout(module_layout)

        # 创建依赖列表
        dependency_layout = QHBoxLayout()
        dependency_label = QLabel('依赖:')
        self.dependency_list = QListWidget()
        dependency_layout.addWidget(dependency_label)
        dependency_layout.addWidget(self.dependency_list)

        # 依赖操作按钮
        dependency_btn_layout = QVBoxLayout()
        add_dependency_btn = QPushButton('添加依赖')
        add_dependency_btn.clicked.connect(self.add_dependency)
        remove_dependency_btn = QPushButton('删除依赖')
        remove_dependency_btn.clicked.connect(self.remove_dependency)
        dependency_btn_layout.addWidget(add_dependency_btn)
        dependency_btn_layout.addWidget(remove_dependency_btn)
        dependency_btn_layout.addStretch()
        dependency_layout.addLayout(dependency_btn_layout)
        main_layout.addLayout(dependency_layout)

        # 创建确认按钮
        confirm_btn = QPushButton('确认架构')
        confirm_btn.clicked.connect(self.confirm_architecture)
        main_layout.addWidget(confirm_btn)

    def add_module(self):
        """添加模块"""
        module_name, ok = QInputDialog.getText(self, '添加模块', '输入模块名称:')
        if ok and module_name:
            item = QListWidgetItem(module_name)
            self.module_list.addItem(item)

    def remove_module(self):
        """删除模块"""
        current_item = self.module_list.currentItem()
        if current_item:
            self.module_list.takeItem(self.module_list.row(current_item))

    def add_dependency(self):
        """添加依赖"""
        dependency_name, ok = QInputDialog.getText(self, '添加依赖', '输入依赖名称:')
        if ok and dependency_name:
            item = QListWidgetItem(dependency_name)
            self.dependency_list.addItem(item)

    def remove_dependency(self):
        """删除依赖"""
        current_item = self.dependency_list.currentItem()
        if current_item:
            self.dependency_list.takeItem(self.dependency_list.row(current_item))

    def confirm_architecture(self):
        """确认架构"""
        # 更新架构信息
        self.architecture['name'] = self.name_edit.toPlainText()
        self.architecture['modules'] = [self.module_list.item(i).text() for i in range(self.module_list.count())]
        self.architecture['dependencies'] = [self.dependency_list.item(i).text() for i in range(self.dependency_list.count())]

        # 发送架构更改信号
        self.architecture_changed.emit(self.architecture)

        # 显示提示
        QMessageBox.information(self, '成功', '架构已确认!')

    def get_architecture(self):
        """获取当前架构"""
        return self.architecture