{"app-id": "com.aicodegen.AICodeGenerator", "runtime": "org.freedesktop.Platform", "runtime-version": "22.08", "sdk": "org.freedesktop.Sdk", "command": "ai-code-generator", "finish-args": ["--share=network", "--share=ipc", "--socket=x11", "--socket=wayland", "--filesystem=home"], "modules": [{"name": "ai-code-generator", "buildsystem": "simple", "build-commands": ["install -Dm755 main.py ${FLATPAK_DEST}/bin/ai-code-generator", "cp -r ui ai tools ${FLATPAK_DEST}/lib/python3.9/site-packages/"], "sources": [{"type": "dir", "path": "."}]}]}