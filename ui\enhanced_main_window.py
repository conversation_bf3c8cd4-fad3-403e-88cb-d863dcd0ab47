# 增强版主窗口

import sys
import os
import json
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QTextEdit, QComboBox, 
                            QFileDialog, QProgressBar, QMessageBox, QInputDialog,
                            QMenuBar, QMenu, QAction, QToolBar, QStatusBar, QSplitter,
                            QTreeWidget, QTreeWidgetItem, QDockWidget)
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QKeySequence
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSettings

# 导入可用的UI组件
try:
    from ui.code_generator import CodeGeneratorWidget
except ImportError:
    CodeGeneratorWidget = None

try:
    from ui.architecture_designer import ArchitectureDesignerWidget
except ImportError:
    ArchitectureDesignerWidget = None

try:
    from ui.conversational_programming import ConversationalProgrammingWidget
except ImportError:
    ConversationalProgrammingWidget = None

try:
    from ui.dev_tools_integration import DevToolsIntegrationWidget
except ImportError:
    DevToolsIntegrationWidget = None

# 导入可用的AI组件
try:
    from ai.context_aware_generator import ContextAwareGenerator
except ImportError:
    ContextAwareGenerator = None

try:
    from ai.model_selector import ModelSelector
except ImportError:
    ModelSelector = None

try:
    from ai.cost_optimizer import CostOptimizer
except ImportError:
    CostOptimizer = None

class EnhancedAICodeTool(QMainWindow):
    def __init__(self):
        super().__init__()
        self.settings = QSettings('AICodeTool', 'Settings')
        self.current_project = None
        self.project_data = {}
        self.init_ui()
        
        # 初始化AI组件
        self.api_integrator = APIIntegrator()
        self.enhanced_api_integrator = EnhancedAPIIntegrator()
        self.context_generator = ContextAwareGenerator()
        self.model_selector = ModelSelector()
        self.cost_optimizer = CostOptimizer()
        
        self.load_settings()

    def init_ui(self):
        # 设置窗口属性
        self.setWindowTitle('AI代码生成与打包工具 - 增强版')
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建主界面
        self.create_main_interface()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建停靠窗口
        self.create_dock_widgets()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        new_action = QAction('新建项目(&N)', self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction('打开项目(&O)', self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction('保存项目(&S)', self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        undo_action = QAction('撤销(&U)', self)
        undo_action.setShortcut(QKeySequence.Undo)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction('重做(&R)', self)
        redo_action.setShortcut(QKeySequence.Redo)
        edit_menu.addAction(redo_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        theme_menu = view_menu.addMenu('主题')
        light_theme = QAction('浅色主题', self)
        light_theme.triggered.connect(lambda: self.set_theme('light'))
        theme_menu.addAction(light_theme)
        
        dark_theme = QAction('深色主题', self)
        dark_theme.triggered.connect(lambda: self.set_theme('dark'))
        theme_menu.addAction(dark_theme)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        api_config_action = QAction('API配置管理(&A)', self)
        api_config_action.triggered.connect(self.show_api_config)
        tools_menu.addAction(api_config_action)
        
        model_selector_action = QAction('智能模型选择(&M)', self)
        model_selector_action.triggered.connect(self.show_model_selector)
        tools_menu.addAction(model_selector_action)
        
        cost_optimizer_action = QAction('成本优化分析(&C)', self)
        cost_optimizer_action.triggered.connect(self.show_cost_optimizer)
        tools_menu.addAction(cost_optimizer_action)
        
        tools_menu.addSeparator()
        
        settings_action = QAction('设置(&S)', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        help_action = QAction('使用指南(&H)', self)
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        
        # 新建项目
        new_action = QAction('新建', self)
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)
        
        # 打开项目
        open_action = QAction('打开', self)
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)
        
        # 保存项目
        save_action = QAction('保存', self)
        save_action.triggered.connect(self.save_project)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 运行代码
        run_action = QAction('运行', self)
        run_action.triggered.connect(self.run_current_code)
        toolbar.addAction(run_action)
        
        # 调试代码
        debug_action = QAction('调试', self)
        debug_action.triggered.connect(self.debug_current_code)
        toolbar.addAction(debug_action)

    def create_main_interface(self):
        """创建主界面"""
        # 创建中央分割器
        central_splitter = QSplitter(Qt.Horizontal)
        self.setCentralWidget(central_splitter)
        
        # 创建选项卡
        self.tabs = QTabWidget()
        self.tab_conversation = ConversationalProgrammingWidget()
        self.tab_architecture = ArchitectureDesignerWidget()
        self.tab_generator = CodeGeneratorWidget()
        self.tab_debugger = CodeDebuggerWidget()
        self.tab_packager = PackagerWidget()
        self.tab_devtools = DevToolsIntegrationWidget()
        
        # 添加选项卡
        self.tabs.addTab(self.tab_conversation, '💬 对话编程')
        self.tabs.addTab(self.tab_architecture, '🏗️ 架构设计')
        self.tabs.addTab(self.tab_generator, '🤖 代码生成')
        self.tabs.addTab(self.tab_debugger, '🔧 代码调试')
        self.tabs.addTab(self.tab_packager, '📦 打包发布')
        self.tabs.addTab(self.tab_devtools, '🛠️ 开发工具')
        
        central_splitter.addWidget(self.tabs)
        
        # 连接信号和槽
        self.tab_conversation.code_generated.connect(self.on_code_generated)
        self.tab_conversation.suggestion_accepted.connect(self.on_suggestion_accepted)
        self.tab_architecture.architecture_changed.connect(self.on_architecture_changed)
        self.tab_generator.code_generated.connect(self.on_code_generated)
        self.tab_debugger.debug_completed.connect(self.on_debug_completed)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage('就绪')
        
        # 添加永久状态信息
        self.project_label = QLabel('无项目')
        self.status_bar.addPermanentWidget(self.project_label)
        
        self.api_status_label = QLabel('API: 未连接')
        self.status_bar.addPermanentWidget(self.api_status_label)

    def create_dock_widgets(self):
        """创建停靠窗口"""
        # 项目文件树
        self.project_dock = QDockWidget('项目文件', self)
        self.project_tree = QTreeWidget()
        self.project_tree.setHeaderLabel('项目结构')
        self.project_dock.setWidget(self.project_tree)
        self.addDockWidget(Qt.LeftDockWidgetArea, self.project_dock)
        
        # 输出面板
        self.output_dock = QDockWidget('输出', self)
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_dock.setWidget(self.output_text)
        self.addDockWidget(Qt.BottomDockWidgetArea, self.output_dock)

    def new_project(self):
        """新建项目"""
        project_name, ok = QInputDialog.getText(self, '新建项目', '项目名称:')
        if ok and project_name:
            self.current_project = project_name
            self.project_data = {
                'name': project_name,
                'architecture': {},
                'code': '',
                'settings': {}
            }
            self.update_project_tree()
            self.project_label.setText(f'项目: {project_name}')
            self.status_bar.showMessage(f'新建项目: {project_name}')

    def open_project(self):
        """打开项目"""
        file_path, _ = QFileDialog.getOpenFileName(self, '打开项目', '', 'AI项目文件 (*.aiproj)')
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.project_data = json.load(f)
                self.current_project = self.project_data.get('name', '未命名项目')
                self.load_project_data()
                self.project_label.setText(f'项目: {self.current_project}')
                self.status_bar.showMessage(f'打开项目: {self.current_project}')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'打开项目失败: {str(e)}')

    def save_project(self):
        """保存项目"""
        if not self.current_project:
            QMessageBox.warning(self, '警告', '没有打开的项目!')
            return
        
        file_path, _ = QFileDialog.getSaveFileName(self, '保存项目', f'{self.current_project}.aiproj', 'AI项目文件 (*.aiproj)')
        if file_path:
            try:
                # 收集当前项目数据
                self.collect_project_data()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.project_data, f, ensure_ascii=False, indent=2)
                self.status_bar.showMessage(f'项目已保存: {file_path}')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'保存项目失败: {str(e)}')

    def collect_project_data(self):
        """收集当前项目数据"""
        self.project_data.update({
            'architecture': self.tab_architecture.get_architecture(),
            'code': self.tab_debugger.code,
            'generator_settings': {
                'api_provider': self.tab_generator.api_combo.currentText(),
                'request': self.tab_generator.request_edit.toPlainText()
            }
        })

    def load_project_data(self):
        """加载项目数据"""
        if 'architecture' in self.project_data:
            self.tab_architecture.architecture = self.project_data['architecture']
        if 'code' in self.project_data:
            self.tab_debugger.set_code(self.project_data['code'])

    def update_project_tree(self):
        """更新项目文件树"""
        self.project_tree.clear()
        if self.current_project:
            root = QTreeWidgetItem(self.project_tree)
            root.setText(0, self.current_project)
            
            # 添加架构节点
            arch_item = QTreeWidgetItem(root)
            arch_item.setText(0, '架构设计')
            
            # 添加代码节点
            code_item = QTreeWidgetItem(root)
            code_item.setText(0, '生成代码')
            
            self.project_tree.expandAll()

    def set_theme(self, theme):
        """设置主题"""
        if theme == 'dark':
            self.setStyleSheet("""
                QMainWindow { background-color: #2b2b2b; color: #ffffff; }
                QTextEdit { background-color: #3c3c3c; color: #ffffff; border: 1px solid #555; }
                QTabWidget::pane { border: 1px solid #555; }
                QTabBar::tab { background-color: #404040; color: #ffffff; padding: 8px; }
                QTabBar::tab:selected { background-color: #555; }
            """)
        else:
            self.setStyleSheet("")

    def show_api_config(self):
        """显示API配置对话框"""
        from ui.api_config_manager import APIConfigManager
        dialog = APIConfigManager(self)
        dialog.config_updated.connect(self.on_api_config_updated)
        dialog.exec_()

    def show_model_selector(self):
        """显示智能模型选择器"""
        from ai.model_selector import TaskRequirement
        
        # 创建示例任务需求
        requirement = TaskRequirement(
            task_type="code_generation",
            complexity="medium",
            language="python",
            domain="web",
            context_length=5000,
            quality_priority="balanced"
        )
        
        # 获取模型推荐
        recommendations = self.model_selector.get_model_recommendations(requirement)
        
        # 显示推荐结果
        result_text = "🤖 智能模型推荐:\\n\\n"
        for i, (model, score, reason) in enumerate(recommendations, 1):
            result_text += f"{i}. {model} (得分: {score:.3f})\\n"
            result_text += f"   {reason}\\n\\n"
        
        QMessageBox.information(self, '智能模型选择', result_text)
    
    def show_cost_optimizer(self):
        """显示成本优化分析"""
        # 获取成本报告
        cost_report = self.cost_optimizer.generate_cost_report(30)
        
        # 创建对话框显示报告
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
        
        dialog = QDialog(self)
        dialog.setWindowTitle('成本优化分析')
        dialog.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout(dialog)
        
        text_edit = QTextEdit()
        text_edit.setPlainText(cost_report)
        text_edit.setReadOnly(True)
        text_edit.setFont(QFont('Consolas', 10))
        layout.addWidget(text_edit)
        
        close_btn = QPushButton('关闭')
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)
        
        dialog.exec_()
    
    def show_settings(self):
        """显示设置对话框"""
        QMessageBox.information(self, '设置', '设置功能将在后续版本中实现')

    def show_help(self):
        """显示帮助"""
        QMessageBox.information(self, '帮助', '请参考项目文档获取详细使用说明')

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, '关于', 'AI代码生成与打包工具 v2.0\n\n一款集成多种AI API的智能代码生成工具')

    def run_current_code(self):
        """运行当前代码"""
        self.tabs.setCurrentIndex(2)  # 切换到调试标签
        self.tab_debugger.run_code()

    def debug_current_code(self):
        """调试当前代码"""
        self.tabs.setCurrentIndex(2)  # 切换到调试标签
        self.tab_debugger.debug_code()

    def load_settings(self):
        """加载设置"""
        # 恢复窗口状态
        self.restoreGeometry(self.settings.value('geometry', b''))
        self.restoreState(self.settings.value('windowState', b''))

    def closeEvent(self, event):
        """关闭事件"""
        # 保存设置
        self.settings.setValue('geometry', self.saveGeometry())
        self.settings.setValue('windowState', self.saveState())
        
        # 询问是否保存项目
        if self.current_project:
            reply = QMessageBox.question(self, '确认', '是否保存当前项目?', 
                                       QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
            if reply == QMessageBox.Yes:
                self.save_project()
            elif reply == QMessageBox.Cancel:
                event.ignore()
                return
        
        event.accept()

    # 保持原有的信号处理方法
    def on_architecture_changed(self, architecture):
        """架构更改时的处理"""
        self.tab_generator.set_architecture(architecture)
        self.status_bar.showMessage('架构已更新')
        self.output_text.append(f'[{self.get_timestamp()}] 架构已更新: {architecture["name"]}')

    def on_code_generated(self, code):
        """代码生成后的处理"""
        self.tab_debugger.set_code(code)
        self.tabs.setCurrentIndex(2)  # 切换到调试标签
        self.status_bar.showMessage('代码已生成，准备调试')
        self.output_text.append(f'[{self.get_timestamp()}] 代码生成完成，共{len(code.split())}行')

    def on_debug_completed(self, success, code):
        """调试完成后的处理"""
        if success:
            self.tab_packager.set_code(code)
            self.tabs.setCurrentIndex(3)  # 切换到打包标签
            self.status_bar.showMessage('代码调试成功，准备打包')
            self.output_text.append(f'[{self.get_timestamp()}] 代码调试成功')
        else:
            self.status_bar.showMessage('代码调试失败，请修改后重试')
            self.output_text.append(f'[{self.get_timestamp()}] 代码调试失败')

    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime('%H:%M:%S')
    
    def on_api_config_updated(self, config):
        """API配置更新时的处理"""
        # 更新API集成器的配置
        for provider, settings in config.items():
            if settings.get('enabled', False) and settings.get('api_key'):
                self.api_integrator.set_api_key(provider, settings['api_key'])
                self.enhanced_api_integrator.set_api_key(provider, settings['api_key'])
        
        self.output_text.append(f'[{self.get_timestamp()}] API配置已更新')
        self.status_bar.showMessage('API配置已更新')
    
    def on_suggestion_accepted(self, code: str, description: str):
        """建议被接受时的处理"""
        self.tab_debugger.set_code(code)
        self.tabs.setCurrentIndex(3)  # 切换到调试标签
        self.output_text.append(f'[{self.get_timestamp()}] 接受了AI建议: {description}')
        self.status_bar.showMessage('AI建议已应用')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setFont(QFont("SimHei", 9))
    app.setStyle('Fusion')
    
    window = EnhancedAICodeTool()
    window.show()
    
    sys.exit(app.exec_())