# 代码质量检查工具

import os
import ast
import re
import subprocess
import json
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum
import importlib.util

class IssueType(Enum):
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    STYLE = "style"

class IssueSeverity(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class CodeIssue:
    """代码问题"""
    file_path: str
    line_number: int
    column: int
    issue_type: IssueType
    severity: IssueSeverity
    rule_id: str
    message: str
    suggestion: str = ""
    auto_fixable: bool = False

@dataclass
class QualityMetrics:
    """质量指标"""
    total_lines: int = 0
    code_lines: int = 0
    comment_lines: int = 0
    blank_lines: int = 0
    complexity: int = 0
    maintainability_index: float = 0.0
    test_coverage: float = 0.0
    duplication_ratio: float = 0.0

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_path: str = None):
        self.project_path = project_path or os.getcwd()
        self.config = self._load_config()
        self.checkers = self._init_checkers()
    
    def _load_config(self) -> Dict:
        """加载配置"""
        config_file = os.path.join(self.project_path, '.quality_config.json')
        default_config = {
            'enabled_checkers': ['pylint', 'flake8', 'mypy', 'bandit', 'complexity'],
            'ignore_patterns': ['**/test_*.py', '**/__pycache__/**', '**/venv/**'],
            'max_line_length': 88,
            'max_complexity': 10,
            'min_test_coverage': 80.0,
            'rules': {
                'enforce_docstrings': True,
                'enforce_type_hints': True,
                'check_security': True,
                'check_performance': True
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def _init_checkers(self) -> Dict:
        """初始化检查器"""
        return {
            'pylint': PylintChecker(self.config),
            'flake8': Flake8Checker(self.config),
            'mypy': MypyChecker(self.config),
            'bandit': BanditChecker(self.config),
            'complexity': ComplexityChecker(self.config),
            'custom': CustomChecker(self.config)
        }
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """检查单个文件"""
        if not os.path.exists(file_path):
            return []
        
        # 检查是否应该忽略此文件
        if self._should_ignore_file(file_path):
            return []
        
        issues = []
        
        # 运行启用的检查器
        for checker_name in self.config['enabled_checkers']:
            if checker_name in self.checkers:
                checker_issues = self.checkers[checker_name].check_file(file_path)
                issues.extend(checker_issues)
        
        return issues
    
    def check_project(self) -> Dict[str, List[CodeIssue]]:
        """检查整个项目"""
        results = {}
        
        # 获取所有Python文件
        python_files = self._get_python_files()
        
        for file_path in python_files:
            issues = self.check_file(file_path)
            if issues:
                results[file_path] = issues
        
        return results
    
    def _get_python_files(self) -> List[str]:
        """获取所有Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_path):
            # 跳过忽略的目录
            dirs[:] = [d for d in dirs if not self._should_ignore_path(os.path.join(root, d))]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    if not self._should_ignore_file(file_path):
                        python_files.append(file_path)
        
        return python_files
    
    def _should_ignore_file(self, file_path: str) -> bool:
        """检查是否应该忽略文件"""
        return self._should_ignore_path(file_path)
    
    def _should_ignore_path(self, path: str) -> bool:
        """检查是否应该忽略路径"""
        import fnmatch
        
        rel_path = os.path.relpath(path, self.project_path)
        
        for pattern in self.config['ignore_patterns']:
            if fnmatch.fnmatch(rel_path, pattern):
                return True
        
        return False
    
    def get_quality_metrics(self, file_path: str = None) -> QualityMetrics:
        """获取质量指标"""
        if file_path:
            return self._get_file_metrics(file_path)
        else:
            return self._get_project_metrics()
    
    def _get_file_metrics(self, file_path: str) -> QualityMetrics:
        """获取文件质量指标"""
        if not os.path.exists(file_path):
            return QualityMetrics()
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\\n')
        total_lines = len(lines)
        code_lines = 0
        comment_lines = 0
        blank_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                blank_lines += 1
            elif stripped.startswith('#'):
                comment_lines += 1
            else:
                code_lines += 1
        
        # 计算复杂度
        complexity = self._calculate_complexity(content)
        
        # 计算可维护性指数
        maintainability = self._calculate_maintainability_index(
            total_lines, complexity, comment_lines / max(total_lines, 1)
        )
        
        return QualityMetrics(
            total_lines=total_lines,
            code_lines=code_lines,
            comment_lines=comment_lines,
            blank_lines=blank_lines,
            complexity=complexity,
            maintainability_index=maintainability
        )
    
    def _get_project_metrics(self) -> QualityMetrics:
        """获取项目质量指标"""
        python_files = self._get_python_files()
        
        total_metrics = QualityMetrics()
        
        for file_path in python_files:
            file_metrics = self._get_file_metrics(file_path)
            total_metrics.total_lines += file_metrics.total_lines
            total_metrics.code_lines += file_metrics.code_lines
            total_metrics.comment_lines += file_metrics.comment_lines
            total_metrics.blank_lines += file_metrics.blank_lines
            total_metrics.complexity += file_metrics.complexity
        
        # 计算平均可维护性指数
        if python_files:
            total_maintainability = sum(
                self._get_file_metrics(f).maintainability_index for f in python_files
            )
            total_metrics.maintainability_index = total_maintainability / len(python_files)
        
        # 获取测试覆盖率
        total_metrics.test_coverage = self._get_test_coverage()
        
        # 计算重复率
        total_metrics.duplication_ratio = self._calculate_duplication_ratio()
        
        return total_metrics
    
    def _calculate_complexity(self, content: str) -> int:
        """计算圈复杂度"""
        try:
            tree = ast.parse(content)
            complexity = 1  # 基础复杂度
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                    complexity += 1
                elif isinstance(node, ast.Try):
                    complexity += len(node.handlers)
                elif isinstance(node, ast.BoolOp):
                    complexity += len(node.values) - 1
                elif isinstance(node, ast.comprehension):
                    complexity += 1
            
            return complexity
        except:
            return 0
    
    def _calculate_maintainability_index(self, lines: int, complexity: int, comment_ratio: float) -> float:
        """计算可维护性指数"""
        import math
        
        if lines == 0:
            return 100.0
        
        # 简化的可维护性指数计算
        volume = lines * math.log2(max(complexity, 1))
        mi = max(0, (171 - 5.2 * math.log(volume) - 0.23 * complexity - 16.2 * math.log(lines) + 50 * math.sin(math.sqrt(2.4 * comment_ratio))))
        
        return min(100.0, mi)
    
    def _get_test_coverage(self) -> float:
        """获取测试覆盖率"""
        try:
            # 尝试运行coverage
            result = subprocess.run(
                ['coverage', 'report', '--show-missing'],
                cwd=self.project_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                # 解析覆盖率报告
                lines = result.stdout.split('\\n')
                for line in lines:
                    if 'TOTAL' in line:
                        parts = line.split()
                        for part in parts:
                            if part.endswith('%'):
                                return float(part[:-1])
        except:
            pass
        
        return 0.0
    
    def _calculate_duplication_ratio(self) -> float:
        """计算代码重复率"""
        python_files = self._get_python_files()
        if len(python_files) < 2:
            return 0.0
        
        # 简化的重复检测
        code_blocks = {}
        total_blocks = 0
        duplicate_blocks = 0
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 检查连续的5行代码块
                for i in range(len(lines) - 4):
                    block = ''.join(lines[i:i+5]).strip()
                    if len(block) > 50:  # 忽略太短的块
                        total_blocks += 1
                        if block in code_blocks:
                            duplicate_blocks += 1
                        else:
                            code_blocks[block] = file_path
            except:
                continue
        
        return (duplicate_blocks / max(total_blocks, 1)) * 100
    
    def generate_report(self, results: Dict[str, List[CodeIssue]]) -> str:
        """生成质量报告"""
        report = []
        report.append("# 代码质量检查报告\\n")
        
        # 统计信息
        total_issues = sum(len(issues) for issues in results.values())
        total_files = len(results)
        
        report.append(f"## 概览")
        report.append(f"- 检查文件数: {total_files}")
        report.append(f"- 发现问题数: {total_issues}")
        
        # 按严重程度统计
        severity_counts = {severity: 0 for severity in IssueSeverity}
        for issues in results.values():
            for issue in issues:
                severity_counts[issue.severity] += 1
        
        report.append(f"- 严重问题: {severity_counts[IssueSeverity.CRITICAL]}")
        report.append(f"- 高优先级: {severity_counts[IssueSeverity.HIGH]}")
        report.append(f"- 中优先级: {severity_counts[IssueSeverity.MEDIUM]}")
        report.append(f"- 低优先级: {severity_counts[IssueSeverity.LOW]}")
        report.append("")
        
        # 质量指标
        metrics = self.get_quality_metrics()
        report.append("## 质量指标")
        report.append(f"- 总行数: {metrics.total_lines}")
        report.append(f"- 代码行数: {metrics.code_lines}")
        report.append(f"- 注释行数: {metrics.comment_lines}")
        report.append(f"- 空白行数: {metrics.blank_lines}")
        report.append(f"- 圈复杂度: {metrics.complexity}")
        report.append(f"- 可维护性指数: {metrics.maintainability_index:.1f}")
        report.append(f"- 测试覆盖率: {metrics.test_coverage:.1f}%")
        report.append(f"- 代码重复率: {metrics.duplication_ratio:.1f}%")
        report.append("")
        
        # 详细问题列表
        if results:
            report.append("## 详细问题")
            for file_path, issues in results.items():
                rel_path = os.path.relpath(file_path, self.project_path)
                report.append(f"### {rel_path}")
                
                for issue in sorted(issues, key=lambda x: (x.line_number, x.severity.value)):
                    severity_icon = {
                        IssueSeverity.CRITICAL: "🔴",
                        IssueSeverity.HIGH: "🟠",
                        IssueSeverity.MEDIUM: "🟡",
                        IssueSeverity.LOW: "🔵"
                    }[issue.severity]
                    
                    report.append(f"- {severity_icon} **行 {issue.line_number}**: {issue.message}")
                    report.append(f"  - 规则: `{issue.rule_id}`")
                    if issue.suggestion:
                        report.append(f"  - 建议: {issue.suggestion}")
                    if issue.auto_fixable:
                        report.append(f"  - 🔧 可自动修复")
                
                report.append("")
        
        return "\\n".join(report)
    
    def auto_fix_issues(self, file_path: str, issues: List[CodeIssue]) -> Tuple[bool, str]:
        """自动修复问题"""
        fixable_issues = [issue for issue in issues if issue.auto_fixable]
        
        if not fixable_issues:
            return False, "没有可自动修复的问题"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按行号倒序排序，避免修复时行号变化
            fixable_issues.sort(key=lambda x: x.line_number, reverse=True)
            
            lines = content.split('\\n')
            fixed_count = 0
            
            for issue in fixable_issues:
                if self._apply_fix(lines, issue):
                    fixed_count += 1
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\\n'.join(lines))
            
            return True, f"已修复 {fixed_count} 个问题"
        
        except Exception as e:
            return False, f"修复失败: {str(e)}"
    
    def _apply_fix(self, lines: List[str], issue: CodeIssue) -> bool:
        """应用修复"""
        line_idx = issue.line_number - 1
        if line_idx < 0 or line_idx >= len(lines):
            return False
        
        # 根据规则ID应用不同的修复
        if issue.rule_id == 'trailing-whitespace':
            lines[line_idx] = lines[line_idx].rstrip()
            return True
        elif issue.rule_id == 'missing-final-newline':
            if line_idx == len(lines) - 1:
                lines.append('')
                return True
        elif issue.rule_id == 'unused-import':
            # 移除未使用的导入
            if lines[line_idx].strip().startswith(('import ', 'from ')):
                lines[line_idx] = ''
                return True
        
        return False

class BaseChecker:
    """检查器基类"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """检查文件"""
        raise NotImplementedError

class PylintChecker(BaseChecker):
    """Pylint检查器"""
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """使用Pylint检查文件"""
        try:
            result = subprocess.run(
                ['pylint', '--output-format=json', file_path],
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                pylint_results = json.loads(result.stdout)
                issues = []
                
                for item in pylint_results:
                    severity_map = {
                        'error': IssueSeverity.HIGH,
                        'warning': IssueSeverity.MEDIUM,
                        'refactor': IssueSeverity.LOW,
                        'convention': IssueSeverity.LOW
                    }
                    
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=item.get('line', 0),
                        column=item.get('column', 0),
                        issue_type=IssueType.WARNING,
                        severity=severity_map.get(item.get('type', 'warning'), IssueSeverity.MEDIUM),
                        rule_id=item.get('symbol', 'unknown'),
                        message=item.get('message', ''),
                        auto_fixable=item.get('symbol') in ['trailing-whitespace', 'unused-import']
                    ))
                
                return issues
        except:
            pass
        
        return []

class Flake8Checker(BaseChecker):
    """Flake8检查器"""
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """使用Flake8检查文件"""
        try:
            result = subprocess.run(
                ['flake8', '--format=json', file_path],
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                flake8_results = json.loads(result.stdout)
                issues = []
                
                for file_data in flake8_results.values():
                    for item in file_data:
                        issues.append(CodeIssue(
                            file_path=file_path,
                            line_number=item.get('line_number', 0),
                            column=item.get('column_number', 0),
                            issue_type=IssueType.STYLE,
                            severity=IssueSeverity.LOW,
                            rule_id=item.get('code', 'unknown'),
                            message=item.get('text', ''),
                            auto_fixable=item.get('code', '').startswith(('W291', 'W292'))
                        ))
                
                return issues
        except:
            pass
        
        return []

class MypyChecker(BaseChecker):
    """Mypy类型检查器"""
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """使用Mypy检查文件"""
        try:
            result = subprocess.run(
                ['mypy', '--show-error-codes', file_path],
                capture_output=True,
                text=True
            )
            
            issues = []
            for line in result.stdout.split('\\n'):
                if ':' in line and 'error:' in line:
                    parts = line.split(':')
                    if len(parts) >= 4:
                        try:
                            line_num = int(parts[1])
                            message = ':'.join(parts[3:]).strip()
                            
                            issues.append(CodeIssue(
                                file_path=file_path,
                                line_number=line_num,
                                column=0,
                                issue_type=IssueType.ERROR,
                                severity=IssueSeverity.MEDIUM,
                                rule_id='type-error',
                                message=message
                            ))
                        except:
                            continue
            
            return issues
        except:
            pass
        
        return []

class BanditChecker(BaseChecker):
    """Bandit安全检查器"""
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """使用Bandit检查安全问题"""
        try:
            result = subprocess.run(
                ['bandit', '-f', 'json', file_path],
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                bandit_results = json.loads(result.stdout)
                issues = []
                
                for item in bandit_results.get('results', []):
                    severity_map = {
                        'HIGH': IssueSeverity.CRITICAL,
                        'MEDIUM': IssueSeverity.HIGH,
                        'LOW': IssueSeverity.MEDIUM
                    }
                    
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=item.get('line_number', 0),
                        column=0,
                        issue_type=IssueType.ERROR,
                        severity=severity_map.get(item.get('issue_severity', 'MEDIUM'), IssueSeverity.MEDIUM),
                        rule_id=item.get('test_id', 'security'),
                        message=item.get('issue_text', ''),
                        suggestion=item.get('issue_text', '')
                    ))
                
                return issues
        except:
            pass
        
        return []

class ComplexityChecker(BaseChecker):
    """复杂度检查器"""
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """检查代码复杂度"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    complexity = self._calculate_function_complexity(node)
                    if complexity > self.config.get('max_complexity', 10):
                        issues.append(CodeIssue(
                            file_path=file_path,
                            line_number=node.lineno,
                            column=node.col_offset,
                            issue_type=IssueType.WARNING,
                            severity=IssueSeverity.MEDIUM,
                            rule_id='high-complexity',
                            message=f"函数 '{node.name}' 的复杂度为 {complexity}，超过了限制 {self.config.get('max_complexity', 10)}",
                            suggestion=f"考虑将函数 '{node.name}' 分解为更小的函数"
                        ))
        except:
            pass
        
        return issues
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """计算函数复杂度"""
        complexity = 1
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.Try):
                complexity += len(child.handlers)
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity

class CustomChecker(BaseChecker):
    """自定义检查器"""
    
    def check_file(self, file_path: str) -> List[CodeIssue]:
        """自定义检查规则"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                # 检查行长度
                if len(line.rstrip()) > self.config.get('max_line_length', 88):
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=i,
                        column=self.config.get('max_line_length', 88),
                        issue_type=IssueType.STYLE,
                        severity=IssueSeverity.LOW,
                        rule_id='line-too-long',
                        message=f"行长度 {len(line.rstrip())} 超过了限制 {self.config.get('max_line_length', 88)}",
                        auto_fixable=False
                    ))
                
                # 检查尾随空格
                if line.rstrip() != line.rstrip('\\n').rstrip('\\r'):
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=i,
                        column=len(line.rstrip()),
                        issue_type=IssueType.STYLE,
                        severity=IssueSeverity.LOW,
                        rule_id='trailing-whitespace',
                        message="行末有多余的空格",
                        auto_fixable=True
                    ))
                
                # 检查TODO注释
                if 'TODO' in line or 'FIXME' in line:
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=i,
                        column=line.find('TODO') if 'TODO' in line else line.find('FIXME'),
                        issue_type=IssueType.INFO,
                        severity=IssueSeverity.LOW,
                        rule_id='todo-comment',
                        message="发现TODO或FIXME注释",
                        suggestion="考虑创建issue来跟踪这个任务"
                    ))
        except:
            pass
        
        return issues