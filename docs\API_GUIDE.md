# API使用指南

本文档详细介绍了AI代码生成器的API使用方法和最佳实践。

## 📋 目录

- [快速开始](#快速开始)
- [AI代码生成API](#ai代码生成api)
- [调试器API](#调试器api)
- [打包器API](#打包器api)
- [性能监控API](#性能监控api)
- [主题管理API](#主题管理api)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## 🚀 快速开始

### 基本导入

```python
from ai.enhanced_code_generator import (
    EnhancedCodeGenerator, 
    CodeGenerationRequest, 
    CodeLanguage, 
    CodeComplexity
)
from ai.enhanced_ai_engine import EnhancedAIEngine
from tools.enhanced_packager import EnhancedPackager, PackageConfig
from tools.performance_optimizer import performance_monitor
```

### 初始化

```python
# 创建代码生成器
generator = EnhancedCodeGenerator()

# 创建AI引擎
ai_engine = EnhancedAIEngine()

# 创建打包器
packager = EnhancedPackager()

# 启动性能监控
performance_monitor.start_monitoring()
```

## 🤖 AI代码生成API

### EnhancedCodeGenerator

#### 基本代码生成

```python
async def generate_simple_code():
    """生成简单代码示例"""
    request = CodeGenerationRequest(
        prompt="创建一个计算斐波那契数列的函数",
        language=CodeLanguage.PYTHON,
        complexity=CodeComplexity.SIMPLE,
        include_tests=True,
        include_docs=True
    )
    
    response = await generator.generate_code(request)
    
    print("生成的代码:")
    print(response.code)
    print("\n测试代码:")
    print(response.tests)
    print(f"\n质量评分: {response.quality_score}")
```

#### 高级代码生成

```python
async def generate_advanced_code():
    """生成高级代码示例"""
    request = CodeGenerationRequest(
        prompt="创建一个RESTful API服务器",
        language=CodeLanguage.PYTHON,
        complexity=CodeComplexity.COMPLEX,
        style=CodeStyle.OBJECT_ORIENTED,
        requirements=[
            "使用Flask框架",
            "支持JWT认证",
            "包含数据库操作",
            "支持异步处理"
        ],
        constraints=[
            "代码行数不超过200行",
            "必须包含错误处理",
            "需要详细的API文档"
        ],
        context={
            "database": "PostgreSQL",
            "authentication": "JWT",
            "deployment": "Docker"
        }
    )
    
    response = await generator.generate_code(request)
    return response
```

#### 批量代码生成

```python
async def batch_generate():
    """批量生成代码"""
    requests = [
        CodeGenerationRequest(
            prompt="创建数据验证函数",
            language=CodeLanguage.PYTHON
        ),
        CodeGenerationRequest(
            prompt="创建日志记录器",
            language=CodeLanguage.PYTHON
        ),
        CodeGenerationRequest(
            prompt="创建配置管理器",
            language=CodeLanguage.PYTHON
        )
    ]
    
    # 并发生成
    import asyncio
    responses = await asyncio.gather(*[
        generator.generate_code(req) for req in requests
    ])
    
    return responses
```

### 代码分析和优化

```python
async def analyze_and_optimize():
    """分析和优化代码"""
    original_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
    """
    
    # 分析代码质量
    quality_score = generator.analyze_code_quality(
        original_code, 
        CodeLanguage.PYTHON
    )
    
    # 生成优化建议
    suggestions = generator.generate_suggestions(
        original_code,
        CodeGenerationRequest(
            prompt="优化这个斐波那契函数",
            language=CodeLanguage.PYTHON
        )
    )
    
    print(f"代码质量评分: {quality_score}")
    print("优化建议:")
    for suggestion in suggestions:
        print(f"- {suggestion}")
```

## 🐛 调试器API

### EnhancedDebugger

```python
from ai.enhanced_code_generator import EnhancedDebugger

async def debug_code_example():
    """代码调试示例"""
    debugger = EnhancedDebugger(generator)
    
    problematic_code = """
def divide_numbers(a, b):
    result = a / b
    return result

print(divide_numbers(10, 0))
    """
    
    error_message = "ZeroDivisionError: division by zero"
    
    debug_result = await debugger.debug_code(
        code=problematic_code,
        error_message=error_message,
        language=CodeLanguage.PYTHON
    )
    
    print("错误分析:")
    print(f"错误类型: {debug_result['error_analysis']['error_type']}")
    print(f"严重程度: {debug_result['error_analysis']['severity']}")
    
    print("\n修复建议:")
    for suggestion in debug_result['fix_suggestions']:
        print(f"- {suggestion}")
    
    print("\n修复后的代码:")
    print(debug_result['fixed_code'])
    
    print(f"\n修复信心度: {debug_result['confidence_score']:.2%}")
```

### 错误模式分析

```python
def analyze_error_patterns():
    """分析错误模式"""
    debugger = EnhancedDebugger(generator)
    
    # 获取调试历史
    history = debugger.debug_history
    
    # 分析常见错误类型
    error_types = {}
    for debug_session in history:
        error_type = debug_session['error_analysis']['error_type']
        error_types[error_type] = error_types.get(error_type, 0) + 1
    
    print("常见错误类型统计:")
    for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
        print(f"{error_type}: {count}次")
```

## 📦 打包器API

### EnhancedPackager

#### 基本打包

```python
async def basic_packaging():
    """基本打包示例"""
    packager = EnhancedPackager()
    
    # 创建配置
    config = PackageConfig(
        name="MyApp",
        version="1.0.0",
        description="我的AI生成应用",
        author="开发者",
        main_file="main.py",
        platforms=[Platform.WINDOWS_X64, Platform.LINUX_X64],
        formats=[PackageFormat.EXECUTABLE, PackageFormat.PORTABLE]
    )
    
    packager.config = config
    
    # 执行打包
    def progress_callback(progress, message):
        print(f"打包进度: {progress}% - {message}")
    
    result = await packager.package_project(
        project_path="./my_project",
        progress_callback=progress_callback
    )
    
    if result['success']:
        print("打包成功!")
        for platform_format, details in result['results'].items():
            if details.get('success'):
                print(f"{platform_format}: {details['output_path']}")
    else:
        print(f"打包失败: {result['error']}")
```

#### 高级打包配置

```python
def advanced_packaging_config():
    """高级打包配置"""
    config = PackageConfig(
        name="AdvancedApp",
        version="2.0.0",
        description="高级AI应用",
        author="AI Team",
        main_file="app.py",
        platforms=[
            Platform.WINDOWS_X64,
            Platform.LINUX_X64,
            Platform.MACOS_X64
        ],
        formats=[
            PackageFormat.EXECUTABLE,
            PackageFormat.INSTALLER,
            PackageFormat.CONTAINER
        ],
        include_files=[
            "config/",
            "assets/",
            "README.md"
        ],
        exclude_files=[
            "tests/",
            "docs/",
            "*.pyc",
            "__pycache__"
        ],
        dependencies=[
            "requests>=2.25.0",
            "PyQt5>=5.15.0"
        ],
        icon="assets/icon.ico",
        license="MIT",
        homepage="https://github.com/myapp",
        keywords=["ai", "code", "generator"]
    )
    
    return config
```

## 📊 性能监控API

### PerformanceMonitor

```python
from tools.performance_optimizer import performance_monitor, performance_optimizer

def performance_monitoring_example():
    """性能监控示例"""
    
    # 启动监控
    performance_monitor.start_monitoring()
    
    # 使用性能装饰器
    @performance_monitor.performance_decorator("my_function")
    def my_expensive_function():
        import time
        time.sleep(1)  # 模拟耗时操作
        return "完成"
    
    # 执行函数
    result = my_expensive_function()
    
    # 获取性能摘要
    summary = performance_monitor.get_performance_summary()
    print("性能摘要:")
    print(f"平均CPU使用率: {summary['system_metrics']['avg_cpu_percent']:.1f}%")
    print(f"平均内存使用率: {summary['system_metrics']['avg_memory_percent']:.1f}%")
    
    # 停止监控
    performance_monitor.stop_monitoring()
```

### 性能优化分析

```python
def performance_optimization():
    """性能优化分析"""
    
    # 分析性能
    analysis = performance_optimizer.analyze_performance()
    
    print(f"性能评分: {analysis['performance_score']:.1f}/100")
    
    if analysis['issues']:
        print("\n发现的问题:")
        for issue in analysis['issues']:
            print(f"- {issue['severity'].upper()}: {issue['description']}")
    
    if analysis['recommendations']:
        print("\n优化建议:")
        for rec in analysis['recommendations']:
            print(f"- {rec}")
    
    # 生成详细报告
    report = performance_optimizer.generate_optimization_report()
    
    # 保存报告
    performance_optimizer.save_report("performance_report.md")
```

## 🎨 主题管理API

### ThemeManager

```python
from ui.theme_manager import ThemeManager

def theme_management_example():
    """主题管理示例"""
    theme_manager = ThemeManager()
    
    # 获取可用主题
    themes = theme_manager.get_available_themes()
    print("可用主题:", themes)
    
    # 创建自定义主题
    custom_colors = {
        "primary": "#1E3A8A",
        "secondary": "#3B82F6",
        "accent": "#60A5FA",
        "background": "#0F172A",
        "surface": "#1E293B",
        "text": "#F1F5F9",
        "text_secondary": "#94A3B8",
        "border": "#334155",
        "hover": "#475569",
        "selected": "#3B82F6"
    }
    
    theme_manager.create_custom_theme("my_blue_theme", custom_colors)
    
    # 导出主题
    theme_manager.export_theme("my_blue_theme", "my_theme.json")
    
    # 导入主题
    theme_manager.import_theme("my_theme.json")
    
    # 应用主题到组件
    from PyQt5.QtWidgets import QWidget
    widget = QWidget()
    theme_manager.apply_theme(widget, "my_blue_theme")
```

## ❌ 错误处理

### 全局错误处理

```python
from tools.performance_optimizer import error_handler

def error_handling_example():
    """错误处理示例"""
    
    try:
        # 可能出错的代码
        result = risky_operation()
    except Exception as e:
        # 手动记录错误
        error_handler.handle_exception(type(e), e, e.__traceback__)
        
        # 获取错误摘要
        summary = error_handler.get_error_summary()
        print(f"总错误数: {summary['total_errors']}")
        print("最常见错误:")
        for error in summary['most_common_errors']:
            print(f"- {error['type']}: {error['count']}次")
```

### 自定义错误处理

```python
import logging

def setup_custom_logging():
    """设置自定义日志"""
    
    # 创建自定义日志器
    logger = logging.getLogger('my_app')
    logger.setLevel(logging.DEBUG)
    
    # 创建处理器
    file_handler = logging.FileHandler('my_app.log', encoding='utf-8')
    console_handler = logging.StreamHandler()
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
```

## 💡 最佳实践

### 1. 异步编程

```python
import asyncio

async def efficient_code_generation():
    """高效的代码生成"""
    
    # 并发处理多个请求
    requests = [create_request(i) for i in range(5)]
    
    # 使用信号量限制并发数
    semaphore = asyncio.Semaphore(3)
    
    async def generate_with_limit(request):
        async with semaphore:
            return await generator.generate_code(request)
    
    results = await asyncio.gather(*[
        generate_with_limit(req) for req in requests
    ])
    
    return results
```

### 2. 缓存优化

```python
from functools import lru_cache
import hashlib

class CachedGenerator:
    """带缓存的代码生成器"""
    
    def __init__(self):
        self.generator = EnhancedCodeGenerator()
        self.cache = {}
    
    async def generate_with_cache(self, request: CodeGenerationRequest):
        """带缓存的代码生成"""
        
        # 创建缓存键
        cache_key = self._create_cache_key(request)
        
        # 检查缓存
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 生成代码
        response = await self.generator.generate_code(request)
        
        # 存储到缓存
        self.cache[cache_key] = response
        
        return response
    
    def _create_cache_key(self, request: CodeGenerationRequest) -> str:
        """创建缓存键"""
        key_data = f"{request.prompt}_{request.language.value}_{request.complexity.value}"
        return hashlib.md5(key_data.encode()).hexdigest()
```

### 3. 资源管理

```python
from contextlib import asynccontextmanager

@asynccontextmanager
async def managed_generator():
    """资源管理的代码生成器"""
    
    generator = EnhancedCodeGenerator()
    performance_monitor.start_monitoring()
    
    try:
        yield generator
    finally:
        performance_monitor.stop_monitoring()
        # 清理资源
        generator.cleanup()

# 使用示例
async def use_managed_generator():
    async with managed_generator() as gen:
        response = await gen.generate_code(request)
        return response
```

### 4. 错误重试机制

```python
import asyncio
from typing import TypeVar, Callable

T = TypeVar('T')

async def retry_with_backoff(
    func: Callable[..., T], 
    max_retries: int = 3,
    backoff_factor: float = 1.0,
    *args, **kwargs
) -> T:
    """带退避的重试机制"""
    
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            
            wait_time = backoff_factor * (2 ** attempt)
            print(f"重试 {attempt + 1}/{max_retries}，等待 {wait_time}s...")
            await asyncio.sleep(wait_time)

# 使用示例
async def reliable_generation():
    response = await retry_with_backoff(
        generator.generate_code,
        max_retries=3,
        backoff_factor=1.0,
        request=request
    )
    return response
```

---

这份API指南涵盖了AI代码生成器的主要功能和使用方法。更多详细信息请参考源代码中的文档字符串和示例。
