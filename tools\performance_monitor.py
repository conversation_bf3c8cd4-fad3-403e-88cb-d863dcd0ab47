# 性能监控工具

import os
import time
import psutil
import threading
import json
import subprocess
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque
import cProfile
import pstats
import io
import tracemalloc
import gc

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    cpu_percent: float
    memory_usage: float  # MB
    memory_percent: float
    disk_io_read: int  # bytes
    disk_io_write: int  # bytes
    network_sent: int  # bytes
    network_recv: int  # bytes
    thread_count: int
    open_files: int

@dataclass
class FunctionProfile:
    """函数性能分析"""
    function_name: str
    filename: str
    line_number: int
    call_count: int
    total_time: float
    cumulative_time: float
    per_call_time: float

@dataclass
class MemorySnapshot:
    """内存快照"""
    timestamp: float
    total_memory: int  # bytes
    peak_memory: int  # bytes
    memory_blocks: int
    top_allocations: List[Dict[str, Any]]

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, project_path: str = None):
        self.project_path = project_path or os.getcwd()
        self.config = self._load_config()
        self.is_monitoring = False
        self.metrics_history = deque(maxlen=self.config['max_history_size'])
        self.monitor_thread = None
        self.process = None
        self.profiler = None
        self.memory_tracer_started = False
        
    def _load_config(self) -> Dict:
        """加载配置"""
        config_file = os.path.join(self.project_path, '.performance_config.json')
        default_config = {
            'monitor_interval': 1.0,  # 秒
            'max_history_size': 1000,
            'cpu_threshold': 80.0,  # %
            'memory_threshold': 80.0,  # %
            'disk_threshold': 80.0,  # %
            'enable_profiling': True,
            'enable_memory_tracking': True,
            'profile_functions': True,
            'alert_thresholds': {
                'cpu_high': 90.0,
                'memory_high': 90.0,
                'response_time_slow': 5.0  # 秒
            },
            'export_formats': ['json', 'csv', 'html']
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def start_monitoring(self, target_process: int = None):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        
        # 获取目标进程
        if target_process:
            try:
                self.process = psutil.Process(target_process)
            except psutil.NoSuchProcess:
                self.process = psutil.Process()
        else:
            self.process = psutil.Process()
        
        # 启动内存跟踪
        if self.config['enable_memory_tracking']:
            tracemalloc.start()
            self.memory_tracer_started = True
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        if self.memory_tracer_started:
            tracemalloc.stop()
            self.memory_tracer_started = False
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 检查阈值警报
                self._check_alerts(metrics)
                
                time.sleep(self.config['monitor_interval'])
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(1)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # CPU使用率
            cpu_percent = self.process.cpu_percent()
            
            # 内存使用
            memory_info = self.process.memory_info()
            memory_usage = memory_info.rss / 1024 / 1024  # MB
            memory_percent = self.process.memory_percent()
            
            # 磁盘I/O
            try:
                io_counters = self.process.io_counters()
                disk_io_read = io_counters.read_bytes
                disk_io_write = io_counters.write_bytes
            except (psutil.AccessDenied, AttributeError):
                disk_io_read = disk_io_write = 0
            
            # 网络I/O (系统级别)
            try:
                net_io = psutil.net_io_counters()
                network_sent = net_io.bytes_sent
                network_recv = net_io.bytes_recv
            except AttributeError:
                network_sent = network_recv = 0
            
            # 线程数
            thread_count = self.process.num_threads()
            
            # 打开文件数
            try:
                open_files = len(self.process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_usage=memory_usage,
                memory_percent=memory_percent,
                disk_io_read=disk_io_read,
                disk_io_write=disk_io_write,
                network_sent=network_sent,
                network_recv=network_recv,
                thread_count=thread_count,
                open_files=open_files
            )
        
        except psutil.NoSuchProcess:
            # 进程已结束
            self.is_monitoring = False
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=0, memory_usage=0, memory_percent=0,
                disk_io_read=0, disk_io_write=0,
                network_sent=0, network_recv=0,
                thread_count=0, open_files=0
            )
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """检查警报阈值"""
        alerts = []
        
        if metrics.cpu_percent > self.config['alert_thresholds']['cpu_high']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.config['alert_thresholds']['memory_high']:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        # 触发警报回调
        for alert in alerts:
            self._trigger_alert(alert, metrics)
    
    def _trigger_alert(self, message: str, metrics: PerformanceMetrics):
        """触发警报"""
        print(f"⚠️ 性能警报: {message}")
        # 这里可以添加更多警报处理逻辑，如发送邮件、写日志等
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
    
    def get_metrics_history(self, duration_minutes: int = 60) -> List[PerformanceMetrics]:
        """获取历史性能指标"""
        cutoff_time = time.time() - (duration_minutes * 60)
        return [m for m in self.metrics_history if m.timestamp > cutoff_time]
    
    def get_performance_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """获取性能摘要"""
        history = self.get_metrics_history(duration_minutes)
        
        if not history:
            return {}
        
        cpu_values = [m.cpu_percent for m in history]
        memory_values = [m.memory_usage for m in history]
        
        return {
            'duration_minutes': duration_minutes,
            'sample_count': len(history),
            'cpu': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'threads': {
                'avg': sum(m.thread_count for m in history) / len(history),
                'max': max(m.thread_count for m in history)
            }
        }
    
    def start_profiling(self):
        """开始性能分析"""
        if self.profiler is None:
            self.profiler = cProfile.Profile()
            self.profiler.enable()
    
    def stop_profiling(self) -> List[FunctionProfile]:
        """停止性能分析并返回结果"""
        if self.profiler is None:
            return []
        
        self.profiler.disable()
        
        # 分析结果
        stats_stream = io.StringIO()
        stats = pstats.Stats(self.profiler, stream=stats_stream)
        stats.sort_stats('cumulative')
        
        # 提取函数性能数据
        profiles = []
        for func_info, (call_count, total_time, cumulative_time, callers) in stats.stats.items():
            filename, line_number, function_name = func_info
            
            profiles.append(FunctionProfile(
                function_name=function_name,
                filename=filename,
                line_number=line_number,
                call_count=call_count,
                total_time=total_time,
                cumulative_time=cumulative_time,
                per_call_time=total_time / call_count if call_count > 0 else 0
            ))
        
        # 按累计时间排序
        profiles.sort(key=lambda x: x.cumulative_time, reverse=True)
        
        # 重置分析器
        self.profiler = None
        
        return profiles
    
    def get_memory_snapshot(self) -> Optional[MemorySnapshot]:
        """获取内存快照"""
        if not self.memory_tracer_started:
            return None
        
        try:
            # 获取当前内存统计
            current, peak = tracemalloc.get_traced_memory()
            
            # 获取内存分配统计
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            # 提取前10个最大的内存分配
            top_allocations = []
            for stat in top_stats[:10]:
                top_allocations.append({
                    'filename': stat.traceback.format()[0] if stat.traceback.format() else 'unknown',
                    'size': stat.size,
                    'count': stat.count
                })
            
            return MemorySnapshot(
                timestamp=time.time(),
                total_memory=current,
                peak_memory=peak,
                memory_blocks=len(top_stats),
                top_allocations=top_allocations
            )
        
        except Exception as e:
            print(f"获取内存快照失败: {e}")
            return None
    
    def profile_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, FunctionProfile]:
        """分析单个函数的性能"""
        profiler = cProfile.Profile()
        
        # 执行函数
        profiler.enable()
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        profiler.disable()
        
        # 分析结果
        stats = pstats.Stats(profiler)
        
        # 查找目标函数的统计信息
        func_name = func.__name__
        total_time = end_time - start_time
        
        profile = FunctionProfile(
            function_name=func_name,
            filename=func.__code__.co_filename,
            line_number=func.__code__.co_firstlineno,
            call_count=1,
            total_time=total_time,
            cumulative_time=total_time,
            per_call_time=total_time
        )
        
        return result, profile
    
    def benchmark_code(self, code: str, iterations: int = 1000) -> Dict[str, Any]:
        """基准测试代码片段"""
        import timeit
        
        try:
            # 执行基准测试
            execution_times = []
            
            for _ in range(iterations):
                start_time = time.perf_counter()
                exec(code)
                end_time = time.perf_counter()
                execution_times.append(end_time - start_time)
            
            # 计算统计信息
            avg_time = sum(execution_times) / len(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            # 计算标准差
            variance = sum((t - avg_time) ** 2 for t in execution_times) / len(execution_times)
            std_dev = variance ** 0.5
            
            return {
                'iterations': iterations,
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'std_dev': std_dev,
                'total_time': sum(execution_times),
                'ops_per_second': 1 / avg_time if avg_time > 0 else 0
            }
        
        except Exception as e:
            return {
                'error': str(e),
                'iterations': 0,
                'avg_time': 0,
                'min_time': 0,
                'max_time': 0,
                'std_dev': 0,
                'total_time': 0,
                'ops_per_second': 0
            }
    
    def analyze_memory_leaks(self, duration_seconds: int = 60) -> Dict[str, Any]:
        """分析内存泄漏"""
        if not self.memory_tracer_started:
            tracemalloc.start()
            self.memory_tracer_started = True
        
        # 记录初始状态
        initial_snapshot = tracemalloc.take_snapshot()
        initial_memory = tracemalloc.get_traced_memory()[0]
        
        # 等待指定时间
        time.sleep(duration_seconds)
        
        # 记录最终状态
        final_snapshot = tracemalloc.take_snapshot()
        final_memory = tracemalloc.get_traced_memory()[0]
        
        # 比较快照
        top_stats = final_snapshot.compare_to(initial_snapshot, 'lineno')
        
        # 分析结果
        memory_growth = final_memory - initial_memory
        potential_leaks = []
        
        for stat in top_stats[:10]:
            if stat.size_diff > 0:  # 内存增长
                potential_leaks.append({
                    'filename': stat.traceback.format()[0] if stat.traceback.format() else 'unknown',
                    'size_diff': stat.size_diff,
                    'count_diff': stat.count_diff,
                    'current_size': stat.size
                })
        
        return {
            'duration_seconds': duration_seconds,
            'memory_growth': memory_growth,
            'growth_rate': memory_growth / duration_seconds,
            'potential_leaks': potential_leaks,
            'leak_detected': memory_growth > 1024 * 1024  # 1MB阈值
        }
    
    def generate_performance_report(self, duration_minutes: int = 60) -> str:
        """生成性能报告"""
        report = []
        report.append("# 性能监控报告\\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"监控时长: {duration_minutes} 分钟\\n")
        
        # 性能摘要
        summary = self.get_performance_summary(duration_minutes)
        if summary:
            report.append("## 性能摘要")
            report.append(f"- 采样次数: {summary['sample_count']}")
            report.append(f"- CPU使用率: 平均 {summary['cpu']['avg']:.1f}%, 最高 {summary['cpu']['max']:.1f}%")
            report.append(f"- 内存使用: 平均 {summary['memory']['avg']:.1f}MB, 最高 {summary['memory']['max']:.1f}MB")
            report.append(f"- 线程数: 平均 {summary['threads']['avg']:.1f}, 最高 {summary['threads']['max']}")
            report.append("")
        
        # 当前状态
        current = self.get_current_metrics()
        if current:
            report.append("## 当前状态")
            report.append(f"- CPU: {current.cpu_percent:.1f}%")
            report.append(f"- 内存: {current.memory_usage:.1f}MB ({current.memory_percent:.1f}%)")
            report.append(f"- 线程数: {current.thread_count}")
            report.append(f"- 打开文件数: {current.open_files}")
            report.append("")
        
        # 内存快照
        memory_snapshot = self.get_memory_snapshot()
        if memory_snapshot:
            report.append("## 内存分析")
            report.append(f"- 当前内存: {memory_snapshot.total_memory / 1024 / 1024:.1f}MB")
            report.append(f"- 峰值内存: {memory_snapshot.peak_memory / 1024 / 1024:.1f}MB")
            report.append(f"- 内存块数: {memory_snapshot.memory_blocks}")
            
            if memory_snapshot.top_allocations:
                report.append("\\n### 主要内存分配")
                for i, alloc in enumerate(memory_snapshot.top_allocations[:5], 1):
                    report.append(f"{i}. {alloc['filename']}: {alloc['size'] / 1024:.1f}KB ({alloc['count']} 次)")
            
            report.append("")
        
        # 性能建议
        report.append("## 性能建议")
        suggestions = self._generate_performance_suggestions(summary, current)
        for suggestion in suggestions:
            report.append(f"- {suggestion}")
        
        return "\\n".join(report)
    
    def _generate_performance_suggestions(self, summary: Dict, current: PerformanceMetrics) -> List[str]:
        """生成性能建议"""
        suggestions = []
        
        if summary and summary['cpu']['avg'] > 70:
            suggestions.append("CPU使用率较高，考虑优化算法或使用多进程")
        
        if current and current.memory_percent > 80:
            suggestions.append("内存使用率过高，检查是否存在内存泄漏")
        
        if current and current.thread_count > 50:
            suggestions.append("线程数量较多，考虑使用线程池或异步编程")
        
        if current and current.open_files > 100:
            suggestions.append("打开文件数较多，确保及时关闭不需要的文件")
        
        if not suggestions:
            suggestions.append("当前性能表现良好，继续保持")
        
        return suggestions
    
    def export_metrics(self, format_type: str = 'json', 
                      duration_minutes: int = 60) -> str:
        """导出性能指标"""
        history = self.get_metrics_history(duration_minutes)
        
        if format_type == 'json':
            return json.dumps([asdict(m) for m in history], indent=2)
        
        elif format_type == 'csv':
            if not history:
                return ""
            
            # CSV头部
            csv_lines = ['timestamp,cpu_percent,memory_usage,memory_percent,thread_count,open_files']
            
            # 数据行
            for m in history:
                csv_lines.append(f"{m.timestamp},{m.cpu_percent},{m.memory_usage},"
                               f"{m.memory_percent},{m.thread_count},{m.open_files}")
            
            return '\\n'.join(csv_lines)
        
        elif format_type == 'html':
            return self._generate_html_report(history)
        
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
    
    def _generate_html_report(self, history: List[PerformanceMetrics]) -> str:
        """生成HTML报告"""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>性能监控报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart-container {{ width: 800px; height: 400px; margin: 20px 0; }}
        .metrics {{ display: flex; justify-content: space-around; margin: 20px 0; }}
        .metric {{ text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
    </style>
</head>
<body>
    <h1>性能监控报告</h1>
    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <div class="metrics">
        <div class="metric">
            <h3>平均CPU</h3>
            <p>{sum(m.cpu_percent for m in history) / len(history):.1f}%</p>
        </div>
        <div class="metric">
            <h3>平均内存</h3>
            <p>{sum(m.memory_usage for m in history) / len(history):.1f}MB</p>
        </div>
        <div class="metric">
            <h3>平均线程</h3>
            <p>{sum(m.thread_count for m in history) / len(history):.1f}</p>
        </div>
    </div>
    
    <div class="chart-container">
        <canvas id="cpuChart"></canvas>
    </div>
    
    <div class="chart-container">
        <canvas id="memoryChart"></canvas>
    </div>
    
    <script>
        const timestamps = {[m.timestamp for m in history]};
        const cpuData = {[m.cpu_percent for m in history]};
        const memoryData = {[m.memory_usage for m in history]};
        
        // CPU图表
        new Chart(document.getElementById('cpuChart'), {{
            type: 'line',
            data: {{
                labels: timestamps.map(t => new Date(t * 1000).toLocaleTimeString()),
                datasets: [{{
                    label: 'CPU使用率 (%)',
                    data: cpuData,
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'CPU使用率趋势'
                    }}
                }}
            }}
        }});
        
        // 内存图表
        new Chart(document.getElementById('memoryChart'), {{
            type: 'line',
            data: {{
                labels: timestamps.map(t => new Date(t * 1000).toLocaleTimeString()),
                datasets: [{{
                    label: '内存使用 (MB)',
                    data: memoryData,
                    borderColor: 'rgb(54, 162, 235)',
                    tension: 0.1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '内存使用趋势'
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"""
        return html
    
    def save_config(self):
        """保存配置"""
        config_file = os.path.join(self.project_path, '.performance_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)