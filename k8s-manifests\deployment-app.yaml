apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-code-generator-app
  namespace: ai-code-generator
  labels:
    app: ai-code-generator
    component: app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-code-generator
      component: app
  template:
    metadata:
      labels:
        app: ai-code-generator
        component: app
    spec:
      containers:
      - name: app
        image: aicodegen/ai-code-generator:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: FLASK_ENV
          value: "production"
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
