# 开发工具集成界面

import os
import json
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                            QTextEdit, QPushButton, QLabel, QTreeWidget, 
                            QTreeWidgetItem, QSplitter, QProgressBar, QComboBox,
                            QCheckBox, QSpinBox, QGroupBox, QFormLayout, QListWidget,
                            QListWidgetItem, QMessageBox, QFileDialog, QTableWidget,
                            QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon

from tools.version_control import VersionControlManager, VCSType
from tools.code_quality import CodeQualityChecker
from tools.automated_testing import AutomatedTestRunner
from tools.performance_monitor import PerformanceMonitor

class DevToolsIntegrationWidget(QWidget):
    """开发工具集成界面"""
    
    def __init__(self):
        super().__init__()
        self.project_path = os.getcwd()
        self.init_managers()
        self.init_ui()
        self.setup_timers()
    
    def init_managers(self):
        """初始化管理器"""
        self.vcs_manager = VersionControlManager(self.project_path)
        self.quality_checker = CodeQualityChecker(self.project_path)
        self.test_runner = AutomatedTestRunner(self.project_path)
        self.performance_monitor = PerformanceMonitor(self.project_path)
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel('🛠️ 开发工具集成')
        title.setFont(QFont('SimHei', 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建选项卡
        tabs = QTabWidget()
        
        # 版本控制标签页
        vcs_tab = self.create_vcs_tab()
        tabs.addTab(vcs_tab, '🔄 版本控制')
        
        # 代码质量标签页
        quality_tab = self.create_quality_tab()
        tabs.addTab(quality_tab, '🔍 代码质量')
        
        # 自动化测试标签页
        testing_tab = self.create_testing_tab()
        tabs.addTab(testing_tab, '🧪 自动化测试')
        
        # 性能监控标签页
        performance_tab = self.create_performance_tab()
        tabs.addTab(performance_tab, '📊 性能监控')
        
        layout.addWidget(tabs)
    
    def create_vcs_tab(self):
        """创建版本控制标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 仓库状态
        status_group = QGroupBox('仓库状态')
        status_layout = QVBoxLayout(status_group)
        
        self.vcs_status_label = QLabel('检查中...')
        status_layout.addWidget(self.vcs_status_label)
        
        # 刷新按钮
        refresh_btn = QPushButton('刷新状态')
        refresh_btn.clicked.connect(self.refresh_vcs_status)
        status_layout.addWidget(refresh_btn)
        
        layout.addWidget(status_group)
        
        # 文件状态列表
        files_group = QGroupBox('文件状态')
        files_layout = QVBoxLayout(files_group)
        
        self.vcs_files_tree = QTreeWidget()
        self.vcs_files_tree.setHeaderLabels(['文件', '状态'])
        files_layout.addWidget(self.vcs_files_tree)
        
        # 版本控制操作按钮
        vcs_buttons = QHBoxLayout()
        
        self.add_btn = QPushButton('添加文件')
        self.add_btn.clicked.connect(self.add_files)
        
        self.commit_btn = QPushButton('提交更改')
        self.commit_btn.clicked.connect(self.commit_changes)
        
        self.push_btn = QPushButton('推送')
        self.push_btn.clicked.connect(self.push_changes)
        
        self.pull_btn = QPushButton('拉取')
        self.pull_btn.clicked.connect(self.pull_changes)
        
        vcs_buttons.addWidget(self.add_btn)
        vcs_buttons.addWidget(self.commit_btn)
        vcs_buttons.addWidget(self.push_btn)
        vcs_buttons.addWidget(self.pull_btn)
        
        files_layout.addLayout(vcs_buttons)
        layout.addWidget(files_group)
        
        # 提交历史
        history_group = QGroupBox('提交历史')
        history_layout = QVBoxLayout(history_group)
        
        self.commit_history_list = QListWidget()
        history_layout.addWidget(self.commit_history_list)
        
        layout.addWidget(history_group)
        
        # 初始化状态
        self.refresh_vcs_status()
        
        return widget    

    def create_quality_tab(self):
        """创建代码质量标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 质量概览
        overview_group = QGroupBox('质量概览')
        overview_layout = QFormLayout(overview_group)
        
        self.quality_score_label = QLabel('计算中...')
        self.issues_count_label = QLabel('0')
        self.coverage_label = QLabel('0%')
        
        overview_layout.addRow('质量评分:', self.quality_score_label)
        overview_layout.addRow('问题数量:', self.issues_count_label)
        overview_layout.addRow('测试覆盖率:', self.coverage_label)
        
        layout.addWidget(overview_group)
        
        # 质量检查控制
        check_group = QGroupBox('质量检查')
        check_layout = QVBoxLayout(check_group)
        
        check_buttons = QHBoxLayout()
        
        self.check_all_btn = QPushButton('检查所有文件')
        self.check_all_btn.clicked.connect(self.check_all_files)
        
        self.check_current_btn = QPushButton('检查当前文件')
        self.check_current_btn.clicked.connect(self.check_current_file)
        
        self.auto_fix_btn = QPushButton('自动修复')
        self.auto_fix_btn.clicked.connect(self.auto_fix_issues)
        
        check_buttons.addWidget(self.check_all_btn)
        check_buttons.addWidget(self.check_current_btn)
        check_buttons.addWidget(self.auto_fix_btn)
        
        check_layout.addLayout(check_buttons)
        
        # 问题列表
        self.issues_table = QTableWidget()
        self.issues_table.setColumnCount(5)
        self.issues_table.setHorizontalHeaderLabels(['文件', '行号', '类型', '严重程度', '描述'])
        self.issues_table.horizontalHeader().setStretchLastSection(True)
        check_layout.addWidget(self.issues_table)
        
        layout.addWidget(check_group)
        
        return widget
    
    def create_testing_tab(self):
        """创建自动化测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 测试概览
        test_overview_group = QGroupBox('测试概览')
        test_overview_layout = QFormLayout(test_overview_group)
        
        self.test_count_label = QLabel('0')
        self.test_passed_label = QLabel('0')
        self.test_failed_label = QLabel('0')
        self.test_coverage_label = QLabel('0%')
        
        test_overview_layout.addRow('总测试数:', self.test_count_label)
        test_overview_layout.addRow('通过:', self.test_passed_label)
        test_overview_layout.addRow('失败:', self.test_failed_label)
        test_overview_layout.addRow('覆盖率:', self.test_coverage_label)
        
        layout.addWidget(test_overview_group)
        
        # 测试控制
        test_control_group = QGroupBox('测试控制')
        test_control_layout = QVBoxLayout(test_control_group)
        
        test_buttons = QHBoxLayout()
        
        self.run_all_tests_btn = QPushButton('运行所有测试')
        self.run_all_tests_btn.clicked.connect(self.run_all_tests)
        
        self.run_unit_tests_btn = QPushButton('运行单元测试')
        self.run_unit_tests_btn.clicked.connect(self.run_unit_tests)
        
        self.generate_tests_btn = QPushButton('生成测试代码')
        self.generate_tests_btn.clicked.connect(self.generate_test_code)
        
        test_buttons.addWidget(self.run_all_tests_btn)
        test_buttons.addWidget(self.run_unit_tests_btn)
        test_buttons.addWidget(self.generate_tests_btn)
        
        test_control_layout.addLayout(test_buttons)
        
        # 测试结果
        self.test_results_text = QTextEdit()
        self.test_results_text.setReadOnly(True)
        test_control_layout.addWidget(self.test_results_text)
        
        layout.addWidget(test_control_group)
        
        return widget
    
    def create_performance_tab(self):
        """创建性能监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 监控控制
        monitor_control_group = QGroupBox('监控控制')
        monitor_control_layout = QHBoxLayout(monitor_control_group)
        
        self.start_monitor_btn = QPushButton('开始监控')
        self.start_monitor_btn.clicked.connect(self.start_performance_monitoring)
        
        self.stop_monitor_btn = QPushButton('停止监控')
        self.stop_monitor_btn.clicked.connect(self.stop_performance_monitoring)
        self.stop_monitor_btn.setEnabled(False)
        
        self.profile_btn = QPushButton('性能分析')
        self.profile_btn.clicked.connect(self.start_profiling)
        
        monitor_control_layout.addWidget(self.start_monitor_btn)
        monitor_control_layout.addWidget(self.stop_monitor_btn)
        monitor_control_layout.addWidget(self.profile_btn)
        
        layout.addWidget(monitor_control_group)
        
        # 实时指标
        metrics_group = QGroupBox('实时指标')
        metrics_layout = QFormLayout(metrics_group)
        
        self.cpu_label = QLabel('0%')
        self.memory_label = QLabel('0MB')
        self.threads_label = QLabel('0')
        
        metrics_layout.addRow('CPU使用率:', self.cpu_label)
        metrics_layout.addRow('内存使用:', self.memory_label)
        metrics_layout.addRow('线程数:', self.threads_label)
        
        layout.addWidget(metrics_group)
        
        # 性能历史
        history_group = QGroupBox('性能历史')
        history_layout = QVBoxLayout(history_group)
        
        self.performance_text = QTextEdit()
        self.performance_text.setReadOnly(True)
        history_layout.addWidget(self.performance_text)
        
        # 导出按钮
        export_buttons = QHBoxLayout()
        
        self.export_json_btn = QPushButton('导出JSON')
        self.export_json_btn.clicked.connect(lambda: self.export_performance_data('json'))
        
        self.export_csv_btn = QPushButton('导出CSV')
        self.export_csv_btn.clicked.connect(lambda: self.export_performance_data('csv'))
        
        self.export_html_btn = QPushButton('导出HTML')
        self.export_html_btn.clicked.connect(lambda: self.export_performance_data('html'))
        
        export_buttons.addWidget(self.export_json_btn)
        export_buttons.addWidget(self.export_csv_btn)
        export_buttons.addWidget(self.export_html_btn)
        
        history_layout.addLayout(export_buttons)
        layout.addWidget(history_group)
        
        return widget
    
    def setup_timers(self):
        """设置定时器"""
        # VCS状态更新定时器
        self.vcs_timer = QTimer()
        self.vcs_timer.timeout.connect(self.refresh_vcs_status)
        self.vcs_timer.start(30000)  # 30秒更新一次
        
        # 性能监控更新定时器
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.update_performance_display)
        self.performance_timer.start(2000)  # 2秒更新一次
    
    # 版本控制方法
    def refresh_vcs_status(self):
        """刷新版本控制状态"""
        if self.vcs_manager.is_repository():
            vcs_type = self.vcs_manager.vcs_type.value if self.vcs_manager.vcs_type else "未知"
            self.vcs_status_label.setText(f"仓库类型: {vcs_type}")
            
            # 更新文件状态
            self.update_file_status()
            
            # 更新提交历史
            self.update_commit_history()
        else:
            self.vcs_status_label.setText("不是版本控制仓库")
    
    def update_file_status(self):
        """更新文件状态"""
        self.vcs_files_tree.clear()
        
        file_status = self.vcs_manager.get_status()
        for file_info in file_status:
            item = QTreeWidgetItem([file_info.path, file_info.status])
            
            # 根据状态设置颜色
            if file_info.status == 'modified':
                item.setForeground(0, QColor('orange'))
            elif file_info.status == 'added':
                item.setForeground(0, QColor('green'))
            elif file_info.status == 'deleted':
                item.setForeground(0, QColor('red'))
            elif file_info.status == 'untracked':
                item.setForeground(0, QColor('blue'))
            
            self.vcs_files_tree.addTopLevelItem(item)
    
    def update_commit_history(self):
        """更新提交历史"""
        self.commit_history_list.clear()
        
        commits = self.vcs_manager.get_commit_history(10)
        for commit in commits:
            item_text = f"{commit.hash[:8]} - {commit.author} - {commit.message}"
            item = QListWidgetItem(item_text)
            self.commit_history_list.addItem(item)
    
    def add_files(self):
        """添加文件到暂存区"""
        selected_items = self.vcs_files_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '警告', '请选择要添加的文件')
            return
        
        files = [item.text(0) for item in selected_items]
        success, message = self.vcs_manager.add_files(files)
        
        if success:
            QMessageBox.information(self, '成功', message)
            self.refresh_vcs_status()
        else:
            QMessageBox.critical(self, '失败', message)
    
    def commit_changes(self):
        """提交更改"""
        from PyQt5.QtWidgets import QInputDialog
        
        message, ok = QInputDialog.getText(self, '提交更改', '提交信息:')
        if ok and message:
            success, result = self.vcs_manager.commit(message)
            
            if success:
                QMessageBox.information(self, '成功', result)
                self.refresh_vcs_status()
            else:
                QMessageBox.critical(self, '失败', result)
    
    def push_changes(self):
        """推送更改"""
        success, message = self.vcs_manager.push()
        
        if success:
            QMessageBox.information(self, '成功', message)
        else:
            QMessageBox.critical(self, '失败', message)
    
    def pull_changes(self):
        """拉取更改"""
        success, message = self.vcs_manager.pull()
        
        if success:
            QMessageBox.information(self, '成功', message)
            self.refresh_vcs_status()
        else:
            QMessageBox.critical(self, '失败', message)
    
    # 代码质量方法
    def check_all_files(self):
        """检查所有文件"""
        results = self.quality_checker.check_project()
        self.display_quality_results(results)
    
    def check_current_file(self):
        """检查当前文件"""
        # 这里应该获取当前编辑的文件
        current_file = "main.py"  # 示例
        if os.path.exists(current_file):
            issues = self.quality_checker.check_file(current_file)
            self.display_quality_results({current_file: issues})
        else:
            QMessageBox.warning(self, '警告', '当前文件不存在')
    
    def display_quality_results(self, results: Dict):
        """显示质量检查结果"""
        self.issues_table.setRowCount(0)
        
        total_issues = 0
        for file_path, issues in results.items():
            total_issues += len(issues)
            
            for issue in issues:
                row = self.issues_table.rowCount()
                self.issues_table.insertRow(row)
                
                self.issues_table.setItem(row, 0, QTableWidgetItem(os.path.basename(file_path)))
                self.issues_table.setItem(row, 1, QTableWidgetItem(str(issue.line_number)))
                self.issues_table.setItem(row, 2, QTableWidgetItem(issue.issue_type.value))
                self.issues_table.setItem(row, 3, QTableWidgetItem(issue.severity.value))
                self.issues_table.setItem(row, 4, QTableWidgetItem(issue.message))
        
        self.issues_count_label.setText(str(total_issues))
        
        # 更新质量评分
        if total_issues == 0:
            score = "A+"
        elif total_issues < 5:
            score = "A"
        elif total_issues < 10:
            score = "B"
        elif total_issues < 20:
            score = "C"
        else:
            score = "D"
        
        self.quality_score_label.setText(score)
    
    def auto_fix_issues(self):
        """自动修复问题"""
        QMessageBox.information(self, '提示', '自动修复功能正在开发中...')
    
    # 自动化测试方法
    def run_all_tests(self):
        """运行所有测试"""
        self.test_results_text.clear()
        self.test_results_text.append("正在运行所有测试...")
        
        # 在后台线程中运行测试
        self.test_thread = TestRunnerThread(self.test_runner, 'all')
        self.test_thread.results_ready.connect(self.display_test_results)
        self.test_thread.start()
    
    def run_unit_tests(self):
        """运行单元测试"""
        self.test_results_text.clear()
        self.test_results_text.append("正在运行单元测试...")
        
        self.test_thread = TestRunnerThread(self.test_runner, 'unit')
        self.test_thread.results_ready.connect(self.display_test_results)
        self.test_thread.start()
    
    def display_test_results(self, results: Dict):
        """显示测试结果"""
        self.test_results_text.clear()
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for framework, suite in results.items():
            self.test_results_text.append(f"=== {framework.upper()} 测试结果 ===")
            self.test_results_text.append(f"执行时间: {suite.total_time:.2f}秒")
            
            for test in suite.tests:
                total_tests += 1
                if test.status.value == 'passed':
                    passed_tests += 1
                    status_icon = "✅"
                elif test.status.value == 'failed':
                    failed_tests += 1
                    status_icon = "❌"
                else:
                    status_icon = "⏭️"
                
                self.test_results_text.append(f"{status_icon} {test.name}")
                if test.message:
                    self.test_results_text.append(f"   {test.message}")
            
            self.test_results_text.append("")
        
        # 更新统计信息
        self.test_count_label.setText(str(total_tests))
        self.test_passed_label.setText(str(passed_tests))
        self.test_failed_label.setText(str(failed_tests))
        
        # 计算通过率
        if total_tests > 0:
            pass_rate = (passed_tests / total_tests) * 100
            self.test_results_text.append(f"通过率: {pass_rate:.1f}%")
    
    def generate_test_code(self):
        """生成测试代码"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择源文件', '', 'Python文件 (*.py)')
        if file_path:
            test_code = self.test_runner.generate_test_code(file_path)
            
            # 保存测试代码
            test_file_path = file_path.replace('.py', '_test.py')
            save_path, _ = QFileDialog.getSaveFileName(self, '保存测试文件', test_file_path, 'Python文件 (*.py)')
            
            if save_path:
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(test_code)
                QMessageBox.information(self, '成功', f'测试代码已保存到: {save_path}')
    
    # 性能监控方法
    def start_performance_monitoring(self):
        """开始性能监控"""
        self.performance_monitor.start_monitoring()
        self.start_monitor_btn.setEnabled(False)
        self.stop_monitor_btn.setEnabled(True)
        self.performance_text.append("性能监控已启动")
    
    def stop_performance_monitoring(self):
        """停止性能监控"""
        self.performance_monitor.stop_monitoring()
        self.start_monitor_btn.setEnabled(True)
        self.stop_monitor_btn.setEnabled(False)
        self.performance_text.append("性能监控已停止")
    
    def update_performance_display(self):
        """更新性能显示"""
        current_metrics = self.performance_monitor.get_current_metrics()
        if current_metrics:
            self.cpu_label.setText(f"{current_metrics.cpu_percent:.1f}%")
            self.memory_label.setText(f"{current_metrics.memory_usage:.1f}MB")
            self.threads_label.setText(str(current_metrics.thread_count))
    
    def start_profiling(self):
        """开始性能分析"""
        self.performance_monitor.start_profiling()
        QMessageBox.information(self, '提示', '性能分析已启动，运行一些代码后点击停止分析')
    
    def export_performance_data(self, format_type: str):
        """导出性能数据"""
        try:
            data = self.performance_monitor.export_metrics(format_type, 60)
            
            file_extension = {'json': '.json', 'csv': '.csv', 'html': '.html'}[format_type]
            file_path, _ = QFileDialog.getSaveFileName(
                self, f'导出{format_type.upper()}', f'performance_data{file_extension}',
                f'{format_type.upper()}文件 (*{file_extension})'
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(data)
                QMessageBox.information(self, '成功', f'性能数据已导出到: {file_path}')
        
        except Exception as e:
            QMessageBox.critical(self, '错误', f'导出失败: {str(e)}')

class TestRunnerThread(QThread):
    """测试运行线程"""
    results_ready = pyqtSignal(dict)
    
    def __init__(self, test_runner, test_type):
        super().__init__()
        self.test_runner = test_runner
        self.test_type = test_type
    
    def run(self):
        """运行测试"""
        try:
            if self.test_type == 'all':
                results = self.test_runner.run_tests()
            else:
                # 运行特定类型的测试
                test_files = self.test_runner.discover_tests()
                results = self.test_runner.run_tests(test_files)
            
            self.results_ready.emit(results)
        except Exception as e:
            # 发送错误结果
            error_result = {
                'error': {
                    'tests': [],
                    'total_time': 0,
                    'error_message': str(e)
                }
            }
            self.results_ready.emit(error_result)