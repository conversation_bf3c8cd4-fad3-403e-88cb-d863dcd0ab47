version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PYTHONPATH=/app
      - FLASK_ENV=production
    depends_on:
      - redis
      - postgres
    networks:
      - ai-code-generator
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-code-generator
    restart: unless-stopped

  postgres:
    image: postgres:13-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=aicodegen
      - POSTGRES_USER=app
      - POSTGRES_PASSWORD=changeme
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ai-code-generator
    restart: unless-stopped

networks:
  ai-code-generator:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
