#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的打包管理器
支持多平台打包、自动化构建和部署
"""

import os
import sys
import json
import subprocess
import shutil
import zipfile
import tarfile
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import tempfile
import time

class PackageFormat(Enum):
    """打包格式"""
    EXECUTABLE = "executable"  # 可执行文件
    INSTALLER = "installer"    # 安装包
    PORTABLE = "portable"      # 便携版
    CONTAINER = "container"    # 容器镜像
    LIBRARY = "library"        # 库包

class Platform(Enum):
    """目标平台"""
    WINDOWS_X64 = "windows-x64"
    WINDOWS_X86 = "windows-x86"
    LINUX_X64 = "linux-x64"
    LINUX_ARM64 = "linux-arm64"
    MACOS_X64 = "macos-x64"
    MACOS_ARM64 = "macos-arm64"
    WEB = "web"
    ANDROID = "android"
    IOS = "ios"

@dataclass
class PackageConfig:
    """打包配置"""
    name: str
    version: str
    description: str
    author: str
    main_file: str
    output_dir: str = "dist"
    platforms: List[Platform] = None
    formats: List[PackageFormat] = None
    include_files: List[str] = None
    exclude_files: List[str] = None
    dependencies: List[str] = None
    icon: Optional[str] = None
    license: Optional[str] = None
    homepage: Optional[str] = None
    keywords: List[str] = None
    
    def __post_init__(self):
        if self.platforms is None:
            self.platforms = [Platform.WINDOWS_X64]
        if self.formats is None:
            self.formats = [PackageFormat.EXECUTABLE]
        if self.include_files is None:
            self.include_files = []
        if self.exclude_files is None:
            self.exclude_files = []
        if self.dependencies is None:
            self.dependencies = []
        if self.keywords is None:
            self.keywords = []

class EnhancedPackager:
    """增强的打包管理器"""
    
    def __init__(self):
        self.config: Optional[PackageConfig] = None
        self.build_tools = self.detect_build_tools()
        self.package_history: List[Dict[str, Any]] = []
        
    def detect_build_tools(self) -> Dict[str, str]:
        """检测可用的构建工具"""
        tools = {}
        
        # Python打包工具
        if shutil.which("pyinstaller"):
            tools["pyinstaller"] = shutil.which("pyinstaller")
        if shutil.which("cx_Freeze"):
            tools["cx_freeze"] = shutil.which("cx_Freeze")
        if shutil.which("nuitka"):
            tools["nuitka"] = shutil.which("nuitka")
        
        # 容器工具
        if shutil.which("docker"):
            tools["docker"] = shutil.which("docker")
        if shutil.which("podman"):
            tools["podman"] = shutil.which("podman")
        
        # 系统打包工具
        if shutil.which("makensis"):  # NSIS for Windows
            tools["nsis"] = shutil.which("makensis")
        if shutil.which("dpkg-deb"):  # Debian packages
            tools["dpkg"] = shutil.which("dpkg-deb")
        if shutil.which("rpmbuild"):  # RPM packages
            tools["rpm"] = shutil.which("rpmbuild")
        
        # Web打包工具
        if shutil.which("npm"):
            tools["npm"] = shutil.which("npm")
        if shutil.which("webpack"):
            tools["webpack"] = shutil.which("webpack")
        
        return tools
    
    def load_config(self, config_file: str) -> bool:
        """加载打包配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 转换枚举值
            if 'platforms' in config_data:
                config_data['platforms'] = [Platform(p) for p in config_data['platforms']]
            if 'formats' in config_data:
                config_data['formats'] = [PackageFormat(f) for f in config_data['formats']]
            
            self.config = PackageConfig(**config_data)
            return True
        except Exception as e:
            print(f"加载配置失败: {e}")
            return False
    
    def save_config(self, config_file: str) -> bool:
        """保存打包配置"""
        if not self.config:
            return False
        
        try:
            config_data = asdict(self.config)
            
            # 转换枚举值为字符串
            config_data['platforms'] = [p.value for p in self.config.platforms]
            config_data['formats'] = [f.value for f in self.config.formats]
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def create_default_config(self, project_path: str) -> PackageConfig:
        """创建默认配置"""
        project_name = os.path.basename(project_path)
        main_file = self.find_main_file(project_path)
        
        config = PackageConfig(
            name=project_name,
            version="1.0.0",
            description=f"{project_name} - AI生成的应用程序",
            author="AI Code Generator",
            main_file=main_file,
            platforms=[Platform.WINDOWS_X64, Platform.LINUX_X64],
            formats=[PackageFormat.EXECUTABLE, PackageFormat.PORTABLE]
        )
        
        self.config = config
        return config
    
    def find_main_file(self, project_path: str) -> str:
        """查找主文件"""
        common_names = ["main.py", "app.py", "run.py", "__main__.py", "index.js", "main.js"]
        
        for name in common_names:
            file_path = os.path.join(project_path, name)
            if os.path.exists(file_path):
                return name
        
        # 查找第一个Python文件
        for file in os.listdir(project_path):
            if file.endswith('.py') and not file.startswith('_'):
                return file
        
        return "main.py"
    
    async def package_project(self, project_path: str, progress_callback=None) -> Dict[str, Any]:
        """打包项目"""
        if not self.config:
            self.create_default_config(project_path)
        
        start_time = time.time()
        results = {}
        total_tasks = len(self.config.platforms) * len(self.config.formats)
        completed_tasks = 0
        
        try:
            # 准备构建环境
            build_dir = self.prepare_build_environment(project_path)
            
            for platform in self.config.platforms:
                for format_type in self.config.formats:
                    if progress_callback:
                        progress = int((completed_tasks / total_tasks) * 100)
                        progress_callback(progress, f"打包 {platform.value} - {format_type.value}")
                    
                    try:
                        result = await self.package_for_platform_format(
                            build_dir, platform, format_type
                        )
                        results[f"{platform.value}_{format_type.value}"] = result
                    except Exception as e:
                        results[f"{platform.value}_{format_type.value}"] = {
                            "success": False,
                            "error": str(e)
                        }
                    
                    completed_tasks += 1
            
            # 清理构建目录
            if os.path.exists(build_dir):
                shutil.rmtree(build_dir)
            
            # 记录打包历史
            package_record = {
                "timestamp": time.time(),
                "config": asdict(self.config),
                "results": results,
                "duration": time.time() - start_time
            }
            self.package_history.append(package_record)
            
            if progress_callback:
                progress_callback(100, "打包完成")
            
            return {
                "success": True,
                "results": results,
                "duration": time.time() - start_time
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "duration": time.time() - start_time
            }
    
    def prepare_build_environment(self, project_path: str) -> str:
        """准备构建环境"""
        build_dir = tempfile.mkdtemp(prefix="ai_package_")
        
        # 复制项目文件
        for item in os.listdir(project_path):
            if item in self.config.exclude_files:
                continue
            
            src = os.path.join(project_path, item)
            dst = os.path.join(build_dir, item)
            
            if os.path.isdir(src):
                shutil.copytree(src, dst, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            else:
                shutil.copy2(src, dst)
        
        # 复制额外文件
        for file_path in self.config.include_files:
            if os.path.exists(file_path):
                dst = os.path.join(build_dir, os.path.basename(file_path))
                if os.path.isdir(file_path):
                    shutil.copytree(file_path, dst)
                else:
                    shutil.copy2(file_path, dst)
        
        return build_dir
    
    async def package_for_platform_format(self, build_dir: str, platform: Platform, format_type: PackageFormat) -> Dict[str, Any]:
        """为特定平台和格式打包"""
        if format_type == PackageFormat.EXECUTABLE:
            return await self.create_executable(build_dir, platform)
        elif format_type == PackageFormat.INSTALLER:
            return await self.create_installer(build_dir, platform)
        elif format_type == PackageFormat.PORTABLE:
            return await self.create_portable(build_dir, platform)
        elif format_type == PackageFormat.CONTAINER:
            return await self.create_container(build_dir, platform)
        elif format_type == PackageFormat.LIBRARY:
            return await self.create_library(build_dir, platform)
        else:
            raise ValueError(f"不支持的打包格式: {format_type}")
    
    async def create_executable(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """创建可执行文件"""
        if "pyinstaller" in self.build_tools:
            return await self.create_executable_pyinstaller(build_dir, platform)
        elif "nuitka" in self.build_tools:
            return await self.create_executable_nuitka(build_dir, platform)
        else:
            raise Exception("未找到可用的可执行文件构建工具")
    
    async def create_executable_pyinstaller(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """使用PyInstaller创建可执行文件"""
        main_file = os.path.join(build_dir, self.config.main_file)
        output_dir = os.path.join(self.config.output_dir, platform.value)
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [
            self.build_tools["pyinstaller"],
            "--onefile",
            "--distpath", output_dir,
            "--workpath", os.path.join(build_dir, "build"),
            "--specpath", build_dir,
            "--name", self.config.name
        ]
        
        if self.config.icon:
            cmd.extend(["--icon", self.config.icon])
        
        if platform == Platform.WINDOWS_X64 or platform == Platform.WINDOWS_X86:
            cmd.append("--windowed")
        
        cmd.append(main_file)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=build_dir)
            
            if result.returncode == 0:
                exe_name = self.config.name
                if platform.value.startswith("windows"):
                    exe_name += ".exe"
                
                exe_path = os.path.join(output_dir, exe_name)
                return {
                    "success": True,
                    "output_path": exe_path,
                    "size": os.path.getsize(exe_path) if os.path.exists(exe_path) else 0,
                    "build_log": result.stdout
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr,
                    "build_log": result.stdout
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def create_executable_nuitka(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """使用Nuitka创建可执行文件"""
        # 简化实现
        return {
            "success": False,
            "error": "Nuitka支持尚未实现"
        }
    
    async def create_installer(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """创建安装包"""
        if platform.value.startswith("windows") and "nsis" in self.build_tools:
            return await self.create_windows_installer(build_dir)
        elif platform.value.startswith("linux") and "dpkg" in self.build_tools:
            return await self.create_deb_package(build_dir)
        else:
            return {
                "success": False,
                "error": f"不支持为{platform.value}创建安装包"
            }
    
    async def create_windows_installer(self, build_dir: str) -> Dict[str, Any]:
        """创建Windows安装包"""
        # 简化实现
        return {
            "success": False,
            "error": "Windows安装包创建尚未实现"
        }
    
    async def create_deb_package(self, build_dir: str) -> Dict[str, Any]:
        """创建Debian包"""
        # 简化实现
        return {
            "success": False,
            "error": "Debian包创建尚未实现"
        }
    
    async def create_portable(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """创建便携版"""
        output_dir = os.path.join(self.config.output_dir, platform.value)
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建便携版目录结构
        portable_dir = os.path.join(output_dir, f"{self.config.name}_portable")
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        
        shutil.copytree(build_dir, portable_dir)
        
        # 创建启动脚本
        if platform.value.startswith("windows"):
            script_content = f'''@echo off
cd /d "%~dp0"
python {self.config.main_file}
pause'''
            script_path = os.path.join(portable_dir, f"{self.config.name}.bat")
        else:
            script_content = f'''#!/bin/bash
cd "$(dirname "$0")"
python3 {self.config.main_file}'''
            script_path = os.path.join(portable_dir, f"{self.config.name}.sh")
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        if not platform.value.startswith("windows"):
            os.chmod(script_path, 0o755)
        
        # 创建压缩包
        archive_name = f"{self.config.name}_{self.config.version}_{platform.value}_portable"
        if platform.value.startswith("windows"):
            archive_path = os.path.join(output_dir, f"{archive_name}.zip")
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                for root, dirs, files in os.walk(portable_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, portable_dir)
                        zf.write(file_path, arc_path)
        else:
            archive_path = os.path.join(output_dir, f"{archive_name}.tar.gz")
            with tarfile.open(archive_path, 'w:gz') as tf:
                tf.add(portable_dir, arcname=os.path.basename(portable_dir))
        
        return {
            "success": True,
            "output_path": archive_path,
            "size": os.path.getsize(archive_path),
            "portable_dir": portable_dir
        }
    
    async def create_container(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """创建容器镜像"""
        if "docker" not in self.build_tools:
            return {
                "success": False,
                "error": "Docker未安装"
            }
        
        # 创建Dockerfile
        dockerfile_content = f'''FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "{self.config.main_file}"]'''
        
        dockerfile_path = os.path.join(build_dir, "Dockerfile")
        with open(dockerfile_path, 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)
        
        # 构建镜像
        image_name = f"{self.config.name.lower()}:{self.config.version}"
        cmd = [
            self.build_tools["docker"],
            "build",
            "-t", image_name,
            build_dir
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "image_name": image_name,
                    "build_log": result.stdout
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr,
                    "build_log": result.stdout
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def create_library(self, build_dir: str, platform: Platform) -> Dict[str, Any]:
        """创建库包"""
        # 简化实现
        return {
            "success": False,
            "error": "库包创建尚未实现"
        }
