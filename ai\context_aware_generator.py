# 上下文感知代码生成器

import ast
import os
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

@dataclass
class CodeContext:
    """代码上下文信息"""
    project_type: str = "general"  # web, desktop, cli, library, etc.
    language: str = "python"
    framework: str = ""  # flask, django, tkinter, etc.
    existing_code: str = ""
    imports: List[str] = None
    functions: List[Dict] = None
    classes: List[Dict] = None
    variables: List[Dict] = None
    dependencies: List[str] = None
    coding_style: Dict = None
    file_structure: Dict = None
    recent_changes: List[str] = None
    
    def __post_init__(self):
        if self.imports is None:
            self.imports = []
        if self.functions is None:
            self.functions = []
        if self.classes is None:
            self.classes = []
        if self.variables is None:
            self.variables = []
        if self.dependencies is None:
            self.dependencies = []
        if self.coding_style is None:
            self.coding_style = {}
        if self.file_structure is None:
            self.file_structure = {}
        if self.recent_changes is None:
            self.recent_changes = []

class ProjectType(Enum):
    WEB_APP = "web_app"
    DESKTOP_APP = "desktop_app"
    CLI_TOOL = "cli_tool"
    LIBRARY = "library"
    DATA_SCIENCE = "data_science"
    GAME = "game"
    API_SERVICE = "api_service"
    AUTOMATION = "automation"

class ContextAwareGenerator:
    """上下文感知代码生成器"""
    
    def __init__(self):
        self.context_analyzers = {
            'python': PythonContextAnalyzer(),
            'javascript': JavaScriptContextAnalyzer(),
            'java': JavaContextAnalyzer(),
            'cpp': CppContextAnalyzer()
        }
        
        self.project_templates = {
            ProjectType.WEB_APP: WebAppTemplate(),
            ProjectType.DESKTOP_APP: DesktopAppTemplate(),
            ProjectType.CLI_TOOL: CLIToolTemplate(),
            ProjectType.LIBRARY: LibraryTemplate(),
            ProjectType.DATA_SCIENCE: DataScienceTemplate(),
            ProjectType.API_SERVICE: APIServiceTemplate()
        }
    
    def analyze_context(self, code: str, language: str = "python", 
                       project_path: str = None) -> CodeContext:
        """分析代码上下文"""
        analyzer = self.context_analyzers.get(language)
        if not analyzer:
            return CodeContext(language=language, existing_code=code)
        
        context = analyzer.analyze(code)
        
        # 分析项目结构
        if project_path and os.path.exists(project_path):
            context.file_structure = self._analyze_project_structure(project_path)
            context.project_type = self._detect_project_type(context.file_structure)
        
        return context
    
    def generate_contextual_prompt(self, request: str, context: CodeContext) -> str:
        """生成上下文感知的提示"""
        prompt_parts = []
        
        # 基础信息
        prompt_parts.append(f"项目类型: {context.project_type}")
        prompt_parts.append(f"编程语言: {context.language}")
        
        if context.framework:
            prompt_parts.append(f"使用框架: {context.framework}")
        
        # 现有代码上下文
        if context.existing_code:
            prompt_parts.append("\\n现有代码上下文:")
            prompt_parts.append(self._extract_code_summary(context.existing_code))
        
        # 导入和依赖
        if context.imports:
            prompt_parts.append(f"\\n已导入模块: {', '.join(context.imports[:10])}")
        
        if context.dependencies:
            prompt_parts.append(f"项目依赖: {', '.join(context.dependencies[:10])}")
        
        # 现有函数和类
        if context.functions:
            func_names = [f['name'] for f in context.functions[:5]]
            prompt_parts.append(f"现有函数: {', '.join(func_names)}")
        
        if context.classes:
            class_names = [c['name'] for c in context.classes[:5]]
            prompt_parts.append(f"现有类: {', '.join(class_names)}")
        
        # 编码风格
        if context.coding_style:
            style_info = []
            if context.coding_style.get('indent_style'):
                style_info.append(f"缩进: {context.coding_style['indent_style']}")
            if context.coding_style.get('naming_convention'):
                style_info.append(f"命名: {context.coding_style['naming_convention']}")
            if style_info:
                prompt_parts.append(f"编码风格: {', '.join(style_info)}")
        
        # 最近修改
        if context.recent_changes:
            prompt_parts.append(f"\\n最近修改: {'; '.join(context.recent_changes[:3])}")
        
        # 用户请求
        prompt_parts.append(f"\\n用户需求: {request}")
        
        # 生成要求
        prompt_parts.append("\\n请根据以上上下文生成代码，要求:")
        prompt_parts.append("1. 保持与现有代码风格一致")
        prompt_parts.append("2. 复用现有的函数和类")
        prompt_parts.append("3. 遵循项目的架构模式")
        prompt_parts.append("4. 添加适当的错误处理")
        prompt_parts.append("5. 包含必要的文档和注释")
        
        return "\\n".join(prompt_parts)
    
    def suggest_improvements(self, context: CodeContext) -> List[str]:
        """基于上下文建议改进"""
        suggestions = []
        
        # 检查导入优化
        if len(context.imports) > 20:
            suggestions.append("考虑重构导入，减少不必要的依赖")
        
        # 检查函数复杂度
        complex_functions = [f for f in context.functions if f.get('complexity', 0) > 10]
        if complex_functions:
            suggestions.append(f"建议重构复杂函数: {', '.join([f['name'] for f in complex_functions[:3]])}")
        
        # 检查代码重复
        if self._detect_code_duplication(context):
            suggestions.append("检测到代码重复，建议提取公共函数")
        
        # 检查错误处理
        if not self._has_error_handling(context):
            suggestions.append("建议添加错误处理和异常捕获")
        
        # 检查文档
        undocumented = [f for f in context.functions if not f.get('docstring')]
        if len(undocumented) > len(context.functions) * 0.5:
            suggestions.append("建议为函数添加文档字符串")
        
        return suggestions
    
    def _analyze_project_structure(self, project_path: str) -> Dict:
        """分析项目结构"""
        structure = {
            'files': [],
            'directories': [],
            'config_files': [],
            'test_files': [],
            'doc_files': []
        }
        
        for root, dirs, files in os.walk(project_path):
            # 跳过隐藏目录和常见的忽略目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            rel_root = os.path.relpath(root, project_path)
            if rel_root != '.':
                structure['directories'].append(rel_root)
            
            for file in files:
                if file.startswith('.'):
                    continue
                
                file_path = os.path.join(rel_root, file) if rel_root != '.' else file
                structure['files'].append(file_path)
                
                # 分类文件
                if file in ['requirements.txt', 'package.json', 'Dockerfile', 'docker-compose.yml']:
                    structure['config_files'].append(file_path)
                elif 'test' in file.lower() or file.startswith('test_'):
                    structure['test_files'].append(file_path)
                elif file.endswith(('.md', '.rst', '.txt')):
                    structure['doc_files'].append(file_path)
        
        return structure
    
    def _detect_project_type(self, file_structure: Dict) -> str:
        """检测项目类型"""
        files = file_structure.get('files', [])
        
        # Web应用检测
        web_indicators = ['app.py', 'wsgi.py', 'manage.py', 'package.json', 'index.html']
        if any(indicator in files for indicator in web_indicators):
            return ProjectType.WEB_APP.value
        
        # 桌面应用检测
        desktop_indicators = ['main.py', 'gui.py', '__main__.py']
        if any(indicator in files for indicator in desktop_indicators):
            return ProjectType.DESKTOP_APP.value
        
        # CLI工具检测
        cli_indicators = ['cli.py', 'command.py', 'console.py']
        if any(indicator in files for indicator in cli_indicators):
            return ProjectType.CLI_TOOL.value
        
        # 库项目检测
        lib_indicators = ['setup.py', 'pyproject.toml', '__init__.py']
        if any(indicator in files for indicator in lib_indicators):
            return ProjectType.LIBRARY.value
        
        # 数据科学检测
        ds_indicators = ['.ipynb', 'data/', 'models/', 'notebooks/']
        if any(any(indicator in f for f in files) for indicator in ds_indicators):
            return ProjectType.DATA_SCIENCE.value
        
        return "general"
    
    def _extract_code_summary(self, code: str, max_lines: int = 20) -> str:
        """提取代码摘要"""
        lines = code.split('\\n')
        
        # 提取重要行（类定义、函数定义、导入等）
        important_lines = []
        for line in lines:
            stripped = line.strip()
            if (stripped.startswith(('import ', 'from ', 'class ', 'def ', '@')) or
                stripped.startswith('#') and len(stripped) > 10):
                important_lines.append(line)
                if len(important_lines) >= max_lines:
                    break
        
        return '\\n'.join(important_lines)
    
    def _detect_code_duplication(self, context: CodeContext) -> bool:
        """检测代码重复"""
        # 简化的重复检测逻辑
        function_bodies = [f.get('body', '') for f in context.functions]
        
        for i, body1 in enumerate(function_bodies):
            for j, body2 in enumerate(function_bodies[i+1:], i+1):
                if len(body1) > 50 and len(body2) > 50:
                    # 计算相似度（简化版）
                    similarity = self._calculate_similarity(body1, body2)
                    if similarity > 0.8:
                        return True
        
        return False
    
    def _has_error_handling(self, context: CodeContext) -> bool:
        """检查是否有错误处理"""
        code = context.existing_code.lower()
        error_keywords = ['try:', 'except:', 'raise', 'assert', 'error', 'exception']
        return any(keyword in code for keyword in error_keywords)
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 简化的相似度计算
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)

class PythonContextAnalyzer:
    """Python代码上下文分析器"""
    
    def analyze(self, code: str) -> CodeContext:
        """分析Python代码上下文"""
        context = CodeContext(language="python", existing_code=code)
        
        try:
            tree = ast.parse(code)
            
            # 分析导入
            context.imports = self._extract_imports(tree)
            
            # 分析函数
            context.functions = self._extract_functions(tree)
            
            # 分析类
            context.classes = self._extract_classes(tree)
            
            # 分析变量
            context.variables = self._extract_variables(tree)
            
            # 检测框架
            context.framework = self._detect_framework(context.imports)
            
            # 分析编码风格
            context.coding_style = self._analyze_coding_style(code)
            
        except SyntaxError:
            # 如果代码有语法错误，进行基础分析
            context.imports = self._extract_imports_regex(code)
            context.framework = self._detect_framework(context.imports)
        
        return context
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """提取导入信息"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")
        
        return imports
    
    def _extract_functions(self, tree: ast.AST) -> List[Dict]:
        """提取函数信息"""
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                func_info = {
                    'name': node.name,
                    'line': node.lineno,
                    'args': [arg.arg for arg in node.args.args],
                    'docstring': ast.get_docstring(node),
                    'complexity': self._calculate_complexity(node),
                    'decorators': [d.id if isinstance(d, ast.Name) else str(d) for d in node.decorator_list]
                }
                functions.append(func_info)
        
        return functions
    
    def _extract_classes(self, tree: ast.AST) -> List[Dict]:
        """提取类信息"""
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_info = {
                    'name': node.name,
                    'line': node.lineno,
                    'bases': [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases],
                    'docstring': ast.get_docstring(node),
                    'methods': [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                }
                classes.append(class_info)
        
        return classes
    
    def _extract_variables(self, tree: ast.AST) -> List[Dict]:
        """提取变量信息"""
        variables = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        var_info = {
                            'name': target.id,
                            'line': node.lineno,
                            'type': self._infer_type(node.value)
                        }
                        variables.append(var_info)
        
        return variables
    
    def _detect_framework(self, imports: List[str]) -> str:
        """检测使用的框架"""
        framework_patterns = {
            'flask': ['flask'],
            'django': ['django'],
            'fastapi': ['fastapi'],
            'tkinter': ['tkinter'],
            'pyqt': ['PyQt5', 'PyQt6'],
            'kivy': ['kivy'],
            'pandas': ['pandas'],
            'numpy': ['numpy'],
            'tensorflow': ['tensorflow'],
            'pytorch': ['torch']
        }
        
        for framework, patterns in framework_patterns.items():
            if any(any(pattern.lower() in imp.lower() for pattern in patterns) for imp in imports):
                return framework
        
        return ""
    
    def _analyze_coding_style(self, code: str) -> Dict:
        """分析编码风格"""
        style = {}
        
        lines = code.split('\\n')
        
        # 分析缩进
        indents = []
        for line in lines:
            if line.strip() and line.startswith(' '):
                indent = len(line) - len(line.lstrip())
                if indent > 0:
                    indents.append(indent)
        
        if indents:
            common_indent = max(set(indents), key=indents.count)
            style['indent_style'] = f"{common_indent} spaces"
        
        # 分析命名约定
        function_names = re.findall(r'def\\s+([a-zA-Z_][a-zA-Z0-9_]*)', code)
        if function_names:
            if all('_' in name or name.islower() for name in function_names):
                style['naming_convention'] = 'snake_case'
            elif all(name[0].islower() and any(c.isupper() for c in name[1:]) for name in function_names):
                style['naming_convention'] = 'camelCase'
        
        # 分析注释风格
        comment_lines = [line for line in lines if line.strip().startswith('#')]
        if comment_lines:
            style['comment_style'] = 'hash_comments'
        
        return style
    
    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """计算函数复杂度（简化版）"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _infer_type(self, node: ast.AST) -> str:
        """推断变量类型"""
        if isinstance(node, ast.Constant):
            return type(node.value).__name__
        elif isinstance(node, ast.List):
            return 'list'
        elif isinstance(node, ast.Dict):
            return 'dict'
        elif isinstance(node, ast.Call):
            if isinstance(node.func, ast.Name):
                return node.func.id
        
        return 'unknown'
    
    def _extract_imports_regex(self, code: str) -> List[str]:
        """使用正则表达式提取导入（备用方法）"""
        imports = []
        
        # 匹配 import 语句
        import_pattern = r'^\\s*import\\s+([a-zA-Z_][a-zA-Z0-9_.,\\s]*)'
        from_pattern = r'^\\s*from\\s+([a-zA-Z_][a-zA-Z0-9_.]*)\\s+import'
        
        for line in code.split('\\n'):
            import_match = re.match(import_pattern, line)
            if import_match:
                imports.extend([imp.strip() for imp in import_match.group(1).split(',')])
            
            from_match = re.match(from_pattern, line)
            if from_match:
                imports.append(from_match.group(1))
        
        return imports

class JavaScriptContextAnalyzer:
    """JavaScript代码上下文分析器"""
    
    def analyze(self, code: str) -> CodeContext:
        """分析JavaScript代码上下文"""
        context = CodeContext(language="javascript", existing_code=code)
        
        # 提取导入/require
        context.imports = self._extract_imports(code)
        
        # 提取函数
        context.functions = self._extract_functions(code)
        
        # 提取类
        context.classes = self._extract_classes(code)
        
        # 检测框架
        context.framework = self._detect_framework(context.imports, code)
        
        return context
    
    def _extract_imports(self, code: str) -> List[str]:
        """提取JavaScript导入"""
        imports = []
        
        # ES6 imports
        import_pattern = r'import\\s+.*?from\\s+[\'"]([^\'"]+)[\'"]'
        imports.extend(re.findall(import_pattern, code))
        
        # CommonJS requires
        require_pattern = r'require\\s*\\(\\s*[\'"]([^\'"]+)[\'"]\\s*\\)'
        imports.extend(re.findall(require_pattern, code))
        
        return imports
    
    def _extract_functions(self, code: str) -> List[Dict]:
        """提取JavaScript函数"""
        functions = []
        
        # 函数声明
        func_pattern = r'function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*\\('
        for match in re.finditer(func_pattern, code):
            functions.append({
                'name': match.group(1),
                'type': 'function_declaration'
            })
        
        # 箭头函数
        arrow_pattern = r'const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=\\s*\\([^)]*\\)\\s*=>'
        for match in re.finditer(arrow_pattern, code):
            functions.append({
                'name': match.group(1),
                'type': 'arrow_function'
            })
        
        return functions
    
    def _extract_classes(self, code: str) -> List[Dict]:
        """提取JavaScript类"""
        classes = []
        
        class_pattern = r'class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)(?:\\s+extends\\s+([a-zA-Z_$][a-zA-Z0-9_$]*))?'
        for match in re.finditer(class_pattern, code):
            classes.append({
                'name': match.group(1),
                'extends': match.group(2) if match.group(2) else None
            })
        
        return classes
    
    def _detect_framework(self, imports: List[str], code: str) -> str:
        """检测JavaScript框架"""
        framework_patterns = {
            'react': ['react', 'jsx'],
            'vue': ['vue'],
            'angular': ['@angular'],
            'express': ['express'],
            'node': ['fs', 'path', 'http'],
            'jquery': ['jquery', '$']
        }
        
        for framework, patterns in framework_patterns.items():
            if any(any(pattern in imp.lower() for pattern in patterns) for imp in imports):
                return framework
            
            # 检查代码中的特征
            if framework == 'jquery' and '$(' in code:
                return framework
            elif framework == 'react' and 'JSX' in code:
                return framework
        
        return ""

class JavaContextAnalyzer:
    """Java代码上下文分析器"""
    
    def analyze(self, code: str) -> CodeContext:
        """分析Java代码上下文"""
        context = CodeContext(language="java", existing_code=code)
        
        # 提取包和导入
        context.imports = self._extract_imports(code)
        
        # 提取类
        context.classes = self._extract_classes(code)
        
        # 提取方法
        context.functions = self._extract_methods(code)
        
        # 检测框架
        context.framework = self._detect_framework(context.imports)
        
        return context
    
    def _extract_imports(self, code: str) -> List[str]:
        """提取Java导入"""
        imports = []
        
        import_pattern = r'import\\s+(?:static\\s+)?([a-zA-Z_][a-zA-Z0-9_.]*(?:\\.\\*)?);'
        imports.extend(re.findall(import_pattern, code))
        
        return imports
    
    def _extract_classes(self, code: str) -> List[Dict]:
        """提取Java类"""
        classes = []
        
        class_pattern = r'(?:public\\s+|private\\s+|protected\\s+)?class\\s+([a-zA-Z_][a-zA-Z0-9_]*)(?:\\s+extends\\s+([a-zA-Z_][a-zA-Z0-9_]*))?'
        for match in re.finditer(class_pattern, code):
            classes.append({
                'name': match.group(1),
                'extends': match.group(2) if match.group(2) else None
            })
        
        return classes
    
    def _extract_methods(self, code: str) -> List[Dict]:
        """提取Java方法"""
        methods = []
        
        method_pattern = r'(?:public\\s+|private\\s+|protected\\s+)?(?:static\\s+)?(?:[a-zA-Z_][a-zA-Z0-9_<>\\[\\]]*\\s+)?([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\([^)]*\\)\\s*\\{'
        for match in re.finditer(method_pattern, code):
            methods.append({
                'name': match.group(1),
                'type': 'method'
            })
        
        return methods
    
    def _detect_framework(self, imports: List[str]) -> str:
        """检测Java框架"""
        framework_patterns = {
            'spring': ['org.springframework'],
            'hibernate': ['org.hibernate'],
            'junit': ['org.junit'],
            'android': ['android.'],
            'javafx': ['javafx.']
        }
        
        for framework, patterns in framework_patterns.items():
            if any(any(pattern in imp for pattern in patterns) for imp in imports):
                return framework
        
        return ""

class CppContextAnalyzer:
    """C++代码上下文分析器"""
    
    def analyze(self, code: str) -> CodeContext:
        """分析C++代码上下文"""
        context = CodeContext(language="cpp", existing_code=code)
        
        # 提取包含文件
        context.imports = self._extract_includes(code)
        
        # 提取函数
        context.functions = self._extract_functions(code)
        
        # 提取类
        context.classes = self._extract_classes(code)
        
        return context
    
    def _extract_includes(self, code: str) -> List[str]:
        """提取C++包含文件"""
        includes = []
        
        include_pattern = r'#include\\s*[<"]([^>"]+)[>"]'
        includes.extend(re.findall(include_pattern, code))
        
        return includes
    
    def _extract_functions(self, code: str) -> List[Dict]:
        """提取C++函数"""
        functions = []
        
        # 简化的函数匹配
        func_pattern = r'(?:inline\\s+)?(?:[a-zA-Z_][a-zA-Z0-9_]*\\s+)?([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\([^)]*\\)\\s*\\{'
        for match in re.finditer(func_pattern, code):
            if match.group(1) not in ['if', 'while', 'for', 'switch']:  # 排除控制结构
                functions.append({
                    'name': match.group(1),
                    'type': 'function'
                })
        
        return functions
    
    def _extract_classes(self, code: str) -> List[Dict]:
        """提取C++类"""
        classes = []
        
        class_pattern = r'class\\s+([a-zA-Z_][a-zA-Z0-9_]*)(?:\\s*:\\s*(?:public|private|protected)\\s+([a-zA-Z_][a-zA-Z0-9_]*))?'
        for match in re.finditer(class_pattern, code):
            classes.append({
                'name': match.group(1),
                'base': match.group(2) if match.group(2) else None
            })
        
        return classes

# 项目模板类
class ProjectTemplate:
    """项目模板基类"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        """获取模板提示"""
        return ""
    
    def get_best_practices(self) -> List[str]:
        """获取最佳实践建议"""
        return []

class WebAppTemplate(ProjectTemplate):
    """Web应用模板"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        if context.framework == 'flask':
            return """
请遵循Flask Web应用的最佳实践:
1. 使用蓝图(Blueprint)组织路由
2. 实现适当的错误处理
3. 使用模板引擎渲染页面
4. 实现表单验证和CSRF保护
5. 使用数据库迁移管理数据结构
"""
        elif context.framework == 'django':
            return """
请遵循Django Web应用的最佳实践:
1. 使用Django的MVT架构模式
2. 实现适当的模型关系和验证
3. 使用Django表单处理用户输入
4. 实现用户认证和权限控制
5. 使用Django的缓存和优化功能
"""
        return "请遵循Web应用开发的通用最佳实践"

class DesktopAppTemplate(ProjectTemplate):
    """桌面应用模板"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        if context.framework == 'tkinter':
            return """
请遵循Tkinter桌面应用的最佳实践:
1. 使用面向对象的方式组织GUI代码
2. 实现响应式布局管理
3. 添加菜单栏和工具栏
4. 实现文件操作和配置管理
5. 处理窗口事件和用户交互
"""
        elif 'pyqt' in context.framework:
            return """
请遵循PyQt桌面应用的最佳实践:
1. 使用信号和槽机制处理事件
2. 实现自定义控件和样式
3. 使用线程处理耗时操作
4. 实现国际化和本地化
5. 添加系统托盘和通知功能
"""
        return "请遵循桌面应用开发的通用最佳实践"

class CLIToolTemplate(ProjectTemplate):
    """命令行工具模板"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        return """
请遵循CLI工具开发的最佳实践:
1. 使用argparse或click处理命令行参数
2. 实现清晰的帮助信息和使用说明
3. 添加进度条和状态反馈
4. 实现配置文件支持
5. 处理错误和异常情况
6. 支持管道操作和批处理
"""

class LibraryTemplate(ProjectTemplate):
    """库项目模板"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        return """
请遵循Python库开发的最佳实践:
1. 设计清晰的API接口
2. 添加完整的文档字符串
3. 实现单元测试和集成测试
4. 处理向后兼容性
5. 使用语义化版本控制
6. 提供使用示例和教程
"""

class DataScienceTemplate(ProjectTemplate):
    """数据科学项目模板"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        return """
请遵循数据科学项目的最佳实践:
1. 使用Jupyter Notebook进行探索性分析
2. 实现数据清洗和预处理管道
3. 使用适当的可视化工具
4. 实现模型训练和评估
5. 添加数据版本控制
6. 实现可重现的实验流程
"""

class APIServiceTemplate(ProjectTemplate):
    """API服务模板"""
    
    def get_template_prompt(self, context: CodeContext) -> str:
        return """
请遵循API服务开发的最佳实践:
1. 实现RESTful API设计原则
2. 添加API文档和版本控制
3. 实现身份验证和授权
4. 添加请求限制和缓存
5. 实现错误处理和日志记录
6. 使用数据验证和序列化
"""