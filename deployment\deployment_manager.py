# 统一部署管理器
import os
import json
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum

from .platform_manager import PlatformManager, PlatformType, DeploymentType, BuildResult
from .container_manager import ContainerManager, ContainerPlatform, DeploymentEnvironment
from .auto_updater import AutoUpdater, UpdateStatus, UpdateInfo
from .app_store_publisher import AppStorePublisher, AppStore, PublishResult, PublishStatus

class DeploymentStage(Enum):
    BUILDING = "building"
    CONTAINERIZING = "containerizing"
    PUBLISHING = "publishing"
    UPDATING = "updating"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class DeploymentPlan:
    """部署计划"""
    name: str
    description: str
    platforms: List[PlatformType]
    containers: List[ContainerPlatform]
    app_stores: List[AppStore]
    auto_update: bool
    parallel_execution: bool

@dataclass
class DeploymentProgress:
    """部署进度"""
    stage: DeploymentStage
    current_task: str
    progress: float  # 0-100
    completed_tasks: List[str]
    failed_tasks: List[str]
    warnings: List[str]

class DeploymentManager:
    """统一部署管理器"""
    
    def __init__(self, project_path: str = None):
        self.project_path = project_path or os.getcwd()
        self.config = self._load_config()
        
        # 初始化各个管理器
        self.platform_manager = PlatformManager(self.project_path)
        self.container_manager = ContainerManager(self.project_path)
        self.auto_updater = AutoUpdater()
        self.app_store_publisher = AppStorePublisher(self.project_path)
        
        # 部署状态
        self.is_deploying = False
        self.current_progress = None
        self.deployment_callbacks: List[Callable[[DeploymentProgress], None]] = []
    
    def _load_config(self) -> Dict:
        """加载部署配置"""
        config_file = os.path.join(self.project_path, 'deployment_config.json')
        default_config = {
            'deployment_plans': {
                'development': {
                    'name': '开发环境部署',
                    'description': '用于开发和测试的快速部署',
                    'platforms': ['windows', 'linux'],
                    'containers': ['docker'],
                    'app_stores': [],
                    'auto_update': False,
                    'parallel_execution': True
                },
                'staging': {
                    'name': '预发布环境部署',
                    'description': '用于预发布测试的完整部署',
                    'platforms': ['windows', 'macos', 'linux'],
                    'containers': ['docker', 'kubernetes'],
                    'app_stores': ['snap_store'],
                    'auto_update': True,
                    'parallel_execution': True
                },
                'production': {
                    'name': '生产环境部署',
                    'description': '完整的生产环境部署',
                    'platforms': ['windows', 'macos', 'linux'],
                    'containers': ['docker', 'kubernetes'],
                    'app_stores': ['microsoft_store', 'snap_store', 'flathub', 'chocolatey', 'homebrew'],
                    'auto_update': True,
                    'parallel_execution': False
                }
            },
            'notification_settings': {
                'email_enabled': False,
                'email_recipients': [],
                'webhook_url': '',
                'slack_webhook': ''
            },
            'rollback_settings': {
                'enabled': True,
                'keep_previous_versions': 3,
                'auto_rollback_on_failure': True
            },
            'monitoring': {
                'enabled': True,
                'health_check_url': '',
                'metrics_endpoint': '',
                'log_aggregation': True
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self._merge_config(default_config, user_config)
        
        return default_config
    
    def _merge_config(self, default: Dict, user: Dict):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def add_deployment_callback(self, callback: Callable[[DeploymentProgress], None]):
        """添加部署进度回调"""
        self.deployment_callbacks.append(callback)
    
    def remove_deployment_callback(self, callback: Callable[[DeploymentProgress], None]):
        """移除部署进度回调"""
        if callback in self.deployment_callbacks:
            self.deployment_callbacks.remove(callback)
    
    def _notify_progress(self, progress: DeploymentProgress):
        """通知部署进度"""
        self.current_progress = progress
        for callback in self.deployment_callbacks:
            try:
                callback(progress)
            except Exception as e:
                print(f"部署回调执行失败: {e}")
    
    def get_deployment_plans(self) -> Dict[str, DeploymentPlan]:
        """获取部署计划"""
        plans = {}
        for plan_name, plan_config in self.config['deployment_plans'].items():
            plans[plan_name] = DeploymentPlan(
                name=plan_config['name'],
                description=plan_config['description'],
                platforms=[PlatformType(p) for p in plan_config['platforms']],
                containers=[ContainerPlatform(c) for c in plan_config['containers']],
                app_stores=[AppStore(s) for s in plan_config['app_stores']],
                auto_update=plan_config['auto_update'],
                parallel_execution=plan_config['parallel_execution']
            )
        return plans
    
    def execute_deployment_plan(self, plan_name: str) -> bool:
        """执行部署计划"""
        if self.is_deploying:
            print("部署正在进行中，请等待完成")
            return False
        
        plan_config = self.config['deployment_plans'].get(plan_name)
        if not plan_config:
            print(f"部署计划不存在: {plan_name}")
            return False
        
        plan = DeploymentPlan(
            name=plan_config['name'],
            description=plan_config['description'],
            platforms=[PlatformType(p) for p in plan_config['platforms']],
            containers=[ContainerPlatform(c) for c in plan_config['containers']],
            app_stores=[AppStore(s) for s in plan_config['app_stores']],
            auto_update=plan_config['auto_update'],
            parallel_execution=plan_config['parallel_execution']
        )
        
        self.is_deploying = True
        
        try:
            if plan.parallel_execution:
                return self._execute_parallel_deployment(plan)
            else:
                return self._execute_sequential_deployment(plan)
        finally:
            self.is_deploying = False
    
    def _execute_parallel_deployment(self, plan: DeploymentPlan) -> bool:
        """并行执行部署"""
        print(f"🚀 开始并行部署: {plan.name}")
        
        # 创建线程池
        threads = []
        results = {}
        
        # 平台构建线程
        if plan.platforms:
            for platform in plan.platforms:
                thread = threading.Thread(
                    target=self._build_platform_thread,
                    args=(platform, results)
                )
                threads.append(thread)
                thread.start()
        
        # 容器化线程
        if plan.containers:
            thread = threading.Thread(
                target=self._containerize_thread,
                args=(plan.containers, results)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查构建结果
        build_success = all(
            result.get('success', False) 
            for result in results.values() 
            if 'build' in result
        )
        
        if not build_success:
            self._notify_progress(DeploymentProgress(
                stage=DeploymentStage.FAILED,
                current_task="构建失败",
                progress=0,
                completed_tasks=[],
                failed_tasks=list(results.keys()),
                warnings=[]
            ))
            return False
        
        # 应用商店发布（串行执行）
        if plan.app_stores:
            publish_success = self._publish_to_stores(plan.app_stores)
            if not publish_success:
                return False
        
        # 设置自动更新
        if plan.auto_update:
            self._setup_auto_update()
        
        self._notify_progress(DeploymentProgress(
            stage=DeploymentStage.COMPLETED,
            current_task="部署完成",
            progress=100,
            completed_tasks=list(results.keys()),
            failed_tasks=[],
            warnings=[]
        ))
        
        # 发送通知
        self._send_deployment_notification(plan.name, True)
        
        return True
    
    def _execute_sequential_deployment(self, plan: DeploymentPlan) -> bool:
        """串行执行部署"""
        print(f"🚀 开始串行部署: {plan.name}")
        
        total_tasks = len(plan.platforms) + len(plan.containers) + len(plan.app_stores) + (1 if plan.auto_update else 0)
        completed_tasks = []
        failed_tasks = []
        current_task_index = 0
        
        # 1. 平台构建
        self._notify_progress(DeploymentProgress(
            stage=DeploymentStage.BUILDING,
            current_task="构建多平台版本",
            progress=(current_task_index / total_tasks) * 100,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            warnings=[]
        ))
        
        for platform in plan.platforms:
            current_task_index += 1
            task_name = f"构建{platform.value}版本"
            
            self._notify_progress(DeploymentProgress(
                stage=DeploymentStage.BUILDING,
                current_task=task_name,
                progress=(current_task_index / total_tasks) * 100,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                warnings=[]
            ))
            
            result = self.platform_manager.build_for_platform(platform)
            if result.success:
                completed_tasks.append(task_name)
            else:
                failed_tasks.append(task_name)
                print(f"❌ {task_name}失败: {result.error_message}")
                
                if self.config['rollback_settings']['auto_rollback_on_failure']:
                    self._rollback_deployment()
                return False
        
        # 2. 容器化
        if plan.containers:
            current_task_index += 1
            self._notify_progress(DeploymentProgress(
                stage=DeploymentStage.CONTAINERIZING,
                current_task="创建容器镜像",
                progress=(current_task_index / total_tasks) * 100,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                warnings=[]
            ))
            
            container_success = self._containerize_application(plan.containers)
            if container_success:
                completed_tasks.append("容器化")
            else:
                failed_tasks.append("容器化")
                return False
        
        # 3. 应用商店发布
        if plan.app_stores:
            self._notify_progress(DeploymentProgress(
                stage=DeploymentStage.PUBLISHING,
                current_task="发布到应用商店",
                progress=(current_task_index / total_tasks) * 100,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                warnings=[]
            ))
            
            publish_success = self._publish_to_stores(plan.app_stores)
            if not publish_success:
                return False
        
        # 4. 设置自动更新
        if plan.auto_update:
            current_task_index += 1
            self._notify_progress(DeploymentProgress(
                stage=DeploymentStage.UPDATING,
                current_task="配置自动更新",
                progress=(current_task_index / total_tasks) * 100,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                warnings=[]
            ))
            
            self._setup_auto_update()
            completed_tasks.append("自动更新配置")
        
        self._notify_progress(DeploymentProgress(
            stage=DeploymentStage.COMPLETED,
            current_task="部署完成",
            progress=100,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            warnings=[]
        ))
        
        # 发送通知
        self._send_deployment_notification(plan.name, True)
        
        return True
    
    def _build_platform_thread(self, platform: PlatformType, results: Dict):
        """平台构建线程"""
        try:
            result = self.platform_manager.build_for_platform(platform)
            results[f"build_{platform.value}"] = {
                'success': result.success,
                'output_path': result.output_path,
                'error': result.error_message
            }
        except Exception as e:
            results[f"build_{platform.value}"] = {
                'success': False,
                'error': str(e)
            }
    
    def _containerize_thread(self, containers: List[ContainerPlatform], results: Dict):
        """容器化线程"""
        try:
            success = self._containerize_application(containers)
            results['containerize'] = {
                'success': success,
                'containers': [c.value for c in containers]
            }
        except Exception as e:
            results['containerize'] = {
                'success': False,
                'error': str(e)
            }
    
    def _containerize_application(self, containers: List[ContainerPlatform]) -> bool:
        """容器化应用"""
        try:
            # 创建容器文件
            self.container_manager.create_container_files()
            
            # 构建Docker镜像
            if ContainerPlatform.DOCKER in containers:
                success = self.container_manager.build_docker_image()
                if not success:
                    return False
                
                # 推送镜像
                success = self.container_manager.push_docker_image()
                if not success:
                    print("⚠️ Docker镜像推送失败，但构建成功")
            
            # 部署到Kubernetes
            if ContainerPlatform.KUBERNETES in containers:
                success = self.container_manager.deploy_to_kubernetes()
                if not success:
                    return False
            
            # Docker Compose部署
            if ContainerPlatform.DOCKER_COMPOSE in containers:
                success = self.container_manager.deploy_with_docker_compose()
                if not success:
                    return False
            
            return True
            
        except Exception as e:
            print(f"容器化失败: {e}")
            return False
    
    def _publish_to_stores(self, app_stores: List[AppStore]) -> bool:
        """发布到应用商店"""
        try:
            results = {}
            
            for store in app_stores:
                print(f"📱 发布到 {store.value}...")
                
                if store == AppStore.MICROSOFT_STORE:
                    result = self.app_store_publisher.publish_to_microsoft_store()
                elif store == AppStore.SNAP_STORE:
                    result = self.app_store_publisher.publish_to_snap_store()
                elif store == AppStore.FLATHUB:
                    result = self.app_store_publisher.publish_to_flathub()
                elif store == AppStore.CHOCOLATEY:
                    result = self.app_store_publisher.publish_to_chocolatey()
                elif store == AppStore.HOMEBREW:
                    result = self.app_store_publisher.publish_to_homebrew()
                else:
                    continue
                
                results[store] = result
                
                if result.status == PublishStatus.FAILED:
                    print(f"❌ {store.value}发布失败: {result.message}")
                    return False
                else:
                    print(f"✅ {store.value}发布成功: {result.message}")
            
            return True
            
        except Exception as e:
            print(f"应用商店发布失败: {e}")
            return False
    
    def _setup_auto_update(self):
        """设置自动更新"""
        try:
            # 启动自动检查
            self.auto_updater.start_auto_check()
            print("✅ 自动更新已配置")
        except Exception as e:
            print(f"⚠️ 自动更新配置失败: {e}")
    
    def _rollback_deployment(self):
        """回滚部署"""
        try:
            print("🔄 开始回滚部署...")
            # 这里可以实现具体的回滚逻辑
            # 例如：恢复之前的版本、回滚容器部署等
            print("✅ 部署回滚完成")
        except Exception as e:
            print(f"❌ 部署回滚失败: {e}")
    
    def _send_deployment_notification(self, plan_name: str, success: bool):
        """发送部署通知"""
        notification_config = self.config['notification_settings']
        
        status = "成功" if success else "失败"
        message = f"部署计划 '{plan_name}' 执行{status}"
        
        try:
            # Webhook通知
            webhook_url = notification_config.get('webhook_url')
            if webhook_url:
                import requests
                requests.post(webhook_url, json={
                    'text': message,
                    'plan': plan_name,
                    'success': success,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # Slack通知
            slack_webhook = notification_config.get('slack_webhook')
            if slack_webhook:
                import requests
                requests.post(slack_webhook, json={
                    'text': f"🚀 {message}",
                    'color': 'good' if success else 'danger'
                })
            
        except Exception as e:
            print(f"发送通知失败: {e}")
    
    def create_deployment_pipeline(self, pipeline_name: str, stages: List[str]) -> bool:
        """创建部署流水线"""
        try:
            pipeline_config = {
                'name': pipeline_name,
                'stages': stages,
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'triggers': {
                    'on_push': True,
                    'on_tag': True,
                    'scheduled': False
                }
            }
            
            # 保存流水线配置
            pipelines_file = os.path.join(self.project_path, 'deployment_pipelines.json')
            pipelines = {}
            
            if os.path.exists(pipelines_file):
                with open(pipelines_file, 'r', encoding='utf-8') as f:
                    pipelines = json.load(f)
            
            pipelines[pipeline_name] = pipeline_config
            
            with open(pipelines_file, 'w', encoding='utf-8') as f:
                json.dump(pipelines, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 部署流水线 '{pipeline_name}' 创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建部署流水线失败: {e}")
            return False
    
    def get_deployment_status(self) -> Dict:
        """获取部署状态"""
        return {
            'is_deploying': self.is_deploying,
            'current_progress': self.current_progress.__dict__ if self.current_progress else None,
            'last_deployment': self._get_last_deployment_info()
        }
    
    def _get_last_deployment_info(self) -> Dict:
        """获取最后一次部署信息"""
        try:
            history_file = os.path.join(self.project_path, 'deployment_history.json')
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    return history[-1] if history else {}
            return {}
        except:
            return {}
    
    def save_deployment_history(self, plan_name: str, success: bool, details: Dict):
        """保存部署历史"""
        try:
            history_file = os.path.join(self.project_path, 'deployment_history.json')
            history = []
            
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            history.append({
                'plan_name': plan_name,
                'success': success,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'details': details
            })
            
            # 只保留最近50条记录
            history = history[-50:]
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存部署历史失败: {e}")
    
    def generate_deployment_report(self) -> str:
        """生成部署报告"""
        try:
            history_file = os.path.join(self.project_path, 'deployment_history.json')
            if not os.path.exists(history_file):
                return "暂无部署历史"
            
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            if not history:
                return "暂无部署历史"
            
            report = ["# 部署报告\n"]
            
            # 统计信息
            total_deployments = len(history)
            successful_deployments = sum(1 for h in history if h['success'])
            success_rate = (successful_deployments / total_deployments) * 100
            
            report.append(f"## 统计信息")
            report.append(f"- 总部署次数: {total_deployments}")
            report.append(f"- 成功次数: {successful_deployments}")
            report.append(f"- 成功率: {success_rate:.1f}%\n")
            
            # 最近10次部署
            report.append("## 最近部署记录")
            for deployment in history[-10:]:
                status = "✅" if deployment['success'] else "❌"
                report.append(f"- {status} {deployment['timestamp']} - {deployment['plan_name']}")
            
            return "\n".join(report)
            
        except Exception as e:
            return f"生成部署报告失败: {e}"
    
    def create_all_deployment_files(self):
        """创建所有部署相关文件"""
        print("🚀 创建完整的部署和分发系统...")
        
        # 创建平台构建文件
        print("📦 创建多平台构建配置...")
        
        # 创建容器化文件
        print("🐳 创建容器化部署文件...")
        self.container_manager.create_container_files()
        
        # 创建应用商店发布文件
        print("📱 创建应用商店发布文件...")
        self.app_store_publisher.create_publish_files()
        
        # 创建自动更新配置
        print("🔄 创建自动更新配置...")
        update_config = {
            'update_server': {
                'base_url': 'https://api.github.com/repos/your-org/your-repo/releases',
                'api_key': '',
                'timeout': 30
            },
            'update_settings': {
                'channel': 'stable',
                'auto_check': True,
                'check_interval': 3600,
                'auto_download': True,
                'auto_install': False,
                'backup_before_update': True,
                'rollback_enabled': True
            }
        }
        
        with open(os.path.join(self.project_path, 'update_config.json'), 'w', encoding='utf-8') as f:
            json.dump(update_config, f, indent=2, ensure_ascii=False)
        
        # 创建部署配置
        print("⚙️ 创建部署管理配置...")
        config_path = os.path.join(self.project_path, 'deployment_config.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        # 创建GitHub Actions工作流
        print("🔧 创建CI/CD工作流...")
        self._create_github_actions_workflow()
        
        # 创建部署脚本
        print("📜 创建部署脚本...")
        self._create_deployment_scripts()
        
        print("\n✅ 完整的部署和分发系统创建完成!")
        print("\n📁 生成的文件和目录:")
        print("  - deployment/ (部署管理器)")
        print("  - Dockerfile (Docker镜像)")
        print("  - docker-compose.yml (容器编排)")
        print("  - deployment/kubernetes/ (K8s清单)")
        print("  - deployment/helm/ (Helm Chart)")
        print("  - publish_config.json (应用商店配置)")
        print("  - app_metadata.json (应用元数据)")
        print("  - update_config.json (自动更新配置)")
        print("  - deployment_config.json (部署配置)")
        print("  - .github/workflows/ (CI/CD工作流)")
        print("  - scripts/ (部署脚本)")
        
        print("\n🚀 使用方法:")
        print("1. 配置各个平台的认证信息")
        print("2. 运行: python -m deployment.deployment_manager")
        print("3. 选择部署计划执行")
    
    def _create_github_actions_workflow(self):
        """创建GitHub Actions工作流"""
        workflow_dir = os.path.join(self.project_path, '.github', 'workflows')
        os.makedirs(workflow_dir, exist_ok=True)
        
        workflow_content = """name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Run tests
      run: python -m pytest tests/

  build-windows:
    needs: test
    runs-on: windows-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Build Windows executable
      run: |
        pip install -r requirements.txt
        python -m deployment.platform_manager build windows
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v3
      with:
        name: windows-build
        path: dist/windows/

  build-macos:
    needs: test
    runs-on: macos-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Build macOS app
      run: |
        pip install -r requirements.txt
        python -m deployment.platform_manager build macos
    - name: Upload macOS artifact
      uses: actions/upload-artifact@v3
      with:
        name: macos-build
        path: dist/macos/

  build-linux:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Build Linux executable
      run: |
        pip install -r requirements.txt
        python -m deployment.platform_manager build linux
    - name: Upload Linux artifact
      uses: actions/upload-artifact@v3
      with:
        name: linux-build
        path: dist/linux/

  docker:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - uses: actions/checkout@v3
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    - name: Build and push Docker image
      run: |
        python -m deployment.container_manager build-and-push

  publish:
    needs: [build-windows, build-macos, build-linux, docker]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - uses: actions/checkout@v3
    - name: Download all artifacts
      uses: actions/download-artifact@v3
    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/*
          macos-build/*
          linux-build/*
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - name: Publish to app stores
      run: |
        python -m deployment.app_store_publisher publish-all
      env:
        MICROSOFT_STORE_CLIENT_ID: ${{ secrets.MICROSOFT_STORE_CLIENT_ID }}
        MICROSOFT_STORE_CLIENT_SECRET: ${{ secrets.MICROSOFT_STORE_CLIENT_SECRET }}
        SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_STORE_CREDENTIALS }}
"""
        
        with open(os.path.join(workflow_dir, 'build-and-deploy.yml'), 'w', encoding='utf-8') as f:
            f.write(workflow_content)
    
    def _create_deployment_scripts(self):
        """创建部署脚本"""
        scripts_dir = os.path.join(self.project_path, 'scripts')
        os.makedirs(scripts_dir, exist_ok=True)
        
        # Windows部署脚本
        windows_script = """@echo off
echo Starting deployment...

echo Building for Windows...
python -m deployment.platform_manager build windows

echo Creating Docker image...
python -m deployment.container_manager build

echo Publishing to app stores...
python -m deployment.app_store_publisher publish-enabled

echo Deployment completed!
pause
"""
        
        with open(os.path.join(scripts_dir, 'deploy-windows.bat'), 'w', encoding='utf-8') as f:
            f.write(windows_script)
        
        # Linux/macOS部署脚本
        unix_script = """#!/bin/bash
set -e

echo "Starting deployment..."

echo "Building for current platform..."
python -m deployment.platform_manager build

echo "Creating Docker image..."
python -m deployment.container_manager build

echo "Publishing to app stores..."
python -m deployment.app_store_publisher publish-enabled

echo "Deployment completed!"
"""
        
        script_path = os.path.join(scripts_dir, 'deploy.sh')
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(unix_script)
        
        # 设置执行权限
        try:
            os.chmod(script_path, 0o755)
        except:
            pass  # Windows不支持chmod


if __name__ == "__main__":
    # 命令行接口
    import sys
    
    manager = DeploymentManager()
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python -m deployment.deployment_manager create-files  # 创建所有部署文件")
        print("  python -m deployment.deployment_manager deploy <plan>  # 执行部署计划")
        print("  python -m deployment.deployment_manager status  # 查看部署状态")
        print("  python -m deployment.deployment_manager report  # 生成部署报告")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "create-files":
        manager.create_all_deployment_files()
    elif command == "deploy":
        if len(sys.argv) < 3:
            plans = manager.get_deployment_plans()
            print("可用的部署计划:")
            for plan_name, plan in plans.items():
                print(f"  {plan_name}: {plan.description}")
        else:
            plan_name = sys.argv[2]
            success = manager.execute_deployment_plan(plan_name)
            sys.exit(0 if success else 1)
    elif command == "status":
        status = manager.get_deployment_status()
        print(json.dumps(status, indent=2, ensure_ascii=False))
    elif command == "report":
        report = manager.generate_deployment_report()
        print(report)
    else:
        print(f"未知命令: {command}")
        sys.exit(1)