#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本
清理临时文件、缓存文件和不必要的文件
"""

import os
import shutil
import glob
import sys
from pathlib import Path

def clean_pycache():
    """清理Python缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    # 查找并删除__pycache__目录
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                print(f"   ✅ 删除: {pycache_path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {pycache_path} - {e}")
    
    # 删除.pyc和.pyo文件
    for pattern in ['**/*.pyc', '**/*.pyo']:
        for file_path in glob.glob(pattern, recursive=True):
            try:
                os.remove(file_path)
                print(f"   ✅ 删除: {file_path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {file_path} - {e}")

def clean_build_files():
    """清理构建文件"""
    print("🔨 清理构建文件...")
    
    build_dirs = ['build', 'dist', '*.egg-info']
    for pattern in build_dirs:
        for path in glob.glob(pattern):
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path)
                    print(f"   ✅ 删除目录: {path}")
                else:
                    os.remove(path)
                    print(f"   ✅ 删除文件: {path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {path} - {e}")

def clean_temp_files():
    """清理临时文件"""
    print("🗑️ 清理临时文件...")
    
    temp_patterns = [
        '**/*.tmp',
        '**/*.temp',
        '**/*.bak',
        '**/*.swp',
        '**/*.swo',
        '**/~*',
        '**/.DS_Store',
        '**/Thumbs.db',
        '**/*.log'
    ]
    
    for pattern in temp_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            try:
                os.remove(file_path)
                print(f"   ✅ 删除: {file_path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {file_path} - {e}")

def clean_ide_files():
    """清理IDE文件"""
    print("💻 清理IDE文件...")
    
    ide_patterns = [
        '.vscode',
        '.idea',
        '*.sublime-project',
        '*.sublime-workspace',
        '.vs'
    ]
    
    for pattern in ide_patterns:
        for path in glob.glob(pattern):
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path)
                    print(f"   ✅ 删除目录: {path}")
                else:
                    os.remove(path)
                    print(f"   ✅ 删除文件: {path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {path} - {e}")

def clean_node_modules():
    """清理Node.js模块（如果存在）"""
    print("📦 清理Node.js模块...")
    
    for root, dirs, files in os.walk('.'):
        if 'node_modules' in dirs:
            node_modules_path = os.path.join(root, 'node_modules')
            try:
                shutil.rmtree(node_modules_path)
                print(f"   ✅ 删除: {node_modules_path}")
            except Exception as e:
                print(f"   ❌ 删除失败: {node_modules_path} - {e}")

def organize_requirements():
    """整理requirements文件"""
    print("📋 整理requirements文件...")
    
    # 检查是否有多个requirements文件
    req_files = glob.glob('requirements*.txt')
    if len(req_files) > 1:
        print(f"   发现多个requirements文件: {req_files}")
        
        # 保留requirements_enhanced.txt作为主要文件
        if 'requirements_enhanced.txt' in req_files:
            print("   ✅ 保留 requirements_enhanced.txt 作为主要依赖文件")
            
            # 删除其他重复的requirements文件
            for req_file in req_files:
                if req_file not in ['requirements_enhanced.txt', 'requirements.txt']:
                    try:
                        os.remove(req_file)
                        print(f"   ✅ 删除重复文件: {req_file}")
                    except Exception as e:
                        print(f"   ❌ 删除失败: {req_file} - {e}")

def create_gitignore():
    """创建或更新.gitignore文件"""
    print("📝 创建/更新.gitignore文件...")
    
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.vs/

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# API keys and sensitive data
api_configs.json
*.key
*.pem
*.p12

# Build artifacts
*.exe
*.msi
*.dmg
*.deb
*.rpm
*.pkg

# Logs
*.log
logs/

# Cache
.cache/
cache/
"""
    
    try:
        with open('.gitignore', 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        print("   ✅ .gitignore 文件已创建/更新")
    except Exception as e:
        print(f"   ❌ 创建.gitignore失败: {e}")

def main():
    """主函数"""
    print("🚀 开始项目清理...")
    print("=" * 50)
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # 执行清理操作
    clean_pycache()
    clean_build_files()
    clean_temp_files()
    clean_ide_files()
    clean_node_modules()
    organize_requirements()
    create_gitignore()
    
    print("=" * 50)
    print("✅ 项目清理完成！")
    print("💡 建议:")
    print("   1. 检查 .gitignore 文件是否符合需求")
    print("   2. 确认重要文件没有被误删")
    print("   3. 重新安装依赖: pip install -r requirements_enhanced.txt")

if __name__ == "__main__":
    main()
