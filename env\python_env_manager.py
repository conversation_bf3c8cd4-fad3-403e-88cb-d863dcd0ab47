# Python环境管理器模块

import os
import sys
import subprocess
import platform
import tempfile
import shutil

class PythonEnvManager:
    def __init__(self):
        self.env_path = os.path.join(os.getcwd(), 'python_env')
        self.is_windows = platform.system() == 'Windows'

    def create_env(self):
        """创建Python虚拟环境"""
        if os.path.exists(self.env_path):
            return True, '虚拟环境已存在'

        try:
            # 使用venv创建虚拟环境
            subprocess.run([sys.executable, '-m', 'venv', self.env_path], check=True)
            return True, f'虚拟环境创建成功: {self.env_path}'
        except subprocess.CalledProcessError as e:
            return False, f'创建虚拟环境失败: {str(e)}'
        except Exception as e:
            return False, f'发生异常: {str(e)}'

    def activate_env(self):
        """激活虚拟环境
        注意：这只是返回激活命令，实际激活需要在终端中执行
        """
        if not os.path.exists(self.env_path):
            return False, '虚拟环境不存在'

        if self.is_windows:
            activate_script = os.path.join(self.env_path, 'Scripts', 'activate.bat')
        else:
            activate_script = os.path.join(self.env_path, 'bin', 'activate')

        if not os.path.exists(activate_script):
            return False, '激活脚本不存在'

        if self.is_windows:
            return True, f'call "{activate_script}"'
        else:
            return True, f'source "{activate_script}"'

    def deactivate_env(self):
        """停用虚拟环境"""
        return True, 'deactivate'

    def install_package(self, package_name):
        """在虚拟环境中安装包"""
        if not os.path.exists(self.env_path):
            return False, '虚拟环境不存在'

        if self.is_windows:
            pip_path = os.path.join(self.env_path, 'Scripts', 'pip.exe')
        else:
            pip_path = os.path.join(self.env_path, 'bin', 'pip')

        if not os.path.exists(pip_path):
            return False, 'pip不存在'

        try:
            subprocess.run([pip_path, 'install', package_name], check=True)
            return True, f'包 {package_name} 安装成功'
        except subprocess.CalledProcessError as e:
            return False, f'安装包失败: {str(e)}'
        except Exception as e:
            return False, f'发生异常: {str(e)}'

    def get_python_path(self):
        """获取虚拟环境中的Python路径"""
        if not os.path.exists(self.env_path):
            return None

        if self.is_windows:
            python_path = os.path.join(self.env_path, 'Scripts', 'python.exe')
        else:
            python_path = os.path.join(self.env_path, 'bin', 'python')

        if os.path.exists(python_path):
            return python_path
        else:
            return None

    def list_packages(self):
        """列出虚拟环境中已安装的包"""
        if not os.path.exists(self.env_path):
            return False, '虚拟环境不存在'

        if self.is_windows:
            pip_path = os.path.join(self.env_path, 'Scripts', 'pip.exe')
        else:
            pip_path = os.path.join(self.env_path, 'bin', 'pip')

        if not os.path.exists(pip_path):
            return False, 'pip不存在'

        try:
            result = subprocess.run([pip_path, 'list'], capture_output=True, text=True, check=True)
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            return False, f'获取包列表失败: {str(e)}'
        except Exception as e:
            return False, f'发生异常: {str(e)}'

    def remove_env(self):
        """删除虚拟环境"""
        if not os.path.exists(self.env_path):
            return True, '虚拟环境不存在'

        try:
            shutil.rmtree(self.env_path)
            return True, '虚拟环境删除成功'
        except Exception as e:
            return False, f'删除虚拟环境失败: {str(e)}'

    def run_in_env(self, script_path):
        """在虚拟环境中运行脚本"""
        python_path = self.get_python_path()
        if not python_path:
            return False, '虚拟环境Python不存在'

        try:
            result = subprocess.run([python_path, script_path], 
                                  capture_output=True, text=True, check=True)
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            return False, f'运行脚本失败: {e.stderr}'
        except Exception as e:
            return False, f'发生异常: {str(e)}'