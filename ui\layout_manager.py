#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面布局管理器
解决界面显示比例问题，提供响应式布局
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from typing import Dict, List, Tuple, Optional
import json
import os

class LayoutConfig:
    """布局配置类"""
    
    def __init__(self):
        self.config_file = "ui_layout_config.json"
        self.default_config = {
            "window": {
                "min_width": 1200,
                "min_height": 800,
                "default_width": 1400,
                "default_height": 900,
                "max_width": 2560,
                "max_height": 1440
            },
            "sidebar": {
                "min_width": 200,
                "max_width": 400,
                "default_width": 250,
                "collapsible": True
            },
            "main_area": {
                "min_width": 600,
                "margins": [10, 10, 10, 10],  # left, top, right, bottom
                "spacing": 10
            },
            "status_bar": {
                "height": 25,
                "show_progress": True
            },
            "toolbar": {
                "height": 40,
                "icon_size": 24
            },
            "tabs": {
                "height": 35,
                "closable": True,
                "movable": True
            },
            "responsive": {
                "breakpoints": {
                    "small": 1024,
                    "medium": 1366,
                    "large": 1920
                },
                "auto_adjust": True
            }
        }
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置，保留默认值
                    self.config = self._merge_config(self.default_config, loaded_config)
            else:
                self.config = self.default_config.copy()
                self.save_config()
        except Exception as e:
            print(f"加载布局配置失败: {e}")
            self.config = self.default_config.copy()
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存布局配置失败: {e}")
    
    def _merge_config(self, default: dict, loaded: dict) -> dict:
        """合并配置"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, path: str, default=None):
        """获取配置值"""
        keys = path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

class ResponsiveLayout:
    """响应式布局管理器"""
    
    def __init__(self, config: LayoutConfig):
        self.config = config
        self.current_breakpoint = "medium"
        self.widgets: Dict[str, QWidget] = {}
        self.layouts: Dict[str, QLayout] = {}
    
    def register_widget(self, name: str, widget: QWidget):
        """注册组件"""
        self.widgets[name] = widget
    
    def register_layout(self, name: str, layout: QLayout):
        """注册布局"""
        self.layouts[name] = layout
    
    def update_breakpoint(self, width: int):
        """更新断点"""
        breakpoints = self.config.get('responsive.breakpoints', {})
        
        if width < breakpoints.get('small', 1024):
            new_breakpoint = "small"
        elif width < breakpoints.get('medium', 1366):
            new_breakpoint = "medium"
        else:
            new_breakpoint = "large"
        
        if new_breakpoint != self.current_breakpoint:
            self.current_breakpoint = new_breakpoint
            self.apply_responsive_layout()
    
    def apply_responsive_layout(self):
        """应用响应式布局"""
        if self.current_breakpoint == "small":
            self._apply_small_layout()
        elif self.current_breakpoint == "medium":
            self._apply_medium_layout()
        else:
            self._apply_large_layout()
    
    def _apply_small_layout(self):
        """小屏幕布局"""
        # 隐藏侧边栏或折叠
        if 'sidebar' in self.widgets:
            sidebar = self.widgets['sidebar']
            if hasattr(sidebar, 'collapse'):
                sidebar.collapse()
        
        # 调整工具栏
        if 'toolbar' in self.widgets:
            toolbar = self.widgets['toolbar']
            toolbar.setToolButtonStyle(Qt.ToolButtonIconOnly)
    
    def _apply_medium_layout(self):
        """中等屏幕布局"""
        # 显示侧边栏
        if 'sidebar' in self.widgets:
            sidebar = self.widgets['sidebar']
            if hasattr(sidebar, 'expand'):
                sidebar.expand()
        
        # 调整工具栏
        if 'toolbar' in self.widgets:
            toolbar = self.widgets['toolbar']
            toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
    
    def _apply_large_layout(self):
        """大屏幕布局"""
        # 显示所有组件
        if 'sidebar' in self.widgets:
            sidebar = self.widgets['sidebar']
            if hasattr(sidebar, 'expand'):
                sidebar.expand()
        
        # 调整工具栏
        if 'toolbar' in self.widgets:
            toolbar = self.widgets['toolbar']
            toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

class ModernSplitter(QSplitter):
    """现代化分割器"""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setChildrenCollapsible(True)
        self.setHandleWidth(8)
        self.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                border: 1px solid #c0c0c0;
                border-radius: 3px;
            }
            QSplitter::handle:hover {
                background-color: #d0d0d0;
            }
            QSplitter::handle:pressed {
                background-color: #b0b0b0;
            }
        """)
    
    def set_proportions(self, proportions: List[float]):
        """设置分割比例"""
        if len(proportions) != self.count():
            return
        
        total_size = self.width() if self.orientation() == Qt.Horizontal else self.height()
        sizes = [int(total_size * prop) for prop in proportions]
        self.setSizes(sizes)

class LayoutManager:
    """布局管理器主类"""
    
    def __init__(self, main_window: QMainWindow):
        self.main_window = main_window
        self.config = LayoutConfig()
        self.responsive = ResponsiveLayout(self.config)
        self.setup_window()
    
    def setup_window(self):
        """设置窗口"""
        # 设置窗口大小
        min_width = self.config.get('window.min_width', 1200)
        min_height = self.config.get('window.min_height', 800)
        default_width = self.config.get('window.default_width', 1400)
        default_height = self.config.get('window.default_height', 900)
        
        self.main_window.setMinimumSize(min_width, min_height)
        self.main_window.resize(default_width, default_height)
        
        # 连接窗口大小改变事件
        self.main_window.resizeEvent = self._on_window_resize
    
    def _on_window_resize(self, event):
        """窗口大小改变事件"""
        if self.config.get('responsive.auto_adjust', True):
            self.responsive.update_breakpoint(event.size().width())
        
        # 调用原始的resizeEvent
        QMainWindow.resizeEvent(self.main_window, event)
    
    def create_main_layout(self) -> QWidget:
        """创建主布局"""
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 设置边距和间距
        margins = self.config.get('main_area.margins', [10, 10, 10, 10])
        spacing = self.config.get('main_area.spacing', 10)
        
        main_layout.setContentsMargins(*margins)
        main_layout.setSpacing(spacing)
        
        return central_widget
    
    def create_splitter(self, orientation=Qt.Horizontal, proportions: List[float] = None) -> ModernSplitter:
        """创建分割器"""
        splitter = ModernSplitter(orientation)
        
        if proportions:
            QTimer.singleShot(100, lambda: splitter.set_proportions(proportions))
        
        return splitter
    
    def apply_widget_style(self, widget: QWidget, style_name: str):
        """应用组件样式"""
        styles = {
            'sidebar': """
                QWidget {
                    background-color: #f5f5f5;
                    border-right: 1px solid #e0e0e0;
                }
            """,
            'main_area': """
                QWidget {
                    background-color: white;
                    border: 1px solid #e0e0e0;
                    border-radius: 5px;
                }
            """,
            'toolbar': """
                QToolBar {
                    background-color: #fafafa;
                    border-bottom: 1px solid #e0e0e0;
                    spacing: 5px;
                }
                QToolButton {
                    padding: 5px;
                    border: none;
                    border-radius: 3px;
                }
                QToolButton:hover {
                    background-color: #e8e8e8;
                }
                QToolButton:pressed {
                    background-color: #d0d0d0;
                }
            """,
            'status_bar': """
                QStatusBar {
                    background-color: #f0f0f0;
                    border-top: 1px solid #e0e0e0;
                }
            """
        }
        
        if style_name in styles:
            widget.setStyleSheet(styles[style_name])
    
    def save_layout_state(self, splitters: Dict[str, QSplitter]):
        """保存布局状态"""
        state = {}
        for name, splitter in splitters.items():
            state[name] = {
                'sizes': splitter.sizes(),
                'orientation': splitter.orientation()
            }
        
        # 保存到配置文件
        self.config.config['layout_state'] = state
        self.config.save_config()
    
    def restore_layout_state(self, splitters: Dict[str, QSplitter]):
        """恢复布局状态"""
        state = self.config.get('layout_state', {})
        
        for name, splitter in splitters.items():
            if name in state:
                splitter_state = state[name]
                if 'sizes' in splitter_state:
                    QTimer.singleShot(100, lambda s=splitter, sizes=splitter_state['sizes']: s.setSizes(sizes))
