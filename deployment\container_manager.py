# 容器化部署管理器
import os
import json
import subprocess
import tempfile
import shutil
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import yaml

class ContainerPlatform(Enum):
    DOCKER = "docker"
    PODMAN = "podman"
    KUBERNETES = "kubernetes"
    DOCKER_COMPOSE = "docker-compose"

class DeploymentEnvironment(Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

@dataclass
class ContainerConfig:
    """容器配置"""
    platform: ContainerPlatform
    base_image: str
    python_version: str
    port: int
    environment: DeploymentEnvironment
    resources: Dict[str, str]
    volumes: List[str]
    environment_vars: Dict[str, str]

class ContainerManager:
    """容器化部署管理器"""
    
    def __init__(self, project_path: str = None):
        self.project_path = project_path or os.getcwd()
        self.config = self._load_container_config()
    
    def _load_container_config(self) -> Dict:
        """加载容器配置"""
        config_file = os.path.join(self.project_path, 'container_config.json')
        default_config = {
            'app_name': 'ai-code-generator',
            'version': '1.0.0',
            'registry': {
                'url': 'docker.io',
                'namespace': 'aicodegen',
                'username': '',
                'password': ''
            },
            'containers': {
                'app': {
                    'base_image': 'python:3.9-slim',
                    'port': 8080,
                    'resources': {
                        'cpu': '500m',
                        'memory': '512Mi'
                    },
                    'environment_vars': {
                        'PYTHONPATH': '/app',
                        'FLASK_ENV': 'production'
                    }
                },
                'redis': {
                    'image': 'redis:6-alpine',
                    'port': 6379,
                    'resources': {
                        'cpu': '100m',
                        'memory': '128Mi'
                    }
                },
                'postgres': {
                    'image': 'postgres:13-alpine',
                    'port': 5432,
                    'resources': {
                        'cpu': '200m',
                        'memory': '256Mi'
                    },
                    'environment_vars': {
                        'POSTGRES_DB': 'aicodegen',
                        'POSTGRES_USER': 'app',
                        'POSTGRES_PASSWORD': 'changeme'
                    }
                }
            },
            'kubernetes': {
                'namespace': 'ai-code-generator',
                'ingress': {
                    'enabled': True,
                    'host': 'aicodegen.example.com',
                    'tls': True
                },
                'autoscaling': {
                    'enabled': True,
                    'min_replicas': 2,
                    'max_replicas': 10,
                    'target_cpu': 70
                }
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self._merge_config(default_config, user_config)
        
        return default_config
    
    def _merge_config(self, default: Dict, user: Dict):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def generate_dockerfile(self, service: str = 'app') -> str:
        """生成Dockerfile"""
        container_config = self.config['containers'][service]
        base_image = container_config['base_image']
        
        dockerfile_content = f"""# AI代码生成器 - {service.upper()}服务
FROM {base_image}

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    git \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
"""
        
        # 添加环境变量
        for key, value in container_config.get('environment_vars', {}).items():
            dockerfile_content += f"ENV {key}={value}\n"
        
        dockerfile_content += f"""
# 暴露端口
EXPOSE {container_config['port']}

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{container_config['port']}/health || exit 1

# 启动命令
CMD ["python", "main.py"]
"""
        
        return dockerfile_content
    
    def generate_docker_compose(self) -> str:
        """生成docker-compose.yml"""
        compose_config = {
            'version': '3.8',
            'services': {},
            'networks': {
                'ai-code-generator': {
                    'driver': 'bridge'
                }
            },
            'volumes': {
                'postgres_data': {},
                'redis_data': {}
            }
        }
        
        # 应用服务
        app_config = self.config['containers']['app']
        compose_config['services']['app'] = {
            'build': {
                'context': '.',
                'dockerfile': 'Dockerfile'
            },
            'ports': [f"{app_config['port']}:{app_config['port']}"],
            'environment': app_config.get('environment_vars', {}),
            'depends_on': ['redis', 'postgres'],
            'networks': ['ai-code-generator'],
            'restart': 'unless-stopped',
            'deploy': {
                'resources': {
                    'limits': app_config['resources'],
                    'reservations': {
                        'cpu': '250m',
                        'memory': '256Mi'
                    }
                }
            }
        }
        
        # Redis服务
        redis_config = self.config['containers']['redis']
        compose_config['services']['redis'] = {
            'image': redis_config['image'],
            'ports': [f"{redis_config['port']}:{redis_config['port']}"],
            'volumes': ['redis_data:/data'],
            'networks': ['ai-code-generator'],
            'restart': 'unless-stopped',
            'deploy': {
                'resources': {
                    'limits': redis_config['resources']
                }
            }
        }
        
        # PostgreSQL服务
        postgres_config = self.config['containers']['postgres']
        compose_config['services']['postgres'] = {
            'image': postgres_config['image'],
            'ports': [f"{postgres_config['port']}:{postgres_config['port']}"],
            'environment': postgres_config.get('environment_vars', {}),
            'volumes': ['postgres_data:/var/lib/postgresql/data'],
            'networks': ['ai-code-generator'],
            'restart': 'unless-stopped',
            'deploy': {
                'resources': {
                    'limits': postgres_config['resources']
                }
            }
        }
        
        return yaml.dump(compose_config, default_flow_style=False, sort_keys=False)
    
    def generate_kubernetes_manifests(self) -> Dict[str, str]:
        """生成Kubernetes清单文件"""
        manifests = {}
        app_name = self.config['app_name']
        namespace = self.config['kubernetes']['namespace']
        
        # Namespace
        manifests['namespace.yaml'] = f"""apiVersion: v1
kind: Namespace
metadata:
  name: {namespace}
  labels:
    app: {app_name}
"""
        
        # ConfigMap
        manifests['configmap.yaml'] = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: {app_name}-config
  namespace: {namespace}
data:
  PYTHONPATH: "/app"
  FLASK_ENV: "production"
  DATABASE_URL: "***************************************/aicodegen"
  REDIS_URL: "redis://redis:6379/0"
"""
        
        # Secret
        manifests['secret.yaml'] = f"""apiVersion: v1
kind: Secret
metadata:
  name: {app_name}-secret
  namespace: {namespace}
type: Opaque
data:
  postgres-password: Y2hhbmdlbWU=  # base64 encoded 'changeme'
  api-key: ""  # 需要用户填入
"""
        
        # Deployment - App
        app_config = self.config['containers']['app']
        manifests['deployment-app.yaml'] = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {app_name}-app
  namespace: {namespace}
  labels:
    app: {app_name}
    component: app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: {app_name}
      component: app
  template:
    metadata:
      labels:
        app: {app_name}
        component: app
    spec:
      containers:
      - name: app
        image: {self.config['registry']['namespace']}/{app_name}:{self.config['version']}
        ports:
        - containerPort: {app_config['port']}
        envFrom:
        - configMapRef:
            name: {app_name}-config
        - secretRef:
            name: {app_name}-secret
        resources:
          limits:
            cpu: {app_config['resources']['cpu']}
            memory: {app_config['resources']['memory']}
          requests:
            cpu: "250m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: {app_config['port']}
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: {app_config['port']}
          initialDelaySeconds: 5
          periodSeconds: 5
"""
        
        # Service - App
        manifests['service-app.yaml'] = f"""apiVersion: v1
kind: Service
metadata:
  name: {app_name}-app
  namespace: {namespace}
  labels:
    app: {app_name}
    component: app
spec:
  selector:
    app: {app_name}
    component: app
  ports:
  - port: 80
    targetPort: {app_config['port']}
    protocol: TCP
  type: ClusterIP
"""
        
        # Deployment - Redis
        redis_config = self.config['containers']['redis']
        manifests['deployment-redis.yaml'] = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {app_name}-redis
  namespace: {namespace}
  labels:
    app: {app_name}
    component: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {app_name}
      component: redis
  template:
    metadata:
      labels:
        app: {app_name}
        component: redis
    spec:
      containers:
      - name: redis
        image: {redis_config['image']}
        ports:
        - containerPort: {redis_config['port']}
        resources:
          limits:
            cpu: {redis_config['resources']['cpu']}
            memory: {redis_config['resources']['memory']}
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: {app_name}-redis-pvc
"""
        
        # Service - Redis
        manifests['service-redis.yaml'] = f"""apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: {namespace}
  labels:
    app: {app_name}
    component: redis
spec:
  selector:
    app: {app_name}
    component: redis
  ports:
  - port: {redis_config['port']}
    targetPort: {redis_config['port']}
  type: ClusterIP
"""
        
        # PVC - Redis
        manifests['pvc-redis.yaml'] = f"""apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {app_name}-redis-pvc
  namespace: {namespace}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
"""
        
        # Deployment - PostgreSQL
        postgres_config = self.config['containers']['postgres']
        manifests['deployment-postgres.yaml'] = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {app_name}-postgres
  namespace: {namespace}
  labels:
    app: {app_name}
    component: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {app_name}
      component: postgres
  template:
    metadata:
      labels:
        app: {app_name}
        component: postgres
    spec:
      containers:
      - name: postgres
        image: {postgres_config['image']}
        ports:
        - containerPort: {postgres_config['port']}
        env:
        - name: POSTGRES_DB
          value: "aicodegen"
        - name: POSTGRES_USER
          value: "app"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {app_name}-secret
              key: postgres-password
        resources:
          limits:
            cpu: {postgres_config['resources']['cpu']}
            memory: {postgres_config['resources']['memory']}
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: {app_name}-postgres-pvc
"""
        
        # Service - PostgreSQL
        manifests['service-postgres.yaml'] = f"""apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: {namespace}
  labels:
    app: {app_name}
    component: postgres
spec:
  selector:
    app: {app_name}
    component: postgres
  ports:
  - port: {postgres_config['port']}
    targetPort: {postgres_config['port']}
  type: ClusterIP
"""
        
        # PVC - PostgreSQL
        manifests['pvc-postgres.yaml'] = f"""apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {app_name}-postgres-pvc
  namespace: {namespace}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
"""
        
        # Ingress
        if self.config['kubernetes']['ingress']['enabled']:
            ingress_config = self.config['kubernetes']['ingress']
            manifests['ingress.yaml'] = f"""apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {app_name}-ingress
  namespace: {namespace}
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - {ingress_config['host']}
    secretName: {app_name}-tls
  rules:
  - host: {ingress_config['host']}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {app_name}-app
            port:
              number: 80
"""
        
        # HPA (Horizontal Pod Autoscaler)
        if self.config['kubernetes']['autoscaling']['enabled']:
            hpa_config = self.config['kubernetes']['autoscaling']
            manifests['hpa.yaml'] = f"""apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {app_name}-hpa
  namespace: {namespace}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {app_name}-app
  minReplicas: {hpa_config['min_replicas']}
  maxReplicas: {hpa_config['max_replicas']}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {hpa_config['target_cpu']}
"""
        
        return manifests
    
    def build_docker_image(self, service: str = 'app', tag: str = None) -> bool:
        """构建Docker镜像"""
        try:
            if not tag:
                tag = f"{self.config['registry']['namespace']}/{self.config['app_name']}:{self.config['version']}"
            
            # 生成Dockerfile
            dockerfile_content = self.generate_dockerfile(service)
            dockerfile_path = os.path.join(self.project_path, f'Dockerfile.{service}')
            with open(dockerfile_path, 'w', encoding='utf-8') as f:
                f.write(dockerfile_content)
            
            # 构建镜像
            cmd = ['docker', 'build', '-f', dockerfile_path, '-t', tag, '.']
            result = subprocess.run(cmd, cwd=self.project_path, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Docker镜像构建成功: {tag}")
                return True
            else:
                print(f"❌ Docker镜像构建失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 构建Docker镜像时出错: {str(e)}")
            return False
    
    def push_docker_image(self, service: str = 'app', tag: str = None) -> bool:
        """推送Docker镜像"""
        try:
            if not tag:
                tag = f"{self.config['registry']['namespace']}/{self.config['app_name']}:{self.config['version']}"
            
            # 登录到镜像仓库
            registry_config = self.config['registry']
            if registry_config.get('username') and registry_config.get('password'):
                login_cmd = ['docker', 'login', registry_config['url'], 
                           '-u', registry_config['username'], 
                           '-p', registry_config['password']]
                subprocess.run(login_cmd, check=True)
            
            # 推送镜像
            push_cmd = ['docker', 'push', tag]
            result = subprocess.run(push_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Docker镜像推送成功: {tag}")
                return True
            else:
                print(f"❌ Docker镜像推送失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 推送Docker镜像时出错: {str(e)}")
            return False
    
    def deploy_with_docker_compose(self, environment: str = 'development') -> bool:
        """使用Docker Compose部署"""
        try:
            # 生成docker-compose.yml
            compose_content = self.generate_docker_compose()
            compose_file = os.path.join(self.project_path, f'docker-compose.{environment}.yml')
            with open(compose_file, 'w', encoding='utf-8') as f:
                f.write(compose_content)
            
            # 部署
            cmd = ['docker-compose', '-f', compose_file, 'up', '-d']
            result = subprocess.run(cmd, cwd=self.project_path, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Docker Compose部署成功 ({environment})")
                return True
            else:
                print(f"❌ Docker Compose部署失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Docker Compose部署时出错: {str(e)}")
            return False
    
    def deploy_to_kubernetes(self, namespace: str = None) -> bool:
        """部署到Kubernetes"""
        try:
            if not namespace:
                namespace = self.config['kubernetes']['namespace']
            
            # 生成Kubernetes清单文件
            manifests = self.generate_kubernetes_manifests()
            
            # 创建manifests目录
            manifests_dir = os.path.join(self.project_path, 'k8s-manifests')
            os.makedirs(manifests_dir, exist_ok=True)
            
            # 写入清单文件
            for filename, content in manifests.items():
                manifest_path = os.path.join(manifests_dir, filename)
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            # 应用清单文件
            cmd = ['kubectl', 'apply', '-f', manifests_dir]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Kubernetes部署成功 (namespace: {namespace})")
                return True
            else:
                print(f"❌ Kubernetes部署失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Kubernetes部署时出错: {str(e)}")
            return False
    
    def generate_helm_chart(self) -> Dict[str, str]:
        """生成Helm Chart"""
        chart_files = {}
        app_name = self.config['app_name']
        
        # Chart.yaml
        chart_files['Chart.yaml'] = f"""apiVersion: v2
name: {app_name}
description: AI代码生成器Helm Chart
type: application
version: {self.config['version']}
appVersion: "{self.config['version']}"
maintainers:
  - name: AI Code Team
    email: <EMAIL>
"""
        
        # values.yaml
        chart_files['values.yaml'] = f"""# 默认配置值
replicaCount: 2

image:
  repository: {self.config['registry']['namespace']}/{app_name}
  pullPolicy: IfNotPresent
  tag: "{self.config['version']}"

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: {str(self.config['kubernetes']['ingress']['enabled']).lower()}
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: {self.config['kubernetes']['ingress']['host']}
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: {app_name}-tls
      hosts:
        - {self.config['kubernetes']['ingress']['host']}

resources:
  limits:
    cpu: {self.config['containers']['app']['resources']['cpu']}
    memory: {self.config['containers']['app']['resources']['memory']}
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: {str(self.config['kubernetes']['autoscaling']['enabled']).lower()}
  minReplicas: {self.config['kubernetes']['autoscaling']['min_replicas']}
  maxReplicas: {self.config['kubernetes']['autoscaling']['max_replicas']}
  targetCPUUtilizationPercentage: {self.config['kubernetes']['autoscaling']['target_cpu']}

redis:
  enabled: true
  image: {self.config['containers']['redis']['image']}
  resources:
    limits:
      cpu: {self.config['containers']['redis']['resources']['cpu']}
      memory: {self.config['containers']['redis']['resources']['memory']}

postgres:
  enabled: true
  image: {self.config['containers']['postgres']['image']}
  resources:
    limits:
      cpu: {self.config['containers']['postgres']['resources']['cpu']}
      memory: {self.config['containers']['postgres']['resources']['memory']}
  persistence:
    enabled: true
    size: 5Gi
"""
        
        return chart_files
    
    def create_container_files(self):
        """创建所有容器相关文件"""
        # 创建deployment目录
        deployment_dir = os.path.join(self.project_path, 'deployment')
        os.makedirs(deployment_dir, exist_ok=True)
        
        # 生成Dockerfile
        dockerfile_content = self.generate_dockerfile()
        with open(os.path.join(self.project_path, 'Dockerfile'), 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)
        
        # 生成docker-compose.yml
        compose_content = self.generate_docker_compose()
        with open(os.path.join(self.project_path, 'docker-compose.yml'), 'w', encoding='utf-8') as f:
            f.write(compose_content)
        
        # 生成Kubernetes清单文件
        manifests = self.generate_kubernetes_manifests()
        k8s_dir = os.path.join(deployment_dir, 'kubernetes')
        os.makedirs(k8s_dir, exist_ok=True)
        
        for filename, content in manifests.items():
            with open(os.path.join(k8s_dir, filename), 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 生成Helm Chart
        helm_files = self.generate_helm_chart()
        helm_dir = os.path.join(deployment_dir, 'helm', self.config['app_name'])
        os.makedirs(helm_dir, exist_ok=True)
        
        for filename, content in helm_files.items():
            with open(os.path.join(helm_dir, filename), 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 生成.dockerignore
        dockerignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
docs/
*.md

# Tests
tests/
test_*/
*_test.py

# Deployment
deployment/
k8s-manifests/
build_temp/
"""
        
        with open(os.path.join(self.project_path, '.dockerignore'), 'w', encoding='utf-8') as f:
            f.write(dockerignore_content)
        
        print("✅ 容器化部署文件生成完成!")
        print("📁 生成的文件:")
        print("  - Dockerfile")
        print("  - docker-compose.yml")
        print("  - .dockerignore")
        print("  - deployment/kubernetes/ (K8s清单文件)")
        print("  - deployment/helm/ (Helm Chart)")