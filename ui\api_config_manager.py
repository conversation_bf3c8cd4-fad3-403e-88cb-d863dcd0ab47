# API配置管理器

import json
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QTextEdit, QComboBox, QTableWidget, QTableWidgetItem,
                            QMessageBox, QTabWidget, QWidget, QFormLayout, QLineEdit,
                            QCheckBox, QSpinBox, QGroupBox, QScrollArea, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

class APIConfigManager(QDialog):
    """API配置管理器"""
    config_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('API配置管理器')
        self.setGeometry(200, 200, 900, 700)
        self.config_file = 'api_config.json'
        self.api_configs = {}
        self.init_ui()
        self.load_config()
    
    def init_ui(self):
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建标题
        title_label = QLabel('AI API 配置管理器')
        title_label.setFont(QFont('SimHei', 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        tabs = QTabWidget()
        
        # 国外AI提供商标签页
        foreign_tab = self._create_foreign_providers_tab()
        tabs.addTab(foreign_tab, '国外AI提供商')
        
        # 国内AI提供商标签页
        domestic_tab = self._create_domestic_providers_tab()
        tabs.addTab(domestic_tab, '国内AI提供商')
        
        # 配置管理标签页
        management_tab = self._create_management_tab()
        tabs.addTab(management_tab, '配置管理')
        
        # 使用统计标签页
        stats_tab = self._create_stats_tab()
        tabs.addTab(stats_tab, '使用统计')
        
        main_layout.addWidget(tabs)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton('测试连接')
        self.test_btn.clicked.connect(self.test_connections)
        
        self.save_btn = QPushButton('保存配置')
        self.save_btn.clicked.connect(self.save_config)
        
        self.load_btn = QPushButton('加载配置')
        self.load_btn.clicked.connect(self.load_config)
        
        self.reset_btn = QPushButton('重置配置')
        self.reset_btn.clicked.connect(self.reset_config)
        
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(self.test_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.load_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(button_layout)
    
    def _create_foreign_providers_tab(self):
        """创建国外AI提供商配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 国外AI提供商配置
        foreign_providers = [
            ('OpenAI GPT-4', 'sk-...', 'https://platform.openai.com/api-keys'),
            ('Anthropic Claude', 'sk-ant-...', 'https://console.anthropic.com/'),
            ('Google Gemini', 'AIza...', 'https://makersuite.google.com/app/apikey'),
            ('Microsoft Azure OpenAI', 'your-api-key', 'https://portal.azure.com/'),
            ('Cohere Command', 'your-api-key', 'https://dashboard.cohere.ai/api-keys'),
            ('Hugging Face', 'hf_...', 'https://huggingface.co/settings/tokens'),
            ('Replicate', 'r8_...', 'https://replicate.com/account/api-tokens'),
            ('Together AI', 'your-api-key', 'https://api.together.xyz/settings/api-keys'),
            ('Fireworks AI', 'fw_...', 'https://fireworks.ai/account/api-keys'),
            ('Groq', 'gsk_...', 'https://console.groq.com/keys'),
            ('Perplexity AI', 'pplx-...', 'https://www.perplexity.ai/settings/api'),
            ('Mistral AI', 'your-api-key', 'https://console.mistral.ai/api-keys/')
        ]
        
        self.foreign_inputs = {}
        
        for provider, placeholder, doc_url in foreign_providers:
            group = QGroupBox(provider)
            group_layout = QFormLayout(group)
            
            # API密钥输入
            api_key_input = QLineEdit()
            api_key_input.setPlaceholderText(placeholder)
            api_key_input.setEchoMode(QLineEdit.Password)
            group_layout.addRow('API密钥:', api_key_input)
            
            # 显示/隐藏密钥按钮
            show_btn = QPushButton('显示')
            show_btn.setCheckable(True)
            show_btn.toggled.connect(lambda checked, inp=api_key_input: 
                                   inp.setEchoMode(QLineEdit.Normal if checked else QLineEdit.Password))
            group_layout.addRow('', show_btn)
            
            # 启用状态
            enabled_cb = QCheckBox('启用此API')
            enabled_cb.setChecked(True)
            group_layout.addRow('', enabled_cb)
            
            # 文档链接
            doc_btn = QPushButton('获取API密钥')
            doc_btn.clicked.connect(lambda _, url=doc_url: self.open_url(url))
            group_layout.addRow('', doc_btn)
            
            self.foreign_inputs[provider] = {
                'api_key': api_key_input,
                'enabled': enabled_cb,
                'show_btn': show_btn
            }
            
            scroll_layout.addWidget(group)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        return widget
    
    def _create_domestic_providers_tab(self):
        """创建国内AI提供商配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 国内AI提供商配置
        domestic_providers = [
            ('百度文心一言', 'API Key,Secret Key', 'https://cloud.baidu.com/product/wenxinworkshop'),
            ('阿里通义千问', 'sk-...', 'https://dashscope.aliyuncs.com/'),
            ('腾讯混元大模型', 'your-api-key', 'https://cloud.tencent.com/product/hunyuan'),
            ('DeepSeek', 'sk-...', 'https://platform.deepseek.com/api_keys'),
            ('月之暗面', 'sk-...', 'https://platform.moonshot.cn/console/api-keys'),
            ('智谱清言', 'your-api-key', 'https://open.bigmodel.cn/usercenter/apikeys'),
            ('讯飞星火', 'your-api-key', 'https://console.xfyun.cn/services/bm35'),
            ('商汤日日新', 'your-api-key', 'https://platform.sensenova.cn/'),
            ('MiniMax', 'your-api-key', 'https://api.minimax.chat/'),
            ('百川智能', 'your-api-key', 'https://platform.baichuan-ai.com/console/apikey'),
            ('零一万物', 'your-api-key', 'https://platform.lingyiwanwu.com/'),
            ('阶跃星辰', 'your-api-key', 'https://platform.stepfun.com/interface/keys'),
            ('字节豆包', 'your-api-key', 'https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey'),
            ('百度千帆', 'API Key,Secret Key', 'https://qianfan.cloud.baidu.com/'),
            ('阿里灵积', 'sk-...', 'https://dashscope.aliyuncs.com/')
        ]
        
        self.domestic_inputs = {}
        
        for provider, placeholder, doc_url in domestic_providers:
            group = QGroupBox(provider)
            group_layout = QFormLayout(group)
            
            # API密钥输入
            api_key_input = QLineEdit()
            api_key_input.setPlaceholderText(placeholder)
            api_key_input.setEchoMode(QLineEdit.Password)
            group_layout.addRow('API密钥:', api_key_input)
            
            # 显示/隐藏密钥按钮
            show_btn = QPushButton('显示')
            show_btn.setCheckable(True)
            show_btn.toggled.connect(lambda checked, inp=api_key_input: 
                                   inp.setEchoMode(QLineEdit.Normal if checked else QLineEdit.Password))
            group_layout.addRow('', show_btn)
            
            # 启用状态
            enabled_cb = QCheckBox('启用此API')
            enabled_cb.setChecked(True)
            group_layout.addRow('', enabled_cb)
            
            # 文档链接
            doc_btn = QPushButton('获取API密钥')
            doc_btn.clicked.connect(lambda _, url=doc_url: self.open_url(url))
            group_layout.addRow('', doc_btn)
            
            self.domestic_inputs[provider] = {
                'api_key': api_key_input,
                'enabled': enabled_cb,
                'show_btn': show_btn
            }
            
            scroll_layout.addWidget(group)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        return widget
    
    def _create_management_tab(self):
        """创建配置管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 批量操作
        batch_group = QGroupBox('批量操作')
        batch_layout = QFormLayout(batch_group)
        
        # 全部启用/禁用
        enable_all_btn = QPushButton('启用所有API')
        enable_all_btn.clicked.connect(self.enable_all_apis)
        disable_all_btn = QPushButton('禁用所有API')
        disable_all_btn.clicked.connect(self.disable_all_apis)
        
        batch_btn_layout = QHBoxLayout()
        batch_btn_layout.addWidget(enable_all_btn)
        batch_btn_layout.addWidget(disable_all_btn)
        batch_layout.addRow('API状态:', batch_btn_layout)
        
        # 导入/导出配置
        import_btn = QPushButton('导入配置文件')
        import_btn.clicked.connect(self.import_config)
        export_btn = QPushButton('导出配置文件')
        export_btn.clicked.connect(self.export_config)
        
        io_btn_layout = QHBoxLayout()
        io_btn_layout.addWidget(import_btn)
        io_btn_layout.addWidget(export_btn)
        batch_layout.addRow('配置文件:', io_btn_layout)
        
        layout.addWidget(batch_group)
        
        # 配置预览
        preview_group = QGroupBox('配置预览')
        preview_layout = QVBoxLayout(preview_group)
        
        self.config_preview = QTextEdit()
        self.config_preview.setReadOnly(True)
        self.config_preview.setFont(QFont('Consolas', 9))
        preview_layout.addWidget(self.config_preview)
        
        refresh_preview_btn = QPushButton('刷新预览')
        refresh_preview_btn.clicked.connect(self.refresh_preview)
        preview_layout.addWidget(refresh_preview_btn)
        
        layout.addWidget(preview_group)
        
        return widget
    
    def _create_stats_tab(self):
        """创建使用统计标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 统计表格
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(5)
        self.stats_table.setHorizontalHeaderLabels(['API提供商', '请求次数', '成功次数', '失败次数', '成功率'])
        layout.addWidget(self.stats_table)
        
        # 统计操作按钮
        stats_btn_layout = QHBoxLayout()
        
        refresh_stats_btn = QPushButton('刷新统计')
        refresh_stats_btn.clicked.connect(self.refresh_stats)
        
        clear_stats_btn = QPushButton('清空统计')
        clear_stats_btn.clicked.connect(self.clear_stats)
        
        export_stats_btn = QPushButton('导出统计')
        export_stats_btn.clicked.connect(self.export_stats)
        
        stats_btn_layout.addWidget(refresh_stats_btn)
        stats_btn_layout.addWidget(clear_stats_btn)
        stats_btn_layout.addWidget(export_stats_btn)
        stats_btn_layout.addStretch()
        
        layout.addLayout(stats_btn_layout)
        
        return widget
    
    def open_url(self, url):
        """打开URL"""
        import webbrowser
        webbrowser.open(url)
    
    def enable_all_apis(self):
        """启用所有API"""
        for inputs in list(self.foreign_inputs.values()) + list(self.domestic_inputs.values()):
            inputs['enabled'].setChecked(True)
    
    def disable_all_apis(self):
        """禁用所有API"""
        for inputs in list(self.foreign_inputs.values()) + list(self.domestic_inputs.values()):
            inputs['enabled'].setChecked(False)
    
    def test_connections(self):
        """测试API连接"""
        QMessageBox.information(self, '测试连接', '连接测试功能正在开发中...')
    
    def save_config(self):
        """保存配置"""
        config = {}
        
        # 收集国外API配置
        for provider, inputs in self.foreign_inputs.items():
            config[provider] = {
                'api_key': inputs['api_key'].text(),
                'enabled': inputs['enabled'].isChecked()
            }
        
        # 收集国内API配置
        for provider, inputs in self.domestic_inputs.items():
            config[provider] = {
                'api_key': inputs['api_key'].text(),
                'enabled': inputs['enabled'].isChecked()
            }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.api_configs = config
            self.config_updated.emit(config)
            QMessageBox.information(self, '成功', '配置已保存!')
            
        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存配置失败: {str(e)}')
    
    def load_config(self):
        """加载配置"""
        if not os.path.exists(self.config_file):
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 应用国外API配置
            for provider, settings in config.items():
                if provider in self.foreign_inputs:
                    inputs = self.foreign_inputs[provider]
                    inputs['api_key'].setText(settings.get('api_key', ''))
                    inputs['enabled'].setChecked(settings.get('enabled', True))
                elif provider in self.domestic_inputs:
                    inputs = self.domestic_inputs[provider]
                    inputs['api_key'].setText(settings.get('api_key', ''))
                    inputs['enabled'].setChecked(settings.get('enabled', True))
            
            self.api_configs = config
            self.refresh_preview()
            
        except Exception as e:
            QMessageBox.critical(self, '错误', f'加载配置失败: {str(e)}')
    
    def reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(self, '确认', '确定要重置所有配置吗？', 
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            # 清空所有输入
            for inputs in list(self.foreign_inputs.values()) + list(self.domestic_inputs.values()):
                inputs['api_key'].clear()
                inputs['enabled'].setChecked(True)
            
            self.api_configs = {}
            self.refresh_preview()
    
    def import_config(self):
        """导入配置文件"""
        from PyQt5.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getOpenFileName(self, '导入配置', '', 'JSON文件 (*.json)')
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 应用配置
                for provider, settings in config.items():
                    if provider in self.foreign_inputs:
                        inputs = self.foreign_inputs[provider]
                        inputs['api_key'].setText(settings.get('api_key', ''))
                        inputs['enabled'].setChecked(settings.get('enabled', True))
                    elif provider in self.domestic_inputs:
                        inputs = self.domestic_inputs[provider]
                        inputs['api_key'].setText(settings.get('api_key', ''))
                        inputs['enabled'].setChecked(settings.get('enabled', True))
                
                QMessageBox.information(self, '成功', '配置导入成功!')
                
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导入配置失败: {str(e)}')
    
    def export_config(self):
        """导出配置文件"""
        from PyQt5.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(self, '导出配置', 'api_config.json', 'JSON文件 (*.json)')
        if file_path:
            try:
                config = {}
                
                # 收集配置
                for provider, inputs in list(self.foreign_inputs.items()) + list(self.domestic_inputs.items()):
                    config[provider] = {
                        'api_key': inputs['api_key'].text(),
                        'enabled': inputs['enabled'].isChecked()
                    }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                QMessageBox.information(self, '成功', '配置导出成功!')
                
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出配置失败: {str(e)}')
    
    def refresh_preview(self):
        """刷新配置预览"""
        config = {}
        
        # 收集当前配置
        for provider, inputs in list(self.foreign_inputs.items()) + list(self.domestic_inputs.items()):
            if inputs['api_key'].text() or inputs['enabled'].isChecked():
                config[provider] = {
                    'api_key': '***' if inputs['api_key'].text() else '',
                    'enabled': inputs['enabled'].isChecked()
                }
        
        # 显示配置预览
        preview_text = json.dumps(config, indent=2, ensure_ascii=False)
        self.config_preview.setText(preview_text)
    
    def refresh_stats(self):
        """刷新使用统计"""
        # 这里应该从API集成器获取实际统计数据
        # 暂时显示示例数据
        sample_stats = [
            ('OpenAI GPT-4', 150, 145, 5, '96.7%'),
            ('Anthropic Claude', 80, 78, 2, '97.5%'),
            ('百度文心一言', 120, 115, 5, '95.8%'),
            ('DeepSeek', 90, 88, 2, '97.8%')
        ]
        
        self.stats_table.setRowCount(len(sample_stats))
        for i, (provider, total, success, failed, rate) in enumerate(sample_stats):
            self.stats_table.setItem(i, 0, QTableWidgetItem(provider))
            self.stats_table.setItem(i, 1, QTableWidgetItem(str(total)))
            self.stats_table.setItem(i, 2, QTableWidgetItem(str(success)))
            self.stats_table.setItem(i, 3, QTableWidgetItem(str(failed)))
            self.stats_table.setItem(i, 4, QTableWidgetItem(rate))
    
    def clear_stats(self):
        """清空统计数据"""
        reply = QMessageBox.question(self, '确认', '确定要清空所有统计数据吗？', 
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.stats_table.setRowCount(0)
            QMessageBox.information(self, '成功', '统计数据已清空!')
    
    def export_stats(self):
        """导出统计数据"""
        from PyQt5.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(self, '导出统计', 'api_stats.csv', 'CSV文件 (*.csv)')
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # 写入表头
                    f.write('API提供商,请求次数,成功次数,失败次数,成功率\\n')
                    
                    # 写入数据
                    for row in range(self.stats_table.rowCount()):
                        row_data = []
                        for col in range(self.stats_table.columnCount()):
                            item = self.stats_table.item(row, col)
                            row_data.append(item.text() if item else '')
                        f.write(','.join(row_data) + '\\n')
                
                QMessageBox.information(self, '成功', '统计数据导出成功!')
                
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出统计失败: {str(e)}')
    
    def get_config(self):
        """获取当前配置"""
        return self.api_configs