def launch_debugger():
    """启动调试器"""
    try:
        # 新增: 导入sys模块解决未定义问题
        import sys
        from pathlib import Path
        
        # 动态导入调试界面
        try:
            import importlib
            # 新增: 使用绝对导入确保路径正确
            # 修复: 检查模块是否存在
            import os
            debug_interface_path = Path(__file__).parent / "ui" / "debug_interface.py"
            if not debug_interface_path.exists():
                print("❌ 调试界面文件不存在，请确认已创建 ui/debug_interface.py")
                return 1
                
            # 添加ui目录到Python路径
            ui_path = str(Path(__file__).parent / "ui")
            if ui_path not in sys.path:
                sys.path.append(ui_path)
                
            debug_interface = importlib.import_module('debug_interface')
            from PyQt5.QtWidgets import QApplication
            app = QApplication(sys.argv)
            # 新增: 验证类是否可实例化
            from debug_interface import DebugInterface
            if not isinstance(DebugInterface, type):
                raise AttributeError("DebugInterface类未定义")
            debug_window = DebugInterface()
            debug_window.show()
        except AttributeError as e:
            print(f"❌ 调试界面模块错误: {e}")
            
    except ImportError as e:
        print(f"❌ 无法启动调试器界面: {e}")
        # 新增: 明确提示如何修复
        print("💡 请确认: 1. 已创建 ui/debug_interface.py 2. 已安装PyQt5")
        return 1

def deploy_to_aliyun(config):
    """部署到阿里云"""
    # 新增: 示例阿里云部署核心逻辑
    # 修复: 添加导入检查和错误处理
    try:
        try:
            import aliyunsdkcore.client
            from aliyunsdkecs.request.v20140526 import RunInstancesRequest
        except ImportError as e:
            print(f"❌ 阿里云SDK未安装: {e}")
            print("💡 请运行: pip install aliyun-python-sdk-core aliyun-python-sdk-ecs")
            return
            
        clt = aliyunsdkcore.client.AcsClient(
            config['access_key_id'],
            config['access_key_secret'],
            config.get('region', 'cn-hangzhou')
        )
        
        req = RunInstancesRequest.RunInstancesRequest()
        req.set_ImageId('your_image_id')
        req.set_InstanceType('ecs.sn1ne.large')
        req.set_SecurityGroupId('your_sg_id')
        
        response = clt.do_action_with_exception(req)
        print(f"✅ 实例部署成功: {response}")
    except Exception as e:
        print(f"❌ 阿里云部署失败: {str(e)}")

def deploy_to_aws(config):
    """部署到AWS"""
    # 新增: AWS基础部署示例
    # 修复: 添加导入检查和错误处理
    try:
        try:
            import boto3
        except ImportError as e:
            print(f"❌ AWS SDK未安装: {e}")
            print("💡 请运行: pip install boto3")
            return
            
        ec2 = boto3.client(
            'ec2',
            aws_access_key_id=config['aws_access_key_id'],
            aws_secret_access_key=config['aws_secret_access_key'],
            region_name=config.get('region', 'us-east-1')
        )
        
        response = ec2.run_instances(
            ImageId='ami-0c55b159cbfafe1f0',
            InstanceType='t2.micro',
            MinCount=1,
            MaxCount=1
        )
        print(f"✅ 实例创建成功: {response['Instances'][0]['InstanceId']}")
    except Exception as e:
        print(f"❌ AWS部署失败: {str(e)}")

def deploy_to_azure(config):
    """部署到Azure"""
    # 新增: Azure部署基础验证
    # 修复: 添加导入检查和错误处理
    try:
        try:
            from azure.identity import ClientSecretCredential
            from azure.mgmt.compute import ComputeManagementClient
        except ImportError as e:
            print(f"❌ Azure SDK未安装: {e}")
            print("💡 请运行: pip install azure-identity azure-mgmt-compute")
            return
            
        credential = ClientSecretCredential(
            tenant_id=config['tenant_id'],
            client_id=config['client_id'],
            client_secret=config['client_secret']
        )
        
        compute_client = ComputeManagementClient(
            credential,
            subscription_id=config['subscription_id']
        )
        
        # 示例: 列出所有可用区域
        regions = compute_client.usage.list(location="eastus")
        print(f"✅ Azure连接成功，可用区域资源: {list(regions)}")
    except Exception as e:
        print(f"❌ Azure部署失败: {str(e)}")

def configure_pack_options():
    """配置打包选项"""
    # 新增: 初始化hidden_imports列表
    hidden_imports = []
    
    # 新增: 添加隐式导入校验
    print("🔍 正在验证隐式导入...")
    default_hidden_imports = ["PyQt5.QtCore", "PyQt5.QtGui", "PyQt5.QtWidgets"]
    for imp in default_hidden_imports:
        try:
            import importlib
            importlib.import_module(imp)
        except ImportError:
            print(f"⚠️  隐式导入缺失: {imp}")
            if input(f"   是否添加? (Y/n): ").lower() != 'n':
                hidden_imports.append(imp)
    

def main():
    # 新增: 初始化变量解决未定义问题
    import argparse
    parser = argparse.ArgumentParser()
    args = parser.parse_args([])
    
    # 新增: 参数冲突强化检查
    interface_args = sum([args.modern, args.enhanced, args.demo])
    if interface_args > 1:
        print("❌ 错误: 冲突的界面参数 - 请仅指定一个界面模式")
        parser.print_help()
        return 1
    
    # 新增: 检查调试参数互斥
    # 修复: 添加检查确保args具有相应属性
    debug_enabled = getattr(args, 'debug', False) if hasattr(args, 'debug') else False
    debugger_enabled = getattr(args, 'debugger', False) if hasattr(args, 'debugger') else False
    
    if debug_enabled and debugger_enabled:
        print("❌ 错误: --debug 和 --debugger 参数不可同时使用")
        return 1