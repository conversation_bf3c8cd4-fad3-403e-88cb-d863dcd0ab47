#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的AI代码生成器
提供更智能的代码生成、优化和调试功能
"""

import asyncio
import json
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import requests
import aiohttp

class CodeLanguage(Enum):
    """支持的编程语言"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CSHARP = "csharp"
    CPP = "cpp"
    C = "c"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    RUBY = "ruby"
    SWIFT = "swift"
    KOTLIN = "kotlin"
    SCALA = "scala"
    R = "r"
    MATLAB = "matlab"
    SQL = "sql"
    HTML = "html"
    CSS = "css"

class CodeComplexity(Enum):
    """代码复杂度"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    EXPERT = "expert"

class CodeStyle(Enum):
    """代码风格"""
    CLEAN = "clean"
    FUNCTIONAL = "functional"
    OBJECT_ORIENTED = "object_oriented"
    PROCEDURAL = "procedural"
    REACTIVE = "reactive"

@dataclass
class CodeGenerationRequest:
    """代码生成请求"""
    prompt: str
    language: CodeLanguage
    complexity: CodeComplexity = CodeComplexity.MEDIUM
    style: CodeStyle = CodeStyle.CLEAN
    include_tests: bool = True
    include_docs: bool = True
    include_comments: bool = True
    max_tokens: int = 2048
    temperature: float = 0.7
    context: Optional[Dict[str, Any]] = None
    requirements: Optional[List[str]] = None
    constraints: Optional[List[str]] = None

@dataclass
class CodeGenerationResponse:
    """代码生成响应"""
    code: str
    tests: Optional[str] = None
    documentation: Optional[str] = None
    explanation: Optional[str] = None
    suggestions: Optional[List[str]] = None
    quality_score: Optional[float] = None
    complexity_analysis: Optional[Dict[str, Any]] = None
    performance_notes: Optional[List[str]] = None
    security_notes: Optional[List[str]] = None
    tokens_used: int = 0
    generation_time: float = 0.0
    provider: str = ""
    model: str = ""

class EnhancedCodeGenerator:
    """增强的代码生成器"""
    
    def __init__(self):
        self.api_configs = self.load_api_configs()
        self.generation_history: List[CodeGenerationResponse] = []
        self.templates = self.load_templates()
        self.best_practices = self.load_best_practices()
        
    def load_api_configs(self) -> Dict[str, Any]:
        """加载API配置"""
        try:
            with open("api_configs.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"加载API配置失败: {e}")
            return {}
    
    def load_templates(self) -> Dict[str, Dict[str, str]]:
        """加载代码模板"""
        templates = {
            CodeLanguage.PYTHON: {
                "function": '''def {function_name}({parameters}):
    """
    {description}
    
    Args:
        {args_docs}
    
    Returns:
        {return_docs}
    """
    {body}''',
                "class": '''class {class_name}:
    """
    {description}
    """
    
    def __init__(self{init_params}):
        """初始化{class_name}"""
        {init_body}
    
    {methods}''',
                "module": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{module_description}
"""

{imports}

{constants}

{classes}

{functions}

if __name__ == "__main__":
    {main_code}'''
            },
            CodeLanguage.JAVASCRIPT: {
                "function": '''/**
 * {description}
 * @param {{*}} {parameters}
 * @returns {{*}} {return_docs}
 */
function {function_name}({parameters}) {{
    {body}
}}''',
                "class": '''/**
 * {description}
 */
class {class_name} {{
    constructor({constructor_params}) {{
        {constructor_body}
    }}
    
    {methods}
}}''',
                "module": '''/**
 * {module_description}
 */

{imports}

{constants}

{classes}

{functions}

export {{ {exports} }};'''
            }
        }
        return templates
    
    def load_best_practices(self) -> Dict[CodeLanguage, List[str]]:
        """加载最佳实践"""
        practices = {
            CodeLanguage.PYTHON: [
                "使用类型提示提高代码可读性",
                "遵循PEP 8代码风格指南",
                "使用docstring文档化函数和类",
                "适当使用异常处理",
                "避免使用可变默认参数",
                "使用列表推导式和生成器表达式",
                "遵循单一职责原则",
                "使用上下文管理器处理资源"
            ],
            CodeLanguage.JAVASCRIPT: [
                "使用const和let而不是var",
                "使用箭头函数简化代码",
                "避免回调地狱，使用Promise或async/await",
                "使用严格模式('use strict')",
                "避免全局变量污染",
                "使用模块化开发",
                "进行错误处理",
                "使用现代ES6+语法"
            ]
        }
        return practices
    
    async def generate_code(self, request: CodeGenerationRequest) -> CodeGenerationResponse:
        """生成代码"""
        start_time = time.time()
        
        try:
            # 构建增强的提示
            enhanced_prompt = self.build_enhanced_prompt(request)
            
            # 选择最佳API提供商
            provider, model = self.select_best_provider(request)
            
            # 生成代码
            code_result = await self.call_api(provider, model, enhanced_prompt, request)
            
            # 后处理
            response = await self.post_process_code(code_result, request)
            
            # 记录生成时间
            response.generation_time = time.time() - start_time
            response.provider = provider
            response.model = model
            
            # 保存到历史记录
            self.generation_history.append(response)
            
            return response
            
        except Exception as e:
            print(f"代码生成失败: {e}")
            return CodeGenerationResponse(
                code=f"# 代码生成失败: {str(e)}",
                generation_time=time.time() - start_time
            )
    
    def build_enhanced_prompt(self, request: CodeGenerationRequest) -> str:
        """构建增强的提示"""
        prompt_parts = []
        
        # 基础提示
        prompt_parts.append(f"请用{request.language.value}语言生成代码。")
        
        # 复杂度要求
        complexity_desc = {
            CodeComplexity.SIMPLE: "简单易懂的",
            CodeComplexity.MEDIUM: "中等复杂度的",
            CodeComplexity.COMPLEX: "复杂但高效的",
            CodeComplexity.EXPERT: "专家级的高级"
        }
        prompt_parts.append(f"代码应该是{complexity_desc[request.complexity]}。")
        
        # 风格要求
        style_desc = {
            CodeStyle.CLEAN: "简洁清晰",
            CodeStyle.FUNCTIONAL: "函数式编程",
            CodeStyle.OBJECT_ORIENTED: "面向对象",
            CodeStyle.PROCEDURAL: "过程式",
            CodeStyle.REACTIVE: "响应式编程"
        }
        prompt_parts.append(f"使用{style_desc[request.style]}的编程风格。")
        
        # 最佳实践
        if request.language in self.best_practices:
            practices = self.best_practices[request.language][:3]  # 取前3个
            prompt_parts.append(f"请遵循以下最佳实践: {', '.join(practices)}")
        
        # 具体要求
        if request.include_comments:
            prompt_parts.append("包含详细的注释。")
        
        if request.include_docs:
            prompt_parts.append("包含完整的文档字符串。")
        
        if request.include_tests:
            prompt_parts.append("生成相应的单元测试。")
        
        # 约束条件
        if request.constraints:
            prompt_parts.append(f"约束条件: {', '.join(request.constraints)}")
        
        # 需求
        if request.requirements:
            prompt_parts.append(f"功能需求: {', '.join(request.requirements)}")
        
        # 上下文
        if request.context:
            context_str = json.dumps(request.context, ensure_ascii=False, indent=2)
            prompt_parts.append(f"上下文信息: {context_str}")
        
        # 用户提示
        prompt_parts.append(f"\n用户需求: {request.prompt}")
        
        return "\n".join(prompt_parts)
    
    def select_best_provider(self, request: CodeGenerationRequest) -> Tuple[str, str]:
        """选择最佳API提供商"""
        # 根据语言和复杂度选择最佳提供商
        language_preferences = {
            CodeLanguage.PYTHON: ["openai", "anthropic", "google"],
            CodeLanguage.JAVASCRIPT: ["openai", "anthropic", "google"],
            CodeLanguage.JAVA: ["openai", "anthropic"],
            CodeLanguage.CSHARP: ["openai", "azure"],
            CodeLanguage.GO: ["openai", "anthropic"],
            CodeLanguage.RUST: ["anthropic", "openai"],
        }
        
        preferred_providers = language_preferences.get(request.language, ["openai"])
        
        # 检查可用的提供商
        for provider in preferred_providers:
            if provider in self.api_configs and self.api_configs[provider].get("enabled", False):
                model = self.api_configs[provider].get("model", "gpt-3.5-turbo")
                return provider, model
        
        # 默认返回第一个可用的提供商
        for provider, config in self.api_configs.items():
            if config.get("enabled", False):
                model = config.get("model", "gpt-3.5-turbo")
                return provider, model
        
        return "openai", "gpt-3.5-turbo"
    
    async def call_api(self, provider: str, model: str, prompt: str, request: CodeGenerationRequest) -> str:
        """调用API"""
        config = self.api_configs.get(provider, {})
        api_key = config.get("api_key", "")
        base_url = config.get("base_url", "")
        
        if not api_key:
            raise ValueError(f"未配置{provider}的API密钥")
        
        # 构建请求
        if provider == "openai":
            return await self.call_openai_api(api_key, base_url, model, prompt, request)
        elif provider == "anthropic":
            return await self.call_anthropic_api(api_key, base_url, model, prompt, request)
        elif provider == "google":
            return await self.call_google_api(api_key, base_url, model, prompt, request)
        else:
            raise ValueError(f"不支持的API提供商: {provider}")
    
    async def call_openai_api(self, api_key: str, base_url: str, model: str, prompt: str, request: CodeGenerationRequest) -> str:
        """调用OpenAI API"""
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "你是一个专业的代码生成助手，能够生成高质量、可维护的代码。"},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": request.max_tokens,
            "temperature": request.temperature
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/chat/completions", headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    error_text = await response.text()
                    raise Exception(f"API调用失败: {response.status} - {error_text}")
    
    async def call_anthropic_api(self, api_key: str, base_url: str, model: str, prompt: str, request: CodeGenerationRequest) -> str:
        """调用Anthropic API"""
        # 简化实现，实际应该根据Anthropic API文档实现
        return f"# 使用Anthropic API生成的{request.language.value}代码\n# TODO: 实现Anthropic API调用"
    
    async def call_google_api(self, api_key: str, base_url: str, model: str, prompt: str, request: CodeGenerationRequest) -> str:
        """调用Google API"""
        # 简化实现，实际应该根据Google API文档实现
        return f"# 使用Google API生成的{request.language.value}代码\n# TODO: 实现Google API调用"
    
    async def post_process_code(self, raw_code: str, request: CodeGenerationRequest) -> CodeGenerationResponse:
        """后处理代码"""
        # 提取代码块
        code = self.extract_code_block(raw_code)
        
        # 生成测试代码
        tests = None
        if request.include_tests:
            tests = await self.generate_tests(code, request)
        
        # 生成文档
        documentation = None
        if request.include_docs:
            documentation = await self.generate_documentation(code, request)
        
        # 代码质量分析
        quality_score = self.analyze_code_quality(code, request.language)
        
        # 复杂度分析
        complexity_analysis = self.analyze_complexity(code, request.language)
        
        # 生成建议
        suggestions = self.generate_suggestions(code, request)
        
        return CodeGenerationResponse(
            code=code,
            tests=tests,
            documentation=documentation,
            explanation=self.generate_explanation(code, request),
            suggestions=suggestions,
            quality_score=quality_score,
            complexity_analysis=complexity_analysis,
            performance_notes=self.analyze_performance(code, request.language),
            security_notes=self.analyze_security(code, request.language)
        )
    
    def extract_code_block(self, text: str) -> str:
        """提取代码块"""
        # 查找代码块标记
        import re
        
        # 匹配 ```language 或 ``` 开头的代码块
        pattern = r'```(?:\w+)?\n(.*?)\n```'
        matches = re.findall(pattern, text, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # 如果没有找到代码块标记，返回原文本
        return text.strip()
    
    async def generate_tests(self, code: str, request: CodeGenerationRequest) -> str:
        """生成测试代码"""
        # 简化实现
        if request.language == CodeLanguage.PYTHON:
            return f'''import unittest

class TestGeneratedCode(unittest.TestCase):
    """测试生成的代码"""
    
    def test_basic_functionality(self):
        """测试基本功能"""
        # TODO: 添加具体的测试用例
        pass

if __name__ == "__main__":
    unittest.main()'''
        
        return "# TODO: 生成测试代码"
    
    async def generate_documentation(self, code: str, request: CodeGenerationRequest) -> str:
        """生成文档"""
        return f"""# {request.language.value.title()} 代码文档

## 概述
这是根据用户需求生成的{request.language.value}代码。

## 功能说明
{request.prompt}

## 使用方法
```{request.language.value}
{code}
```

## 注意事项
- 请根据实际需求调整代码
- 建议进行充分的测试
- 注意代码的安全性和性能
"""
    
    def generate_explanation(self, code: str, request: CodeGenerationRequest) -> str:
        """生成代码解释"""
        return f"这段{request.language.value}代码实现了用户需求：{request.prompt}。代码遵循了{request.style.value}的编程风格，复杂度为{request.complexity.value}级别。"
    
    def analyze_code_quality(self, code: str, language: CodeLanguage) -> float:
        """分析代码质量"""
        # 简化的质量评分算法
        score = 0.7  # 基础分数
        
        # 检查注释
        if "#" in code or "//" in code or "/*" in code:
            score += 0.1
        
        # 检查函数定义
        if "def " in code or "function " in code or "func " in code:
            score += 0.1
        
        # 检查错误处理
        if "try" in code or "catch" in code or "except" in code:
            score += 0.1
        
        return min(score, 1.0)
    
    def analyze_complexity(self, code: str, language: CodeLanguage) -> Dict[str, Any]:
        """分析代码复杂度"""
        lines = code.split('\n')
        return {
            "lines_of_code": len([line for line in lines if line.strip()]),
            "cyclomatic_complexity": "中等",
            "maintainability_index": "良好"
        }
    
    def generate_suggestions(self, code: str, request: CodeGenerationRequest) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if request.language in self.best_practices:
            practices = self.best_practices[request.language]
            suggestions.extend(practices[:2])  # 取前2个建议
        
        suggestions.append("建议进行代码审查")
        suggestions.append("添加更多的单元测试")
        
        return suggestions
    
    def analyze_performance(self, code: str, language: CodeLanguage) -> List[str]:
        """分析性能"""
        notes = []
        
        if "for" in code and "in" in code:
            notes.append("注意循环的性能，考虑使用更高效的算法")
        
        if language == CodeLanguage.PYTHON and "list" in code:
            notes.append("考虑使用生成器或numpy数组提高性能")
        
        return notes
    
    def analyze_security(self, code: str, language: CodeLanguage) -> List[str]:
        """分析安全性"""
        notes = []
        
        if "input(" in code:
            notes.append("注意输入验证，防止注入攻击")
        
        if "eval(" in code or "exec(" in code:
            notes.append("避免使用eval()和exec()，存在安全风险")
        
        return notes


class EnhancedDebugger:
    """增强的代码调试器"""

    def __init__(self, code_generator: EnhancedCodeGenerator):
        self.code_generator = code_generator
        self.debug_history: List[Dict[str, Any]] = []

    async def debug_code(self, code: str, error_message: str, language: CodeLanguage) -> Dict[str, Any]:
        """调试代码"""
        debug_request = CodeGenerationRequest(
            prompt=f"请分析并修复以下代码中的错误:\n\n错误信息: {error_message}\n\n代码:\n{code}",
            language=language,
            complexity=CodeComplexity.MEDIUM,
            include_tests=True,
            include_docs=False,
            include_comments=True
        )

        # 分析错误
        error_analysis = self.analyze_error(error_message, code, language)

        # 生成修复建议
        fix_suggestions = await self.generate_fix_suggestions(code, error_message, language)

        # 生成修复后的代码
        fixed_code_response = await self.code_generator.generate_code(debug_request)

        debug_result = {
            "original_code": code,
            "error_message": error_message,
            "error_analysis": error_analysis,
            "fix_suggestions": fix_suggestions,
            "fixed_code": fixed_code_response.code,
            "tests": fixed_code_response.tests,
            "explanation": fixed_code_response.explanation,
            "confidence_score": self.calculate_confidence_score(error_analysis, fix_suggestions)
        }

        self.debug_history.append(debug_result)
        return debug_result

    def analyze_error(self, error_message: str, code: str, language: CodeLanguage) -> Dict[str, Any]:
        """分析错误"""
        analysis = {
            "error_type": self.classify_error_type(error_message),
            "severity": self.assess_error_severity(error_message),
            "likely_causes": self.identify_likely_causes(error_message, code, language),
            "affected_lines": self.find_affected_lines(error_message, code)
        }
        return analysis

    def classify_error_type(self, error_message: str) -> str:
        """分类错误类型"""
        error_keywords = {
            "SyntaxError": "语法错误",
            "NameError": "名称错误",
            "TypeError": "类型错误",
            "ValueError": "值错误",
            "IndexError": "索引错误",
            "KeyError": "键错误",
            "AttributeError": "属性错误",
            "ImportError": "导入错误",
            "IndentationError": "缩进错误"
        }

        for keyword, error_type in error_keywords.items():
            if keyword in error_message:
                return error_type

        return "未知错误"

    def assess_error_severity(self, error_message: str) -> str:
        """评估错误严重程度"""
        critical_keywords = ["Fatal", "Critical", "Segmentation fault"]
        high_keywords = ["Error", "Exception", "Failed"]
        medium_keywords = ["Warning", "Deprecated"]

        error_lower = error_message.lower()

        if any(keyword.lower() in error_lower for keyword in critical_keywords):
            return "严重"
        elif any(keyword.lower() in error_lower for keyword in high_keywords):
            return "高"
        elif any(keyword.lower() in error_lower for keyword in medium_keywords):
            return "中"
        else:
            return "低"

    def identify_likely_causes(self, error_message: str, code: str, language: CodeLanguage) -> List[str]:
        """识别可能的原因"""
        causes = []

        if "NameError" in error_message:
            causes.append("变量或函数名未定义")
            causes.append("拼写错误")
            causes.append("作用域问题")

        if "SyntaxError" in error_message:
            causes.append("语法错误")
            causes.append("括号不匹配")
            causes.append("缩进问题")

        if "TypeError" in error_message:
            causes.append("类型不匹配")
            causes.append("参数数量错误")
            causes.append("对象不支持该操作")

        return causes

    def find_affected_lines(self, error_message: str, code: str) -> List[int]:
        """查找受影响的行"""
        import re

        # 查找行号
        line_pattern = r'line (\d+)'
        matches = re.findall(line_pattern, error_message)

        if matches:
            return [int(match) for match in matches]

        return []

    async def generate_fix_suggestions(self, code: str, error_message: str, language: CodeLanguage) -> List[str]:
        """生成修复建议"""
        suggestions = []

        error_type = self.classify_error_type(error_message)

        if error_type == "语法错误":
            suggestions.extend([
                "检查括号、引号是否匹配",
                "检查缩进是否正确",
                "检查语句结尾是否缺少分号（如适用）"
            ])

        elif error_type == "名称错误":
            suggestions.extend([
                "检查变量名拼写",
                "确保变量在使用前已定义",
                "检查导入语句是否正确"
            ])

        elif error_type == "类型错误":
            suggestions.extend([
                "检查数据类型是否匹配",
                "添加类型转换",
                "检查函数参数数量和类型"
            ])

        # 添加通用建议
        suggestions.extend([
            "添加异常处理",
            "增加输入验证",
            "添加调试输出"
        ])

        return suggestions

    def calculate_confidence_score(self, error_analysis: Dict[str, Any], fix_suggestions: List[str]) -> float:
        """计算修复信心分数"""
        base_score = 0.6

        # 根据错误类型调整分数
        if error_analysis["error_type"] in ["语法错误", "缩进错误"]:
            base_score += 0.3
        elif error_analysis["error_type"] in ["名称错误", "类型错误"]:
            base_score += 0.2

        # 根据建议数量调整分数
        if len(fix_suggestions) >= 3:
            base_score += 0.1

        return min(base_score, 1.0)
