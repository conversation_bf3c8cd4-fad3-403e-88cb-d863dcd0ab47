# 多平台打包器
import os
import sys
import json
import shutil
import subprocess
import tempfile
import zipfile
import tarfile
import platform
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import threading
import time
import hashlib

class Platform(Enum):
    WINDOWS = "windows"
    MACOS = "macos"
    LINUX = "linux"
    WEB = "web"
    ANDROID = "android"
    IOS = "ios"

class Architecture(Enum):
    X86 = "x86"
    X64 = "x64"
    ARM64 = "arm64"
    ARM = "arm"

class PackageFormat(Enum):
    # Windows
    EXE = "exe"
    MSI = "msi"
    NSIS = "nsis"
    
    # macOS
    APP = "app"
    DMG = "dmg"
    PKG = "pkg"
    
    # Linux
    APPIMAGE = "appimage"
    DEB = "deb"
    RPM = "rpm"
    SNAP = "snap"
    FLATPAK = "flatpak"
    TAR_GZ = "tar.gz"
    
    # Web
    PWA = "pwa"
    ELECTRON = "electron"
    
    # Mobile
    APK = "apk"
    AAB = "aab"
    IPA = "ipa"

@dataclass
class PackageConfig:
    """打包配置"""
    name: str
    version: str
    description: str
    author: str
    license: str
    main_file: str
    icon_path: str
    output_dir: str
    platforms: List[Platform]
    architectures: List[Architecture]
    formats: List[PackageFormat]
    dependencies: List[str]
    resources: List[str]
    exclude_patterns: List[str]
    optimization: Dict[str, Any]
    signing: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class PackageResult:
    """打包结果"""
    platform: Platform
    architecture: Architecture
    format: PackageFormat
    success: bool
    output_path: str
    file_size: int
    build_time: float
    error_message: str = ""
    warnings: List[str] = None

class MultiPlatformPackager:
    """多平台打包器"""
    
    def __init__(self):
        self.config = None
        self.build_cache = {}
        self.progress_callbacks = []
        self.current_progress = 0
        self.total_tasks = 0
        
        # 工具路径
        self.tools = self._detect_tools()
        
        # 模板路径
        self.templates_dir = os.path.join(os.path.dirname(__file__), "templates")
        os.makedirs(self.templates_dir, exist_ok=True)
    
    def _detect_tools(self) -> Dict[str, str]:
        """检测可用的打包工具"""
        tools = {}
        
        # Python打包工具
        for tool in ["pyinstaller", "cx_Freeze", "py2exe", "py2app", "briefcase"]:
            if shutil.which(tool):
                tools[tool] = shutil.which(tool)
        
        # 系统工具
        system_tools = {
            "makensis": "NSIS",
            "dpkg-deb": "Debian包",
            "rpmbuild": "RPM包",
            "snapcraft": "Snap包",
            "flatpak-builder": "Flatpak",
            "appimagetool": "AppImage",
            "create-dmg": "macOS DMG",
            "pkgbuild": "macOS PKG",
            "codesign": "macOS代码签名",
            "signtool": "Windows代码签名"
        }
        
        for tool, desc in system_tools.items():
            if shutil.which(tool):
                tools[tool] = shutil.which(tool)
        
        return tools
    
    def load_config(self, config_path: str) -> PackageConfig:
        """加载打包配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 转换枚举
        config_data['platforms'] = [Platform(p) for p in config_data['platforms']]
        config_data['architectures'] = [Architecture(a) for a in config_data['architectures']]
        config_data['formats'] = [PackageFormat(f) for f in config_data['formats']]
        
        self.config = PackageConfig(**config_data)
        return self.config
    
    def create_default_config(self, project_path: str) -> PackageConfig:
        """创建默认配置"""
        project_name = os.path.basename(project_path)
        
        config = PackageConfig(
            name=project_name,
            version="1.0.0",
            description=f"{project_name} application",
            author="Developer",
            license="MIT",
            main_file="main.py",
            icon_path="icon.ico",
            output_dir="dist",
            platforms=[Platform.WINDOWS, Platform.MACOS, Platform.LINUX],
            architectures=[Architecture.X64],
            formats=[PackageFormat.EXE, PackageFormat.APP, PackageFormat.APPIMAGE],
            dependencies=[],
            resources=[],
            exclude_patterns=["__pycache__", "*.pyc", ".git", ".vscode"],
            optimization={
                "strip_debug": True,
                "compress": True,
                "optimize_imports": True,
                "bundle_python": True
            },
            signing={
                "enabled": False,
                "certificate_path": "",
                "certificate_password": "",
                "timestamp_server": ""
            },
            metadata={}
        )
        
        self.config = config
        return config
    
    def save_config(self, config_path: str):
        """保存配置"""
        if not self.config:
            raise ValueError("没有配置可保存")
        
        # 转换为可序列化的格式
        config_dict = asdict(self.config)
        config_dict['platforms'] = [p.value for p in self.config.platforms]
        config_dict['architectures'] = [a.value for a in self.config.architectures]
        config_dict['formats'] = [f.value for f in self.config.formats]
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def add_progress_callback(self, callback):
        """添加进度回调"""
        self.progress_callbacks.append(callback)
    
    def _notify_progress(self, current: int, total: int, message: str):
        """通知进度"""
        self.current_progress = current
        self.total_tasks = total
        progress = (current / total) * 100 if total > 0 else 0
        
        for callback in self.progress_callbacks:
            callback(current, total, progress, message)
    
    def package_all(self) -> List[PackageResult]:
        """打包所有平台"""
        if not self.config:
            raise ValueError("未加载配置")
        
        results = []
        task_count = 0
        total_tasks = len(self.config.platforms) * len(self.config.architectures) * len(self.config.formats)
        
        for platform in self.config.platforms:
            for architecture in self.config.architectures:
                for format in self.config.formats:
                    # 检查平台和格式兼容性
                    if not self._is_compatible(platform, format):
                        continue
                    
                    task_count += 1
                    self._notify_progress(task_count, total_tasks, 
                                        f"打包 {platform.value} {architecture.value} {format.value}")
                    
                    result = self.package_single(platform, architecture, format)
                    results.append(result)
        
        return results
    
    def package_single(self, platform: Platform, architecture: Architecture, 
                      format: PackageFormat) -> PackageResult:
        """打包单个平台"""
        start_time = time.time()
        
        try:
            # 创建临时构建目录
            build_dir = self._create_build_dir(platform, architecture, format)
            
            # 准备源文件
            self._prepare_source_files(build_dir)
            
            # 安装依赖
            self._install_dependencies(build_dir)
            
            # 执行平台特定的打包
            if platform == Platform.WINDOWS:
                output_path = self._package_windows(build_dir, architecture, format)
            elif platform == Platform.MACOS:
                output_path = self._package_macos(build_dir, architecture, format)
            elif platform == Platform.LINUX:
                output_path = self._package_linux(build_dir, architecture, format)
            elif platform == Platform.WEB:
                output_path = self._package_web(build_dir, format)
            elif platform == Platform.ANDROID:
                output_path = self._package_android(build_dir, format)
            elif platform == Platform.IOS:
                output_path = self._package_ios(build_dir, format)
            else:
                raise ValueError(f"不支持的平台: {platform}")
            
            # 后处理
            output_path = self._post_process(output_path, platform, format)
            
            # 计算文件大小
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            build_time = time.time() - start_time
            
            return PackageResult(
                platform=platform,
                architecture=architecture,
                format=format,
                success=True,
                output_path=output_path,
                file_size=file_size,
                build_time=build_time
            )
            
        except Exception as e:
            return PackageResult(
                platform=platform,
                architecture=architecture,
                format=format,
                success=False,
                output_path="",
                file_size=0,
                build_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _is_compatible(self, platform: Platform, format: PackageFormat) -> bool:
        """检查平台和格式兼容性"""
        compatibility = {
            Platform.WINDOWS: [PackageFormat.EXE, PackageFormat.MSI, PackageFormat.NSIS],
            Platform.MACOS: [PackageFormat.APP, PackageFormat.DMG, PackageFormat.PKG],
            Platform.LINUX: [PackageFormat.APPIMAGE, PackageFormat.DEB, PackageFormat.RPM, 
                            PackageFormat.SNAP, PackageFormat.FLATPAK, PackageFormat.TAR_GZ],
            Platform.WEB: [PackageFormat.PWA, PackageFormat.ELECTRON],
            Platform.ANDROID: [PackageFormat.APK, PackageFormat.AAB],
            Platform.IOS: [PackageFormat.IPA]
        }
        
        return format in compatibility.get(platform, [])
    
    def _create_build_dir(self, platform: Platform, architecture: Architecture, 
                         format: PackageFormat) -> str:
        """创建构建目录"""
        build_name = f"{platform.value}_{architecture.value}_{format.value}"
        build_dir = os.path.join(tempfile.gettempdir(), "packager", build_name)
        
        if os.path.exists(build_dir):
            shutil.rmtree(build_dir)
        
        os.makedirs(build_dir, exist_ok=True)
        return build_dir
    
    def _prepare_source_files(self, build_dir: str):
        """准备源文件"""
        # 复制主文件
        main_file_path = self.config.main_file
        if os.path.exists(main_file_path):
            shutil.copy2(main_file_path, build_dir)
        
        # 复制资源文件
        for resource in self.config.resources:
            if os.path.exists(resource):
                dest_path = os.path.join(build_dir, os.path.basename(resource))
                if os.path.isdir(resource):
                    shutil.copytree(resource, dest_path)
                else:
                    shutil.copy2(resource, dest_path)
        
        # 复制图标
        if self.config.icon_path and os.path.exists(self.config.icon_path):
            shutil.copy2(self.config.icon_path, build_dir)
    
    def _install_dependencies(self, build_dir: str):
        """安装依赖"""
        if not self.config.dependencies:
            return
        
        # 创建requirements.txt
        requirements_path = os.path.join(build_dir, "requirements.txt")
        with open(requirements_path, 'w') as f:
            for dep in self.config.dependencies:
                f.write(f"{dep}\n")
        
        # 安装依赖到构建目录
        subprocess.run([
            sys.executable, "-m", "pip", "install",
            "-r", requirements_path,
            "-t", build_dir
        ], check=True)
    
    def _package_windows(self, build_dir: str, architecture: Architecture, 
                        format: PackageFormat) -> str:
        """Windows平台打包"""
        if format == PackageFormat.EXE:
            return self._create_windows_exe(build_dir, architecture)
        elif format == PackageFormat.MSI:
            return self._create_windows_msi(build_dir, architecture)
        elif format == PackageFormat.NSIS:
            return self._create_windows_nsis(build_dir, architecture)
        else:
            raise ValueError(f"不支持的Windows格式: {format}")
    
    def _create_windows_exe(self, build_dir: str, architecture: Architecture) -> str:
        """创建Windows EXE"""
        if "pyinstaller" not in self.tools:
            raise RuntimeError("PyInstaller未安装")
        
        output_dir = os.path.join(self.config.output_dir, "windows")
        os.makedirs(output_dir, exist_ok=True)
        
        # PyInstaller命令
        cmd = [
            self.tools["pyinstaller"],
            "--onefile",
            "--windowed",
            "--distpath", output_dir,
            "--workpath", os.path.join(build_dir, "build"),
            "--specpath", build_dir
        ]
        
        # 添加图标
        if self.config.icon_path:
            cmd.extend(["--icon", self.config.icon_path])
        
        # 添加名称
        cmd.extend(["--name", self.config.name])
        
        # 添加主文件
        cmd.append(os.path.join(build_dir, os.path.basename(self.config.main_file)))
        
        # 执行打包
        subprocess.run(cmd, cwd=build_dir, check=True)
        
        exe_path = os.path.join(output_dir, f"{self.config.name}.exe")
        return exe_path
    
    def _create_windows_msi(self, build_dir: str, architecture: Architecture) -> str:
        """创建Windows MSI"""
        # 首先创建EXE
        exe_path = self._create_windows_exe(build_dir, architecture)
        
        # 使用WiX工具创建MSI（如果可用）
        if "candle" in self.tools and "light" in self.tools:
            return self._create_msi_with_wix(exe_path, build_dir)
        else:
            # 使用Python的bdist_msi
            return self._create_msi_with_python(build_dir)
    
    def _create_windows_nsis(self, build_dir: str, architecture: Architecture) -> str:
        """创建Windows NSIS安装程序"""
        if "makensis" not in self.tools:
            raise RuntimeError("NSIS未安装")
        
        # 首先创建EXE
        exe_path = self._create_windows_exe(build_dir, architecture)
        
        # 生成NSIS脚本
        nsis_script = self._generate_nsis_script(exe_path, build_dir)
        script_path = os.path.join(build_dir, "installer.nsi")
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(nsis_script)
        
        # 编译NSIS脚本
        subprocess.run([self.tools["makensis"], script_path], check=True)
        
        installer_path = os.path.join(build_dir, f"{self.config.name}_Setup.exe")
        return installer_path
    
    def _package_macos(self, build_dir: str, architecture: Architecture, 
                      format: PackageFormat) -> str:
        """macOS平台打包"""
        if format == PackageFormat.APP:
            return self._create_macos_app(build_dir, architecture)
        elif format == PackageFormat.DMG:
            return self._create_macos_dmg(build_dir, architecture)
        elif format == PackageFormat.PKG:
            return self._create_macos_pkg(build_dir, architecture)
        else:
            raise ValueError(f"不支持的macOS格式: {format}")
    
    def _create_macos_app(self, build_dir: str, architecture: Architecture) -> str:
        """创建macOS APP"""
        if "py2app" in self.tools:
            return self._create_app_with_py2app(build_dir, architecture)
        elif "pyinstaller" in self.tools:
            return self._create_app_with_pyinstaller(build_dir, architecture)
        else:
            raise RuntimeError("没有可用的macOS打包工具")
    
    def _create_app_with_pyinstaller(self, build_dir: str, architecture: Architecture) -> str:
        """使用PyInstaller创建macOS APP"""
        output_dir = os.path.join(self.config.output_dir, "macos")
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [
            self.tools["pyinstaller"],
            "--onedir",
            "--windowed",
            "--distpath", output_dir,
            "--workpath", os.path.join(build_dir, "build"),
            "--specpath", build_dir,
            "--name", self.config.name
        ]
        
        # 添加图标
        if self.config.icon_path:
            cmd.extend(["--icon", self.config.icon_path])
        
        # 添加主文件
        cmd.append(os.path.join(build_dir, os.path.basename(self.config.main_file)))
        
        subprocess.run(cmd, cwd=build_dir, check=True)
        
        app_path = os.path.join(output_dir, f"{self.config.name}.app")
        return app_path
    
    def _create_macos_dmg(self, build_dir: str, architecture: Architecture) -> str:
        """创建macOS DMG"""
        # 首先创建APP
        app_path = self._create_macos_app(build_dir, architecture)
        
        if "create-dmg" in self.tools:
            return self._create_dmg_with_tool(app_path)
        else:
            return self._create_dmg_with_hdiutil(app_path)
    
    def _package_linux(self, build_dir: str, architecture: Architecture, 
                      format: PackageFormat) -> str:
        """Linux平台打包"""
        if format == PackageFormat.APPIMAGE:
            return self._create_linux_appimage(build_dir, architecture)
        elif format == PackageFormat.DEB:
            return self._create_linux_deb(build_dir, architecture)
        elif format == PackageFormat.RPM:
            return self._create_linux_rpm(build_dir, architecture)
        elif format == PackageFormat.SNAP:
            return self._create_linux_snap(build_dir, architecture)
        elif format == PackageFormat.FLATPAK:
            return self._create_linux_flatpak(build_dir, architecture)
        elif format == PackageFormat.TAR_GZ:
            return self._create_linux_tarball(build_dir, architecture)
        else:
            raise ValueError(f"不支持的Linux格式: {format}")
    
    def _create_linux_appimage(self, build_dir: str, architecture: Architecture) -> str:
        """创建Linux AppImage"""
        if "appimagetool" not in self.tools:
            raise RuntimeError("appimagetool未安装")
        
        # 创建AppDir结构
        appdir = os.path.join(build_dir, f"{self.config.name}.AppDir")
        os.makedirs(appdir, exist_ok=True)
        
        # 复制应用文件
        app_files_dir = os.path.join(appdir, "usr", "bin")
        os.makedirs(app_files_dir, exist_ok=True)
        
        # 使用PyInstaller创建可执行文件
        if "pyinstaller" in self.tools:
            cmd = [
                self.tools["pyinstaller"],
                "--onefile",
                "--distpath", app_files_dir,
                "--name", self.config.name,
                os.path.join(build_dir, os.path.basename(self.config.main_file))
            ]
            subprocess.run(cmd, cwd=build_dir, check=True)
        
        # 创建desktop文件
        desktop_content = self._generate_desktop_file()
        with open(os.path.join(appdir, f"{self.config.name}.desktop"), 'w') as f:
            f.write(desktop_content)
        
        # 复制图标
        if self.config.icon_path:
            shutil.copy2(self.config.icon_path, 
                        os.path.join(appdir, f"{self.config.name}.png"))
        
        # 创建AppImage
        output_path = os.path.join(self.config.output_dir, "linux", f"{self.config.name}.AppImage")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        subprocess.run([self.tools["appimagetool"], appdir, output_path], check=True)
        
        return output_path
    
    def _create_linux_deb(self, build_dir: str, architecture: Architecture) -> str:
        """创建Debian包"""
        if "dpkg-deb" not in self.tools:
            raise RuntimeError("dpkg-deb未安装")
        
        # 创建DEB包结构
        deb_dir = os.path.join(build_dir, "deb")
        os.makedirs(deb_dir, exist_ok=True)
        
        # DEBIAN目录
        debian_dir = os.path.join(deb_dir, "DEBIAN")
        os.makedirs(debian_dir, exist_ok=True)
        
        # 创建control文件
        control_content = self._generate_deb_control(architecture)
        with open(os.path.join(debian_dir, "control"), 'w') as f:
            f.write(control_content)
        
        # 复制应用文件
        app_dir = os.path.join(deb_dir, "usr", "bin")
        os.makedirs(app_dir, exist_ok=True)
        
        # 使用PyInstaller创建可执行文件
        if "pyinstaller" in self.tools:
            cmd = [
                self.tools["pyinstaller"],
                "--onefile",
                "--distpath", app_dir,
                "--name", self.config.name,
                os.path.join(build_dir, os.path.basename(self.config.main_file))
            ]
            subprocess.run(cmd, cwd=build_dir, check=True)
        
        # 创建DEB包
        output_path = os.path.join(self.config.output_dir, "linux", 
                                  f"{self.config.name}_{self.config.version}_{architecture.value}.deb")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        subprocess.run([self.tools["dpkg-deb"], "--build", deb_dir, output_path], check=True)
        
        return output_path
    
    def _package_web(self, build_dir: str, format: PackageFormat) -> str:
        """Web平台打包"""
        if format == PackageFormat.PWA:
            return self._create_pwa(build_dir)
        elif format == PackageFormat.ELECTRON:
            return self._create_electron_app(build_dir)
        else:
            raise ValueError(f"不支持的Web格式: {format}")
    
    def _package_android(self, build_dir: str, format: PackageFormat) -> str:
        """Android平台打包"""
        if format == PackageFormat.APK:
            return self._create_android_apk(build_dir)
        elif format == PackageFormat.AAB:
            return self._create_android_aab(build_dir)
        else:
            raise ValueError(f"不支持的Android格式: {format}")
    
    def _package_ios(self, build_dir: str, format: PackageFormat) -> str:
        """iOS平台打包"""
        if format == PackageFormat.IPA:
            return self._create_ios_ipa(build_dir)
        else:
            raise ValueError(f"不支持的iOS格式: {format}")
    
    def _post_process(self, output_path: str, platform: Platform, format: PackageFormat) -> str:
        """后处理"""
        # 代码签名
        if self.config.signing.get("enabled", False):
            self._sign_package(output_path, platform)
        
        # 压缩
        if self.config.optimization.get("compress", False):
            output_path = self._compress_package(output_path)
        
        # 生成校验和
        self._generate_checksum(output_path)
        
        return output_path
    
    def _sign_package(self, package_path: str, platform: Platform):
        """签名包"""
        if platform == Platform.WINDOWS and "signtool" in self.tools:
            self._sign_windows_package(package_path)
        elif platform == Platform.MACOS and "codesign" in self.tools:
            self._sign_macos_package(package_path)
    
    def _sign_windows_package(self, package_path: str):
        """签名Windows包"""
        cert_path = self.config.signing.get("certificate_path")
        cert_password = self.config.signing.get("certificate_password")
        timestamp_server = self.config.signing.get("timestamp_server", 
                                                   "http://timestamp.digicert.com")
        
        if cert_path and os.path.exists(cert_path):
            cmd = [
                self.tools["signtool"], "sign",
                "/f", cert_path,
                "/p", cert_password,
                "/t", timestamp_server,
                package_path
            ]
            subprocess.run(cmd, check=True)
    
    def _sign_macos_package(self, package_path: str):
        """签名macOS包"""
        developer_id = self.config.signing.get("developer_id")
        
        if developer_id:
            cmd = [
                self.tools["codesign"],
                "--force",
                "--verify",
                "--verbose",
                "--sign", developer_id,
                package_path
            ]
            subprocess.run(cmd, check=True)
    
    def _compress_package(self, package_path: str) -> str:
        """压缩包"""
        if package_path.endswith(('.exe', '.app', '.AppImage')):
            # 创建ZIP压缩包
            zip_path = package_path + ".zip"
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                if os.path.isdir(package_path):
                    for root, dirs, files in os.walk(package_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, os.path.dirname(package_path))
                            zipf.write(file_path, arcname)
                else:
                    zipf.write(package_path, os.path.basename(package_path))
            
            return zip_path
        
        return package_path
    
    def _generate_checksum(self, package_path: str):
        """生成校验和"""
        hash_md5 = hashlib.md5()
        hash_sha256 = hashlib.sha256()
        
        with open(package_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
                hash_sha256.update(chunk)
        
        checksum_path = package_path + ".checksums"
        with open(checksum_path, 'w') as f:
            f.write(f"MD5: {hash_md5.hexdigest()}\n")
            f.write(f"SHA256: {hash_sha256.hexdigest()}\n")
    
    def _generate_nsis_script(self, exe_path: str, build_dir: str) -> str:
        """生成NSIS脚本"""
        return f"""
!define APPNAME "{self.config.name}"
!define VERSION "{self.config.version}"
!define DESCRIPTION "{self.config.description}"
!define PUBLISHER "{self.config.author}"

Name "${{APPNAME}}"
OutFile "{self.config.name}_Setup.exe"
InstallDir "$PROGRAMFILES\\${{APPNAME}}"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Install"
    SetOutPath $INSTDIR
    File "{exe_path}"
    
    CreateDirectory "$SMPROGRAMS\\${{APPNAME}}"
    CreateShortCut "$SMPROGRAMS\\${{APPNAME}}\\${{APPNAME}}.lnk" "$INSTDIR\\{os.path.basename(exe_path)}"
    CreateShortCut "$DESKTOP\\${{APPNAME}}.lnk" "$INSTDIR\\{os.path.basename(exe_path)}"
    
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "DisplayName" "${{APPNAME}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "UninstallString" "$INSTDIR\\uninstall.exe"
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\{os.path.basename(exe_path)}"
    Delete "$INSTDIR\\uninstall.exe"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\${{APPNAME}}\\${{APPNAME}}.lnk"
    RMDir "$SMPROGRAMS\\${{APPNAME}}"
    Delete "$DESKTOP\\${{APPNAME}}.lnk"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}"
SectionEnd
"""
    
    def _generate_desktop_file(self) -> str:
        """生成Linux desktop文件"""
        return f"""[Desktop Entry]
Type=Application
Name={self.config.name}
Comment={self.config.description}
Exec={self.config.name}
Icon={self.config.name}
Categories=Development;
Keywords={self.config.name};
StartupNotify=true
"""
    
    def _generate_deb_control(self, architecture: Architecture) -> str:
        """生成DEB control文件"""
        arch_map = {
            Architecture.X86: "i386",
            Architecture.X64: "amd64",
            Architecture.ARM: "armhf",
            Architecture.ARM64: "arm64"
        }
        
        return f"""Package: {self.config.name.lower()}
Version: {self.config.version}
Section: devel
Priority: optional
Architecture: {arch_map.get(architecture, "amd64")}
Maintainer: {self.config.author}
Description: {self.config.description}
"""
    
    def generate_build_report(self, results: List[PackageResult]) -> str:
        """生成构建报告"""
        report = []
        report.append("# 多平台打包报告\n")
        report.append(f"应用名称: {self.config.name}")
        report.append(f"版本: {self.config.version}")
        report.append(f"构建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        successful_builds = [r for r in results if r.success]
        failed_builds = [r for r in results if not r.success]
        
        report.append(f"## 构建统计")
        report.append(f"- 总计: {len(results)}")
        report.append(f"- 成功: {len(successful_builds)}")
        report.append(f"- 失败: {len(failed_builds)}\n")
        
        if successful_builds:
            report.append("## 成功构建")
            for result in successful_builds:
                report.append(f"### ✅ {result.platform.value} {result.architecture.value} {result.format.value}")
                report.append(f"- 输出路径: {result.output_path}")
                report.append(f"- 文件大小: {result.file_size / 1024 / 1024:.1f} MB")
                report.append(f"- 构建时间: {result.build_time:.1f} 秒")
                if result.warnings:
                    report.append(f"- 警告: {', '.join(result.warnings)}")
                report.append("")
        
        if failed_builds:
            report.append("## 失败构建")
            for result in failed_builds:
                report.append(f"### ❌ {result.platform.value} {result.architecture.value} {result.format.value}")
                report.append(f"- 错误: {result.error_message}")
                report.append(f"- 构建时间: {result.build_time:.1f} 秒")
                report.append("")
        
        return "\n".join(report)
    
    def clean_build_cache(self):
        """清理构建缓存"""
        cache_dir = os.path.join(tempfile.gettempdir(), "packager")
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
        
        self.build_cache.clear()


# 使用示例
def main():
    """使用示例"""
    packager = MultiPlatformPackager()
    
    # 创建默认配置
    config = packager.create_default_config(".")
    config.name = "AI Code Generator"
    config.version = "1.0.0"
    config.description = "AI-powered code generation tool"
    config.author = "AI Code Team"
    config.main_file = "main.py"
    config.dependencies = ["PyQt5", "requests", "openai"]
    config.resources = ["ui/", "ai/", "tools/"]
    
    # 保存配置
    packager.save_config("package_config.json")
    
    # 添加进度回调
    def progress_callback(current, total, progress, message):
        print(f"进度: {progress:.1f}% - {message}")
    
    packager.add_progress_callback(progress_callback)
    
    # 执行打包
    print("🚀 开始多平台打包...")
    results = packager.package_all()
    
    # 生成报告
    report = packager.generate_build_report(results)
    print("\n" + report)
    
    # 保存报告
    with open("build_report.md", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("📊 构建报告已保存到 build_report.md")


if __name__ == "__main__":
    main()